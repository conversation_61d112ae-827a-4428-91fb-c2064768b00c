#!/bin/bash

# Migration script to reorganize codebase from generic structure to module-based architecture
# This script moves files from current structure to the new module-based structure

echo "🔄 Starting migration to module-based architecture..."

# Create new directory structure
echo "📁 Creating new directory structure..."

# Shared kernel directories
mkdir -p src/shared/domain/entities
mkdir -p src/shared/domain/value-objects
mkdir -p src/shared/domain/repositories
mkdir -p src/shared/domain/services
mkdir -p src/shared/application/use-cases
mkdir -p src/shared/application/dtos
mkdir -p src/shared/application/interfaces
mkdir -p src/shared/infrastructure/adapters/odoo
mkdir -p src/shared/infrastructure/auth
mkdir -p src/shared/infrastructure/database
mkdir -p src/shared/infrastructure/config

# Gateway presentation layer
mkdir -p src/presentation/controllers/gateway
mkdir -p src/presentation/controllers/modules

# Module directories (example: CRM)
mkdir -p src/modules/crm/domain/entities
mkdir -p src/modules/crm/domain/value-objects
mkdir -p src/modules/crm/domain/repositories
mkdir -p src/modules/crm/domain/services
mkdir -p src/modules/crm/domain/events
mkdir -p src/modules/crm/application/use-cases
mkdir -p src/modules/crm/application/dtos
mkdir -p src/modules/crm/application/queries
mkdir -p src/modules/crm/application/handlers
mkdir -p src/modules/crm/infrastructure/adapters
mkdir -p src/modules/crm/infrastructure/repositories
mkdir -p src/modules/crm/infrastructure/mappers
mkdir -p src/modules/crm/infrastructure/external-services
mkdir -p src/modules/crm/presentation/controllers
mkdir -p src/modules/crm/presentation/dtos
mkdir -p src/modules/crm/presentation/validators

echo "✅ Directory structure created"

# Phase 1: Move shared domain logic
echo "🔄 Phase 1: Moving shared domain logic..."

# Move domain value objects (shared across modules)
if [ -f "src/domain/value-objects/odoo-connection-config.ts" ]; then
    mv src/domain/value-objects/odoo-connection-config.ts src/shared/domain/value-objects/
    echo "  ✅ Moved odoo-connection-config.ts to shared"
fi

# Move domain repositories (shared interfaces)
if [ -f "src/domain/repositories/odoo-adapter.interface.ts" ]; then
    mv src/domain/repositories/odoo-adapter.interface.ts src/shared/domain/repositories/
    echo "  ✅ Moved odoo-adapter.interface.ts to shared"
fi

# Move domain entities (base entities)
if [ -f "src/domain/entities/odoo-record.ts" ]; then
    mv src/domain/entities/odoo-record.ts src/shared/domain/entities/
    echo "  ✅ Moved odoo-record.ts to shared"
fi

# Phase 2: Move shared application logic
echo "🔄 Phase 2: Moving shared application logic..."

# Move generic use cases
if [ -f "src/application/use-cases/odoo-connection.use-case.ts" ]; then
    mv src/application/use-cases/odoo-connection.use-case.ts src/shared/application/use-cases/
    echo "  ✅ Moved odoo-connection.use-case.ts to shared"
fi

# Move generic DTOs
if [ -d "src/application/dtos" ]; then
    cp -r src/application/dtos/* src/shared/application/dtos/ 2>/dev/null || true
    echo "  ✅ Copied DTOs to shared"
fi

# Move application interfaces
if [ -d "src/application/interfaces" ]; then
    cp -r src/application/interfaces/* src/shared/application/interfaces/ 2>/dev/null || true
    echo "  ✅ Copied interfaces to shared"
fi

# Phase 3: Move shared infrastructure
echo "🔄 Phase 3: Moving shared infrastructure..."

# Move Odoo adapters (core adapters used by all modules)
if [ -d "src/infrastructure/adapters/odoo" ]; then
    cp -r src/infrastructure/adapters/odoo/* src/shared/infrastructure/adapters/odoo/ 2>/dev/null || true
    echo "  ✅ Copied Odoo adapters to shared"
fi

# Move auth (shared across all modules)
if [ -d "src/infrastructure/auth" ]; then
    cp -r src/infrastructure/auth/* src/shared/infrastructure/auth/ 2>/dev/null || true
    echo "  ✅ Copied auth to shared"
fi

# Move database (shared database logic)
if [ -d "src/infrastructure/database" ]; then
    cp -r src/infrastructure/database/* src/shared/infrastructure/database/ 2>/dev/null || true
    echo "  ✅ Copied database to shared"
fi

# Move config (shared configuration)
if [ -d "src/infrastructure/config" ]; then
    cp -r src/infrastructure/config/* src/shared/infrastructure/config/ 2>/dev/null || true
    echo "  ✅ Copied config to shared"
fi

# Phase 4: Reorganize presentation layer
echo "🔄 Phase 4: Reorganizing presentation layer..."

# Move generic controllers to gateway
if [ -f "src/presentation/controllers/v1/odoo-v1.controller.ts" ]; then
    mv src/presentation/controllers/v1/odoo-v1.controller.ts src/presentation/controllers/gateway/
    echo "  ✅ Moved odoo-v1.controller.ts to gateway"
fi

if [ -f "src/presentation/controllers/api-version.controller.ts" ]; then
    mv src/presentation/controllers/api-version.controller.ts src/presentation/controllers/gateway/
    echo "  ✅ Moved api-version.controller.ts to gateway"
fi

# Phase 5: Create shared module
echo "🔄 Phase 5: Creating shared module..."

cat > src/shared/shared.module.ts << 'EOF'
import { Module, Global } from '@nestjs/common';
import { DatabaseModule } from './infrastructure/database/database.module';
import { AuthModule } from './infrastructure/auth/auth.module';
import { OdooConnectionUseCase } from './application/use-cases/odoo-connection.use-case';
import { UniversalOdooAdapter } from './infrastructure/adapters/odoo/universal-odoo-adapter';
import { OdooConnectionPoolService } from './infrastructure/adapters/odoo/odoo-connection-pool.service';
import { UserContextService } from './infrastructure/adapters/odoo/user-context.service';
import { UserOdooMappingService } from './infrastructure/adapters/odoo/user-odoo-mapping.service';

/**
 * Shared Module - Contains common functionality used across all modules
 * This is the "Shared Kernel" in DDD terminology
 */
@Global()
@Module({
  imports: [
    DatabaseModule,
    AuthModule,
  ],
  providers: [
    // Core Odoo services
    OdooConnectionUseCase,
    UniversalOdooAdapter,
    OdooConnectionPoolService,
    UserContextService,
    UserOdooMappingService,
  ],
  exports: [
    // Export for use in other modules
    DatabaseModule,
    AuthModule,
    OdooConnectionUseCase,
    UniversalOdooAdapter,
    OdooConnectionPoolService,
    UserContextService,
    UserOdooMappingService,
  ],
})
export class SharedModule {}
EOF

echo "  ✅ Created shared.module.ts"

# Phase 6: Update import paths (create update script)
echo "🔄 Phase 6: Creating import path update script..."

cat > scripts/update-imports.js << 'EOF'
const fs = require('fs');
const path = require('path');

// Mapping of old paths to new paths
const pathMappings = {
  '../../../domain/': '../../../shared/domain/',
  '../../../application/': '../../../shared/application/',
  '../../domain/': '../../shared/domain/',
  '../../application/': '../../shared/application/',
  '../domain/': '../shared/domain/',
  '../application/': '../shared/application/',
};

function updateImportsInFile(filePath) {
  if (!fs.existsSync(filePath)) return;
  
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  for (const [oldPath, newPath] of Object.entries(pathMappings)) {
    if (content.includes(oldPath)) {
      content = content.replace(new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newPath);
      updated = true;
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Updated imports in ${filePath}`);
  }
}

function updateImportsInDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) return;
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      updateImportsInDirectory(fullPath);
    } else if (item.endsWith('.ts') || item.endsWith('.js')) {
      updateImportsInFile(fullPath);
    }
  }
}

console.log('🔄 Updating import paths...');
updateImportsInDirectory('src');
console.log('✅ Import paths updated');
EOF

echo "  ✅ Created import update script"

# Phase 7: Create module template
echo "🔄 Phase 7: Creating module template..."

cat > scripts/create-module.sh << 'EOF'
#!/bin/bash

# Script to create a new Odoo module with proper structure
# Usage: ./create-module.sh <module-name>

if [ -z "$1" ]; then
    echo "Usage: ./create-module.sh <module-name>"
    echo "Example: ./create-module.sh sales"
    exit 1
fi

MODULE_NAME=$1
MODULE_DIR="src/modules/$MODULE_NAME"

echo "🔄 Creating module: $MODULE_NAME"

# Create directory structure
mkdir -p $MODULE_DIR/domain/entities
mkdir -p $MODULE_DIR/domain/value-objects
mkdir -p $MODULE_DIR/domain/repositories
mkdir -p $MODULE_DIR/domain/services
mkdir -p $MODULE_DIR/domain/events
mkdir -p $MODULE_DIR/application/use-cases
mkdir -p $MODULE_DIR/application/dtos
mkdir -p $MODULE_DIR/application/queries
mkdir -p $MODULE_DIR/application/handlers
mkdir -p $MODULE_DIR/infrastructure/adapters
mkdir -p $MODULE_DIR/infrastructure/repositories
mkdir -p $MODULE_DIR/infrastructure/mappers
mkdir -p $MODULE_DIR/infrastructure/external-services
mkdir -p $MODULE_DIR/presentation/controllers
mkdir -p $MODULE_DIR/presentation/dtos
mkdir -p $MODULE_DIR/presentation/validators

# Create module file
cat > $MODULE_DIR/${MODULE_NAME}.module.ts << EOL
import { Module } from '@nestjs/common';
import { SharedModule } from '../../shared/shared.module';

@Module({
  imports: [SharedModule],
  providers: [
    // Add your providers here
  ],
  controllers: [
    // Add your controllers here
  ],
  exports: [
    // Add your exports here
  ],
})
export class ${MODULE_NAME^}Module {}
EOL

echo "✅ Module $MODULE_NAME created successfully"
echo "📁 Structure created in: $MODULE_DIR"
EOF

chmod +x scripts/create-module.sh
echo "  ✅ Created module template script"

# Phase 8: Clean up old directories (optional - commented out for safety)
echo "🔄 Phase 8: Cleanup (manual step)..."
echo "  ⚠️  Manual cleanup required:"
echo "     - Review moved files in shared/"
echo "     - Update import statements"
echo "     - Remove old empty directories"
echo "     - Update module imports in app.module.ts"

echo ""
echo "🎉 Migration completed!"
echo ""
echo "📋 Next steps:"
echo "1. Run: node scripts/update-imports.js"
echo "2. Update app.module.ts to import SharedModule"
echo "3. Create your first module: ./scripts/create-module.sh crm"
echo "4. Test the application"
echo "5. Remove old empty directories after verification"
echo ""
echo "📁 New structure:"
echo "   src/shared/          - Shared kernel (common code)"
echo "   src/modules/         - Domain modules (CRM, Sales, etc.)"
echo "   src/infrastructure/  - Global infrastructure"
echo "   src/presentation/    - API gateway"
echo "   src/common/          - Cross-cutting concerns"
EOF
