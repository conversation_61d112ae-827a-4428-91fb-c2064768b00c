/**
 * MongoDB Initialization Script
 * This script runs when MongoDB container starts for the first time
 */

// Switch to the application database
db = db.getSiblingDB('universal-odoo-adapter');

// Create application user
db.createUser({
  user: 'app_user',
  pwd: 'app_password',
  roles: [
    {
      role: 'readWrite',
      db: 'universal-odoo-adapter'
    }
  ]
});

// Create collections with validation
db.createCollection('user_odoo_mappings', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['webUserId', 'odooInstanceId', 'odooHost', 'odooDatabase', 'odooUsername', 'odooPasswordEncrypted'],
      properties: {
        webUserId: {
          bsonType: 'string',
          description: 'Web user ID is required and must be a string'
        },
        odooInstanceId: {
          bsonType: 'string',
          description: 'Odoo instance ID is required and must be a string'
        },
        odooHost: {
          bsonType: 'string',
          description: 'Odoo host is required and must be a string'
        },
        odooDatabase: {
          bsonType: 'string',
          description: 'Odoo database is required and must be a string'
        },
        odooUsername: {
          bsonType: 'string',
          description: 'Odoo username is required and must be a string'
        },
        odooPasswordEncrypted: {
          bsonType: 'string',
          description: 'Encrypted Odoo password is required and must be a string'
        },
        odooProtocol: {
          bsonType: 'string',
          enum: ['http', 'https'],
          description: 'Protocol must be http or https'
        },
        odooPort: {
          bsonType: 'int',
          minimum: 1,
          maximum: 65535,
          description: 'Port must be a valid port number'
        },
        isActive: {
          bsonType: 'bool',
          description: 'isActive must be a boolean'
        },
        lastUsed: {
          bsonType: 'date',
          description: 'lastUsed must be a date'
        },
        totalUsageCount: {
          bsonType: 'int',
          minimum: 0,
          description: 'totalUsageCount must be a non-negative integer'
        },
        connectionAttempts: {
          bsonType: 'int',
          minimum: 0,
          description: 'connectionAttempts must be a non-negative integer'
        }
      }
    }
  }
});

print('✅ Database and collections created successfully');
print('✅ Application user created');
print('🎉 MongoDB initialization completed!');
