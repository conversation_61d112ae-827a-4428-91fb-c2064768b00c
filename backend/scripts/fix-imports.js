const fs = require('fs');
const path = require('path');

// Mapping of old paths to new paths
const pathMappings = {
  // Domain imports
  '../../../domain/value-objects/odoo-connection-config': '../../../shared/domain/value-objects/odoo-connection-config',
  '../../../../domain/value-objects/odoo-connection-config': '../../../../shared/domain/value-objects/odoo-connection-config',
  '../../../domain/repositories/odoo-adapter.interface': '../../../shared/domain/repositories/odoo-adapter.interface',
  '../../../../domain/repositories/odoo-adapter.interface': '../../../../shared/domain/repositories/odoo-adapter.interface',
  '../../../domain/entities/odoo-record': '../../../shared/domain/entities/odoo-record',
  '../../../../domain/entities/odoo-record': '../../../../shared/domain/entities/odoo-record',
  
  // Application imports
  '../../../application/use-cases/odoo-connection.use-case': '../../../shared/application/use-cases/odoo-connection.use-case',
  '../../application/use-cases/odoo-connection.use-case': '../../shared/application/use-cases/odoo-connection.use-case',
  '../../../application/dtos/odoo-connection.dto': '../../../shared/application/dtos/odoo-connection.dto',
  '../../application/dtos/odoo-connection.dto': '../../shared/application/dtos/odoo-connection.dto',
  
  // Database imports
  '../../database/schemas/user-odoo-mapping.schema': '../../../shared/infrastructure/database/schemas/user-odoo-mapping.schema',
  '../../../database/schemas/user-odoo-mapping.schema': '../../../shared/infrastructure/database/schemas/user-odoo-mapping.schema',
};

function updateImportsInFile(filePath) {
  if (!fs.existsSync(filePath)) return;
  
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  for (const [oldPath, newPath] of Object.entries(pathMappings)) {
    if (content.includes(oldPath)) {
      content = content.replace(new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newPath);
      updated = true;
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ Updated imports in ${filePath}`);
  }
}

function updateImportsInDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) return;
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      updateImportsInDirectory(fullPath);
    } else if (item.endsWith('.ts') || item.endsWith('.js')) {
      updateImportsInFile(fullPath);
    }
  }
}

console.log('🔄 Fixing import paths...');
updateImportsInDirectory('src');
console.log('✅ Import paths fixed');
