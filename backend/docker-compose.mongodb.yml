version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: universal-odoo-adapter-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: universal-odoo-adapter
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - universal-odoo-adapter

  mongo-express:
    image: mongo-express:1.0.2
    container_name: universal-odoo-adapter-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - universal-odoo-adapter

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

networks:
  universal-odoo-adapter:
    driver: bridge
