# 🔄 Migration Guide: Generic → Module-Based Architecture

## Overview

This guide explains how to migrate from the current generic Odoo adapter architecture to a module-based Domain-Driven Design (DDD) architecture.

## 🎯 Migration Goals

- **Modularize** codebase by Odoo business domains (CRM, Sales, Inventory, etc.)
- **Maintain** existing functionality during migration
- **Improve** code organization and maintainability
- **Enable** independent module development

## 📋 Current vs Target Structure

### Current Structure
```
src/
├── application/           # Generic application logic
├── domain/               # Generic domain logic  
├── infrastructure/       # All infrastructure
├── presentation/         # All controllers
└── common/              # Utilities
```

### Target Structure
```
src/
├── shared/              # 🔄 Shared Kernel
│   ├── domain/         # Common domain logic
│   ├── application/    # Common use cases
│   └── infrastructure/ # Shared infrastructure
├── modules/            # 📦 Bounded Contexts
│   ├── crm/           # CRM domain module
│   ├── sales/         # Sales domain module
│   └── inventory/     # Inventory domain module
├── infrastructure/    # 🔧 Global infrastructure
├── presentation/      # 🌐 API Gateway
└── common/           # 📦 Cross-cutting concerns
```

## 🚀 Migration Steps

### Step 1: Run Migration Script

```bash
# Make script executable
chmod +x scripts/migrate-to-modules.sh

# Run migration
./scripts/migrate-to-modules.sh
```

### Step 2: Update Import Paths

```bash
# Update all import statements
node scripts/update-imports.js
```

### Step 3: Update App Module

Update `src/app.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';

// Import shared module instead of individual modules
import { SharedModule } from './shared/shared.module';
import { CommonModule } from './common/common.module';

// Import domain modules
import { CrmModule } from './modules/crm/crm.module';
// import { SalesModule } from './modules/sales/sales.module';
// import { InventoryModule } from './modules/inventory/inventory.module';

// Global infrastructure
import { GlobalExceptionFilter } from './infrastructure/filters/global-exception.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    SharedModule,      // Shared kernel
    CommonModule,      // Cross-cutting concerns
    CrmModule,         // Domain modules
    // SalesModule,
    // InventoryModule,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
```

### Step 4: Create Your First Module

```bash
# Create CRM module
./scripts/create-module.sh crm

# Create Sales module  
./scripts/create-module.sh sales

# Create Inventory module
./scripts/create-module.sh inventory
```

### Step 5: Implement Module Logic

For each module, implement the layers:

#### Domain Layer Example (CRM)
```typescript
// src/modules/crm/domain/entities/lead.entity.ts
export class Lead extends OdooBaseModel {
  calculateScore(): number { /* business logic */ }
  canConvertToOpportunity(): boolean { /* business rules */ }
}

// src/modules/crm/domain/value-objects/lead-status.vo.ts
export class LeadStatus {
  isQualified(): boolean { /* validation logic */ }
}
```

#### Application Layer Example (CRM)
```typescript
// src/modules/crm/application/use-cases/create-lead.use-case.ts
@Injectable()
export class CreateLeadUseCase {
  async execute(request: CreateLeadRequest): Promise<CreateLeadResponse> {
    // 1. Validate business rules
    // 2. Create domain entity
    // 3. Save via repository
    // 4. Publish events
  }
}
```

#### Infrastructure Layer Example (CRM)
```typescript
// src/modules/crm/infrastructure/adapters/odoo-crm.adapter.ts
@Injectable()
export class OdooCrmAdapter {
  async createLead(lead: Lead): Promise<number> {
    // Map domain entity to Odoo model
    // Call shared Odoo adapter
  }
}
```

#### Presentation Layer Example (CRM)
```typescript
// src/modules/crm/presentation/controllers/leads.controller.ts
@Controller('crm/leads')
export class LeadsController {
  @Post()
  async createLead(@Body() dto: CreateLeadDto) {
    return await this.createLeadUseCase.execute(dto);
  }
}
```

## 📁 File Movement Reference

### What Goes to `shared/`

**Domain Logic (Shared Kernel):**
- `src/domain/value-objects/odoo-connection-config.ts` → `src/shared/domain/value-objects/`
- `src/domain/repositories/odoo-adapter.interface.ts` → `src/shared/domain/repositories/`
- `src/domain/entities/odoo-record.ts` → `src/shared/domain/entities/`

**Application Logic (Common Use Cases):**
- `src/application/use-cases/odoo-connection.use-case.ts` → `src/shared/application/use-cases/`
- `src/application/dtos/odoo-connection.dto.ts` → `src/shared/application/dtos/`

**Infrastructure (Shared Services):**
- `src/infrastructure/adapters/odoo/` → `src/shared/infrastructure/adapters/odoo/`
- `src/infrastructure/auth/` → `src/shared/infrastructure/auth/`
- `src/infrastructure/database/` → `src/shared/infrastructure/database/`

### What Stays in `infrastructure/`

**Global Infrastructure:**
- `src/infrastructure/filters/` (Global exception handling)
- `src/infrastructure/odoo.module.ts` (Global Odoo module)

### What Goes to `presentation/controllers/gateway/`

**Generic Controllers:**
- `src/presentation/controllers/v1/odoo-v1.controller.ts` → `src/presentation/controllers/gateway/`
- `src/presentation/controllers/api-version.controller.ts` → `src/presentation/controllers/gateway/`

### What Stays in `common/`

**Cross-cutting Concerns:**
- `src/common/` (No changes - global utilities)

## 🔧 Module Development Workflow

### 1. Identify Domain Boundaries
- **CRM**: Leads, Opportunities, Customers
- **Sales**: Orders, Quotations, Invoicing  
- **Inventory**: Products, Stock, Movements
- **Accounting**: Invoices, Payments, Reports

### 2. Create Module Structure
```bash
./scripts/create-module.sh <module-name>
```

### 3. Implement Domain Layer
- Entities with business logic
- Value objects with validation
- Domain services
- Repository interfaces

### 4. Implement Application Layer
- Use cases (business workflows)
- DTOs (data transfer objects)
- Query handlers
- Event handlers

### 5. Implement Infrastructure Layer
- Odoo adapters (module-specific)
- Repository implementations
- External service integrations
- Data mappers

### 6. Implement Presentation Layer
- Controllers (REST endpoints)
- Request/Response DTOs
- Validation logic
- Swagger documentation

## 🧪 Testing Strategy

### Unit Tests
```typescript
// Test domain logic
describe('Lead Entity', () => {
  it('should calculate score correctly', () => {
    const lead = new Lead(/* ... */);
    expect(lead.calculateScore()).toBe(85);
  });
});

// Test use cases
describe('CreateLeadUseCase', () => {
  it('should create lead with valid data', async () => {
    const result = await useCase.execute(validRequest);
    expect(result.lead.id).toBeDefined();
  });
});
```

### Integration Tests
```typescript
// Test API endpoints
describe('LeadsController', () => {
  it('should create lead via API', async () => {
    const response = await request(app)
      .post('/api/v1/crm/leads')
      .send(createLeadDto)
      .expect(201);
  });
});
```

## 🚨 Common Issues & Solutions

### Import Path Issues
**Problem:** Import statements not updated
**Solution:** Run `node scripts/update-imports.js`

### Module Not Found
**Problem:** Module imports not updated in app.module.ts
**Solution:** Import SharedModule and domain modules

### Circular Dependencies
**Problem:** Modules importing each other
**Solution:** Use shared kernel or domain events

### Database Connection Issues
**Problem:** Database services not available
**Solution:** Ensure SharedModule is imported

## 📊 Benefits After Migration

### 1. **Modularity**
- Independent module development
- Clear domain boundaries
- Easier testing and maintenance

### 2. **Scalability**
- Easy to add new Odoo modules
- Microservices-ready architecture
- Team-based development

### 3. **Maintainability**
- Rich domain models with business logic
- Clear separation of concerns
- Better code organization

### 4. **Flexibility**
- Easy to modify individual modules
- Plugin-like architecture
- Version-specific implementations

## 🎯 Next Steps

1. **Complete Migration:** Follow all steps in this guide
2. **Implement CRM Module:** Start with leads and opportunities
3. **Add Sales Module:** Orders and quotations
4. **Extend with More Modules:** Inventory, accounting, HR
5. **Add Cross-Module Integration:** Events and workflows
6. **Performance Optimization:** Caching and connection pooling
7. **Documentation:** API docs and developer guides

## 📚 Additional Resources

- [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [NestJS Modules](https://docs.nestjs.com/modules)
- [CQRS Pattern](https://docs.nestjs.com/recipes/cqrs)

---

**Happy coding! 🚀**
