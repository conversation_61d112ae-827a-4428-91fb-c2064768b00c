import { OdooBaseModel } from '../../../../shared/domain/entities/odoo-base.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { ContactInfo } from '../value-objects/contact-info.vo';

/**
 * Lead Domain Entity
 * Represents a potential customer in the CRM system
 */
export class Lead extends OdooBaseModel {
  constructor(
    id: number,
    public readonly name: string,
    public readonly contactInfo: ContactInfo,
    public readonly status: LeadStatus,
    public readonly source: string,
    public readonly expectedRevenue?: number,
    public readonly probability?: number,
    public readonly description?: string,
    public readonly assignedUserId?: number,
    public readonly companyId?: number,
    public readonly tags: string[] = [],
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    super(id, createdAt, updatedAt);
  }

  /**
   * Business logic: Check if lead is qualified
   */
  isQualified(): boolean {
    return this.status.isQualified() && 
           this.contactInfo.isComplete() && 
           this.expectedRevenue !== undefined &&
           this.expectedRevenue > 0;
  }

  /**
   * Business logic: Check if lead is ready for conversion
   */
  canConvertToOpportunity(): boolean {
    return this.isQualified() && 
           this.status.canConvert() &&
           this.assignedUserId !== undefined;
  }

  /**
   * Business logic: Calculate lead score
   */
  calculateScore(): number {
    let score = 0;
    
    // Base score from status
    score += this.status.getScoreWeight();
    
    // Revenue potential
    if (this.expectedRevenue) {
      score += Math.min(this.expectedRevenue / 1000, 50); // Max 50 points
    }
    
    // Probability factor
    if (this.probability) {
      score += this.probability * 0.3; // Max 30 points
    }
    
    // Contact completeness
    score += this.contactInfo.getCompletenessScore();
    
    // Source quality
    score += this.getSourceScore();
    
    return Math.min(Math.round(score), 100);
  }

  /**
   * Business logic: Update lead status
   */
  updateStatus(newStatus: LeadStatus, reason?: string): Lead {
    if (!this.status.canTransitionTo(newStatus)) {
      throw new Error(`Cannot transition from ${this.status.value} to ${newStatus.value}`);
    }

    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      newStatus,
      this.source,
      this.expectedRevenue,
      this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      this.tags,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Add tag to lead
   */
  addTag(tag: string): Lead {
    if (this.tags.includes(tag)) {
      return this;
    }

    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.expectedRevenue,
      this.probability,
      this.description,
      this.assignedUserId,
      this.companyId,
      [...this.tags, tag],
      this.createdAt,
      new Date()
    );
  }

  /**
   * Business logic: Assign lead to user
   */
  assignTo(userId: number): Lead {
    return new Lead(
      this.id,
      this.name,
      this.contactInfo,
      this.status,
      this.source,
      this.expectedRevenue,
      this.probability,
      this.description,
      userId,
      this.companyId,
      this.tags,
      this.createdAt,
      new Date()
    );
  }

  /**
   * Private helper: Get score based on lead source
   */
  private getSourceScore(): number {
    const sourceScores: Record<string, number> = {
      'website': 15,
      'referral': 20,
      'social_media': 10,
      'email_campaign': 12,
      'cold_call': 8,
      'trade_show': 18,
      'partner': 16,
      'direct': 14,
    };

    return sourceScores[this.source.toLowerCase()] || 5;
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      id: this.id,
      name: this.name,
      contactInfo: this.contactInfo.toPlainObject(),
      status: this.status.value,
      source: this.source,
      expectedRevenue: this.expectedRevenue,
      probability: this.probability,
      description: this.description,
      assignedUserId: this.assignedUserId,
      companyId: this.companyId,
      tags: this.tags,
      score: this.calculateScore(),
      isQualified: this.isQualified(),
      canConvert: this.canConvertToOpportunity(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
