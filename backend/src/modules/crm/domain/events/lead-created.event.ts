/**
 * Lead Created Domain Event
 * Published when a new lead is created in the system
 */
export class LeadCreatedEvent {
  constructor(
    public readonly leadId: number,
    public readonly leadName: string,
    public readonly email?: string,
    public readonly source?: string,
    public readonly score?: number,
    public readonly timestamp: Date = new Date(),
  ) {}

  /**
   * Get event name for event bus
   */
  static getEventName(): string {
    return 'lead.created';
  }

  /**
   * Convert to plain object for serialization
   */
  toPlainObject() {
    return {
      eventName: LeadCreatedEvent.getEventName(),
      leadId: this.leadId,
      leadName: this.leadName,
      email: this.email,
      source: this.source,
      score: this.score,
      timestamp: this.timestamp,
    };
  }
}
