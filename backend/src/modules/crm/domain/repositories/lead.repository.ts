import { Lead } from '../entities/lead.entity';

/**
 * Lead Repository Interface
 * Defines the contract for lead data persistence
 */
export interface ILeadRepository {
  /**
   * Save a lead (create or update)
   */
  save(lead: Lead): Promise<Lead>;

  /**
   * Find lead by ID
   */
  findById(id: number): Promise<Lead | null>;

  /**
   * Find lead by email
   */
  findByEmail(email: string): Promise<Lead | null>;

  /**
   * Find leads with filters
   */
  findMany(filters: {
    status?: string;
    source?: string;
    assignedUserId?: number;
    minScore?: number;
    maxScore?: number;
    offset?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    leads: Lead[];
    total: number;
    analytics: {
      averageScore: number;
      conversionRate: number;
      topSources: Array<{ source: string; count: number }>;
    };
  }>;

  /**
   * Update lead status
   */
  updateStatus(id: number, status: string): Promise<boolean>;

  /**
   * Delete lead (soft delete)
   */
  delete(id: number): Promise<boolean>;

  /**
   * Get lead statistics
   */
  getStatistics(): Promise<{
    totalLeads: number;
    qualifiedLeads: number;
    convertedLeads: number;
    averageScore: number;
    conversionRate: number;
  }>;
}
