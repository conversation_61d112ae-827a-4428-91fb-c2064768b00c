/**
 * Lead Status Value Object
 * Represents the current status of a lead in the CRM pipeline
 */
export class LeadStatus {
  private static readonly VALID_STATUSES = [
    'new',
    'contacted',
    'qualified',
    'proposal',
    'negotiation',
    'won',
    'lost',
    'cancelled'
  ] as const;

  private static readonly STATUS_TRANSITIONS: Record<string, string[]> = {
    'new': ['contacted', 'qualified', 'lost', 'cancelled'],
    'contacted': ['qualified', 'proposal', 'lost', 'cancelled'],
    'qualified': ['proposal', 'negotiation', 'lost', 'cancelled'],
    'proposal': ['negotiation', 'won', 'lost', 'cancelled'],
    'negotiation': ['won', 'lost', 'cancelled'],
    'won': [], // Terminal state
    'lost': [], // Terminal state
    'cancelled': [] // Terminal state
  };

  private static readonly STATUS_WEIGHTS: Record<string, number> = {
    'new': 5,
    'contacted': 10,
    'qualified': 20,
    'proposal': 30,
    'negotiation': 40,
    'won': 100,
    'lost': 0,
    'cancelled': 0
  };

  constructor(public readonly value: string) {
    if (!LeadStatus.VALID_STATUSES.includes(value as any)) {
      throw new Error(`Invalid lead status: ${value}`);
    }
  }

  /**
   * Check if this status is qualified
   */
  isQualified(): boolean {
    return ['qualified', 'proposal', 'negotiation', 'won'].includes(this.value);
  }

  /**
   * Check if lead can be converted to opportunity
   */
  canConvert(): boolean {
    return ['qualified', 'proposal', 'negotiation'].includes(this.value);
  }

  /**
   * Check if this is a terminal status
   */
  isTerminal(): boolean {
    return ['won', 'lost', 'cancelled'].includes(this.value);
  }

  /**
   * Check if this is a winning status
   */
  isWon(): boolean {
    return this.value === 'won';
  }

  /**
   * Check if this is a losing status
   */
  isLost(): boolean {
    return ['lost', 'cancelled'].includes(this.value);
  }

  /**
   * Check if can transition to another status
   */
  canTransitionTo(newStatus: LeadStatus): boolean {
    const allowedTransitions = LeadStatus.STATUS_TRANSITIONS[this.value] || [];
    return allowedTransitions.includes(newStatus.value);
  }

  /**
   * Get score weight for this status
   */
  getScoreWeight(): number {
    return LeadStatus.STATUS_WEIGHTS[this.value] || 0;
  }

  /**
   * Get human-readable label
   */
  getLabel(): string {
    const labels: Record<string, string> = {
      'new': 'New Lead',
      'contacted': 'Contacted',
      'qualified': 'Qualified',
      'proposal': 'Proposal Sent',
      'negotiation': 'In Negotiation',
      'won': 'Won',
      'lost': 'Lost',
      'cancelled': 'Cancelled'
    };

    return labels[this.value] || this.value;
  }

  /**
   * Get status color for UI
   */
  getColor(): string {
    const colors: Record<string, string> = {
      'new': '#6c757d',        // Gray
      'contacted': '#17a2b8',   // Info blue
      'qualified': '#ffc107',   // Warning yellow
      'proposal': '#fd7e14',    // Orange
      'negotiation': '#6f42c1', // Purple
      'won': '#28a745',         // Success green
      'lost': '#dc3545',        // Danger red
      'cancelled': '#6c757d'    // Gray
    };

    return colors[this.value] || '#6c757d';
  }

  /**
   * Get next possible statuses
   */
  getNextStatuses(): LeadStatus[] {
    const nextStatusValues = LeadStatus.STATUS_TRANSITIONS[this.value] || [];
    return nextStatusValues.map(status => new LeadStatus(status));
  }

  /**
   * Create from string
   */
  static fromString(value: string): LeadStatus {
    return new LeadStatus(value.toLowerCase());
  }

  /**
   * Get all valid statuses
   */
  static getAllStatuses(): LeadStatus[] {
    return LeadStatus.VALID_STATUSES.map(status => new LeadStatus(status));
  }

  /**
   * Equality check
   */
  equals(other: LeadStatus): boolean {
    return this.value === other.value;
  }

  /**
   * String representation
   */
  toString(): string {
    return this.value;
  }

  /**
   * JSON serialization
   */
  toJSON(): string {
    return this.value;
  }
}
