/**
 * Contact Information Value Object
 * Encapsulates contact details with validation and business logic
 */
export class ContactInfo {
  constructor(
    public readonly email?: string,
    public readonly phone?: string,
    public readonly company?: string,
    public readonly website?: string,
    public readonly address?: string,
    public readonly city?: string,
    public readonly country?: string,
  ) {
    this.validateEmail();
    this.validatePhone();
    this.validateWebsite();
  }

  /**
   * Check if contact info is complete
   */
  isComplete(): boolean {
    return !!(this.email && this.phone && this.company);
  }

  /**
   * Check if has any contact method
   */
  hasContactMethod(): boolean {
    return !!(this.email || this.phone);
  }

  /**
   * Check if email is available
   */
  hasEmail(): boolean {
    return !!this.email;
  }

  /**
   * Check if phone is available
   */
  hasPhone(): boolean {
    return !!this.phone;
  }

  /**
   * Check if company info is available
   */
  hasCompany(): boolean {
    return !!this.company;
  }

  /**
   * Get completeness score (0-100)
   */
  getCompletenessScore(): number {
    let score = 0;
    
    if (this.email) score += 25;
    if (this.phone) score += 25;
    if (this.company) score += 20;
    if (this.website) score += 10;
    if (this.address) score += 10;
    if (this.city) score += 5;
    if (this.country) score += 5;
    
    return score;
  }

  /**
   * Get missing fields
   */
  getMissingFields(): string[] {
    const missing: string[] = [];
    
    if (!this.email) missing.push('email');
    if (!this.phone) missing.push('phone');
    if (!this.company) missing.push('company');
    if (!this.website) missing.push('website');
    if (!this.address) missing.push('address');
    
    return missing;
  }

  /**
   * Get primary contact method
   */
  getPrimaryContactMethod(): 'email' | 'phone' | 'none' {
    if (this.email) return 'email';
    if (this.phone) return 'phone';
    return 'none';
  }

  /**
   * Format phone number for display
   */
  getFormattedPhone(): string {
    if (!this.phone) return '';
    
    // Simple formatting - in real app, use a proper phone formatting library
    const cleaned = this.phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return this.phone;
  }

  /**
   * Get domain from email
   */
  getEmailDomain(): string {
    if (!this.email) return '';
    const parts = this.email.split('@');
    return parts.length > 1 ? parts[1].toLowerCase() : '';
  }

  /**
   * Check if email domain suggests business email
   */
  isBusinessEmail(): boolean {
    if (!this.email) return false;
    
    const domain = this.getEmailDomain();
    const personalDomains = [
      'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
      'aol.com', 'icloud.com', 'live.com', 'msn.com'
    ];
    
    return !personalDomains.includes(domain);
  }

  /**
   * Create updated contact info
   */
  updateEmail(email: string): ContactInfo {
    return new ContactInfo(
      email,
      this.phone,
      this.company,
      this.website,
      this.address,
      this.city,
      this.country
    );
  }

  updatePhone(phone: string): ContactInfo {
    return new ContactInfo(
      this.email,
      phone,
      this.company,
      this.website,
      this.address,
      this.city,
      this.country
    );
  }

  updateCompany(company: string): ContactInfo {
    return new ContactInfo(
      this.email,
      this.phone,
      company,
      this.website,
      this.address,
      this.city,
      this.country
    );
  }

  /**
   * Validate email format
   */
  private validateEmail(): void {
    if (this.email && !this.isValidEmail(this.email)) {
      throw new Error(`Invalid email format: ${this.email}`);
    }
  }

  /**
   * Validate phone format
   */
  private validatePhone(): void {
    if (this.phone && !this.isValidPhone(this.phone)) {
      throw new Error(`Invalid phone format: ${this.phone}`);
    }
  }

  /**
   * Validate website format
   */
  private validateWebsite(): void {
    if (this.website && !this.isValidWebsite(this.website)) {
      throw new Error(`Invalid website format: ${this.website}`);
    }
  }

  /**
   * Email validation helper
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Phone validation helper
   */
  private isValidPhone(phone: string): boolean {
    // Allow various phone formats
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleaned = phone.replace(/[\s\-\(\)\.]/g, '');
    return phoneRegex.test(cleaned) && cleaned.length >= 7;
  }

  /**
   * Website validation helper
   */
  private isValidWebsite(website: string): boolean {
    try {
      new URL(website);
      return true;
    } catch {
      // Try with https prefix
      try {
        new URL(`https://${website}`);
        return true;
      } catch {
        return false;
      }
    }
  }

  /**
   * Equality check
   */
  equals(other: ContactInfo): boolean {
    return this.email === other.email &&
           this.phone === other.phone &&
           this.company === other.company &&
           this.website === other.website &&
           this.address === other.address &&
           this.city === other.city &&
           this.country === other.country;
  }

  /**
   * Convert to plain object
   */
  toPlainObject() {
    return {
      email: this.email,
      phone: this.phone,
      company: this.company,
      website: this.website,
      address: this.address,
      city: this.city,
      country: this.country,
      isComplete: this.isComplete(),
      hasContactMethod: this.hasContactMethod(),
      completenessScore: this.getCompletenessScore(),
      primaryContactMethod: this.getPrimaryContactMethod(),
      isBusinessEmail: this.isBusinessEmail(),
      formattedPhone: this.getFormattedPhone(),
      emailDomain: this.getEmailDomain(),
    };
  }

  /**
   * String representation
   */
  toString(): string {
    const parts: string[] = [];
    if (this.email) parts.push(this.email);
    if (this.phone) parts.push(this.phone);
    if (this.company) parts.push(this.company);
    return parts.join(' | ');
  }
}
