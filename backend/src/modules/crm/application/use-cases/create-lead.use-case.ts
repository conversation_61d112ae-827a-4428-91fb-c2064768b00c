import { Injectable, Logger } from '@nestjs/common';
import { Lead } from '../../domain/entities/lead.entity';
import { LeadStatus } from '../../domain/value-objects/lead-status.vo';
import { ContactInfo } from '../../domain/value-objects/contact-info.vo';
import { ILeadRepository } from '../../domain/repositories/lead.repository';
import { CreateLeadDto } from '../dtos/create-lead.dto';
import { LeadCreatedEvent } from '../../domain/events/lead-created.event';
import { EventBus } from '@nestjs/cqrs';
import { ResponseBuilderService } from '../../../../common/services/response-builder.service';

export interface CreateLeadRequest {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  source: string;
  expectedRevenue?: number;
  probability?: number;
  description?: string;
  assignedUserId?: number;
  tags?: string[];
}

export interface CreateLeadResponse {
  lead: Lead;
  score: number;
  recommendations: string[];
}

@Injectable()
export class CreateLeadUseCase {
  private readonly logger = new Logger(CreateLeadUseCase.name);

  constructor(
    private readonly leadRepository: ILeadRepository,
    private readonly eventBus: EventBus,
    private readonly responseBuilder: ResponseBuilderService,
  ) {}

  async execute(request: CreateLeadRequest): Promise<CreateLeadResponse> {
    try {
      this.logger.log(`Creating new lead: ${request.name}`);

      // 1. Validate business rules
      await this.validateBusinessRules(request);

      // 2. Create contact info value object
      const contactInfo = new ContactInfo(
        request.email,
        request.phone,
        request.company,
      );

      // 3. Create lead entity
      const lead = new Lead(
        0, // Will be set by repository
        request.name,
        contactInfo,
        new LeadStatus('new'),
        request.source,
        request.expectedRevenue,
        request.probability || this.calculateInitialProbability(request),
        request.description,
        request.assignedUserId,
        undefined, // companyId will be set by context
        request.tags || [],
      );

      // 4. Apply business logic
      const enrichedLead = await this.enrichLead(lead, request);

      // 5. Save to repository
      const savedLead = await this.leadRepository.save(enrichedLead);

      // 6. Calculate lead score
      const score = savedLead.calculateScore();

      // 7. Generate recommendations
      const recommendations = this.generateRecommendations(savedLead);

      // 8. Publish domain event
      const event = new LeadCreatedEvent(
        savedLead.id,
        savedLead.name,
        savedLead.contactInfo.email,
        savedLead.source,
        score,
      );
      await this.eventBus.publish(event);

      this.logger.log(`Lead created successfully: ID ${savedLead.id}, Score: ${score}`);

      return {
        lead: savedLead,
        score,
        recommendations,
      };

    } catch (error) {
      this.logger.error(`Failed to create lead: ${request.name}`, error);
      throw error;
    }
  }

  /**
   * Validate business rules for lead creation
   */
  private async validateBusinessRules(request: CreateLeadRequest): Promise<void> {
    // Check for duplicate leads
    if (request.email) {
      const existingLead = await this.leadRepository.findByEmail(request.email);
      if (existingLead && !existingLead.status.isTerminal()) {
        throw new Error(`Active lead already exists for email: ${request.email}`);
      }
    }

    // Validate source
    const validSources = [
      'website', 'referral', 'social_media', 'email_campaign',
      'cold_call', 'trade_show', 'partner', 'direct'
    ];
    if (!validSources.includes(request.source.toLowerCase())) {
      throw new Error(`Invalid lead source: ${request.source}`);
    }

    // Validate revenue
    if (request.expectedRevenue !== undefined && request.expectedRevenue < 0) {
      throw new Error('Expected revenue cannot be negative');
    }

    // Validate probability
    if (request.probability !== undefined && 
        (request.probability < 0 || request.probability > 100)) {
      throw new Error('Probability must be between 0 and 100');
    }
  }

  /**
   * Calculate initial probability based on source and other factors
   */
  private calculateInitialProbability(request: CreateLeadRequest): number {
    const sourceProbabilities: Record<string, number> = {
      'referral': 60,
      'partner': 55,
      'trade_show': 45,
      'website': 35,
      'direct': 40,
      'email_campaign': 25,
      'social_media': 20,
      'cold_call': 15,
    };

    let probability = sourceProbabilities[request.source.toLowerCase()] || 30;

    // Adjust based on expected revenue
    if (request.expectedRevenue) {
      if (request.expectedRevenue > 100000) probability += 10;
      else if (request.expectedRevenue > 50000) probability += 5;
      else if (request.expectedRevenue < 5000) probability -= 5;
    }

    // Adjust based on contact completeness
    if (request.email && request.phone) probability += 5;
    if (request.company) probability += 3;

    return Math.min(Math.max(probability, 5), 95);
  }

  /**
   * Enrich lead with additional business logic
   */
  private async enrichLead(lead: Lead, request: CreateLeadRequest): Promise<Lead> {
    let enrichedLead = lead;

    // Auto-assign based on source or territory
    if (!request.assignedUserId) {
      const assignedUserId = await this.autoAssignLead(lead);
      if (assignedUserId) {
        enrichedLead = enrichedLead.assignTo(assignedUserId);
      }
    }

    // Add automatic tags based on business rules
    const autoTags = this.generateAutoTags(request);
    for (const tag of autoTags) {
      enrichedLead = enrichedLead.addTag(tag);
    }

    return enrichedLead;
  }

  /**
   * Auto-assign lead to appropriate user
   */
  private async autoAssignLead(lead: Lead): Promise<number | undefined> {
    // This would typically involve more complex logic:
    // - Round-robin assignment
    // - Territory-based assignment
    // - Workload balancing
    // - Skill-based assignment

    // For now, simple source-based assignment
    const sourceAssignments: Record<string, number> = {
      'website': 1,
      'referral': 2,
      'social_media': 3,
      'email_campaign': 1,
      'cold_call': 4,
      'trade_show': 2,
      'partner': 5,
      'direct': 1,
    };

    return sourceAssignments[lead.source.toLowerCase()];
  }

  /**
   * Generate automatic tags based on lead characteristics
   */
  private generateAutoTags(request: CreateLeadRequest): string[] {
    const tags: string[] = [];

    // Source-based tags
    tags.push(`source:${request.source.toLowerCase()}`);

    // Revenue-based tags
    if (request.expectedRevenue) {
      if (request.expectedRevenue > 100000) tags.push('high-value');
      else if (request.expectedRevenue > 50000) tags.push('medium-value');
      else tags.push('low-value');
    }

    // Company-based tags
    if (request.company) {
      tags.push('has-company');
    }

    // Contact completeness tags
    if (request.email && request.phone) {
      tags.push('complete-contact');
    }

    return tags;
  }

  /**
   * Generate recommendations for lead management
   */
  private generateRecommendations(lead: Lead): string[] {
    const recommendations: string[] = [];

    // Score-based recommendations
    const score = lead.calculateScore();
    if (score > 80) {
      recommendations.push('High-priority lead - contact immediately');
    } else if (score > 60) {
      recommendations.push('Good potential - schedule follow-up within 24 hours');
    } else if (score < 30) {
      recommendations.push('Low priority - consider nurturing campaign');
    }

    // Contact info recommendations
    if (!lead.contactInfo.email) {
      recommendations.push('Missing email - try to obtain during first contact');
    }
    if (!lead.contactInfo.phone) {
      recommendations.push('Missing phone - request during qualification');
    }

    // Revenue recommendations
    if (!lead.expectedRevenue) {
      recommendations.push('No revenue estimate - qualify budget during discovery');
    }

    // Assignment recommendations
    if (!lead.assignedUserId) {
      recommendations.push('Unassigned lead - assign to appropriate sales rep');
    }

    return recommendations;
  }
}
