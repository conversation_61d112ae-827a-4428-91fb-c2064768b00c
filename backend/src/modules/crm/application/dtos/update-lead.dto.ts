import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsA<PERSON>y, <PERSON>, <PERSON> } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Update Lead DTO
 * Data Transfer Object for updating existing leads
 */
export class UpdateLeadDto {
  @ApiPropertyOptional({
    description: 'Lead name or title',
    example: '<PERSON> Smith - Enterprise Solutions',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '******-0123',
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Company name',
    example: 'Enterprise Corp',
  })
  @IsOptional()
  @IsString()
  company?: string;

  @ApiPropertyOptional({
    description: 'Lead source',
    example: 'website',
    enum: ['website', 'referral', 'social_media', 'email_campaign', 'cold_call', 'trade_show', 'partner', 'direct'],
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Lead status',
    example: 'qualified',
    enum: ['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'cancelled'],
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({
    description: 'Expected revenue in USD',
    example: 150000,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  expectedRevenue?: number;

  @ApiPropertyOptional({
    description: 'Probability percentage (0-100)',
    example: 70,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  probability?: number;

  @ApiPropertyOptional({
    description: 'Lead description or notes',
    example: 'Updated requirements after discovery call',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Assigned user ID',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  assignedUserId?: number;

  @ApiPropertyOptional({
    description: 'Lead tags',
    example: ['enterprise', 'erp', 'urgent'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}
