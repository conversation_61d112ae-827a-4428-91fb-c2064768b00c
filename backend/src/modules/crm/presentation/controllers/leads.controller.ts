import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiConsumes,
  ApiProduces,
} from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '../../../../infrastructure/auth/jwt-auth.guard';
// import { ResponseBuilderService } from '../../../../common/services/response-builder.service';
import { CreateLeadUseCase } from '../../application/use-cases/create-lead.use-case';
import { UpdateLeadUseCase } from '../../application/use-cases/update-lead.use-case';
import { GetLeadsQuery } from '../../application/queries/get-leads.query';
import { CreateLeadDto } from '../../application/dtos/create-lead.dto';
import { UpdateLeadDto } from '../../application/dtos/update-lead.dto';
import { LeadFilterDto } from '../../application/dtos/lead-filter.dto';

@ApiTags('CRM - Leads')
@Controller({ path: 'crm/leads', version: '1' })
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LeadsController {
  constructor(
    private readonly createLeadUseCase: CreateLeadUseCase,
    private readonly updateLeadUseCase: UpdateLeadUseCase,
    private readonly getLeadsQuery: GetLeadsQuery,
    // private readonly responseBuilder: ResponseBuilderService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: '🎯 Create New Lead',
    description: `
**Create a new lead in the CRM system with intelligent scoring and auto-assignment.**

### Features:
- 🤖 **Auto-scoring**: Intelligent lead scoring based on multiple factors
- 👥 **Auto-assignment**: Automatic assignment to appropriate sales reps
- 🏷️ **Auto-tagging**: Automatic tag generation based on lead characteristics
- 📊 **Recommendations**: AI-powered recommendations for lead management
- ✅ **Validation**: Comprehensive business rule validation

### Scoring Factors:
- Lead source quality (referral > website > cold call)
- Expected revenue potential
- Contact information completeness
- Probability assessment
- Company information availability

### Auto-assignment Rules:
- Territory-based assignment
- Workload balancing
- Source-specific routing
- Skill-based matching
    `,
  })
  @ApiConsumes('application/json')
  @ApiProduces('application/json')
  @ApiBody({
    type: CreateLeadDto,
    description: 'Lead creation data with contact and business information',
    examples: {
      'High-Value Lead': {
        summary: 'High-value enterprise lead',
        description: 'Example of a high-potential enterprise lead',
        value: {
          name: 'John Smith - Enterprise Solutions',
          email: '<EMAIL>',
          phone: '******-0123',
          company: 'Enterprise Corp',
          source: 'referral',
          expectedRevenue: 150000,
          probability: 70,
          description: 'Looking for comprehensive ERP solution for 500+ employees',
          tags: ['enterprise', 'erp', 'urgent']
        }
      },
      'Website Lead': {
        summary: 'Website inquiry lead',
        description: 'Lead from website contact form',
        value: {
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '******-0456',
          company: 'Small Business Inc',
          source: 'website',
          expectedRevenue: 25000,
          description: 'Interested in accounting software for small business'
        }
      },
      'Trade Show Lead': {
        summary: 'Trade show contact',
        description: 'Lead collected at industry trade show',
        value: {
          name: 'Mike Chen',
          email: '<EMAIL>',
          phone: '******-0789',
          company: 'Manufacturing Solutions',
          source: 'trade_show',
          expectedRevenue: 75000,
          probability: 45,
          description: 'Interested in inventory management system',
          tags: ['manufacturing', 'inventory']
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: '✅ Lead created successfully',
    content: {
      'application/json': {
        examples: {
          'Success Response': {
            summary: 'Successful lead creation',
            value: {
              success: true,
              data: {
                lead: {
                  id: 123,
                  name: 'John Smith - Enterprise Solutions',
                  contactInfo: {
                    email: '<EMAIL>',
                    phone: '******-0123',
                    company: 'Enterprise Corp'
                  },
                  status: 'new',
                  source: 'referral',
                  expectedRevenue: 150000,
                  probability: 70,
                  assignedUserId: 5,
                  tags: ['enterprise', 'erp', 'urgent', 'source:referral', 'high-value'],
                  score: 85,
                  isQualified: false,
                  canConvert: false
                },
                score: 85,
                recommendations: [
                  'High-priority lead - contact immediately',
                  'Complete contact information available',
                  'High revenue potential - prioritize qualification'
                ]
              },
              message: 'Lead created successfully with score 85',
              statusCode: 201,
              apiVersion: 'v1',
              timestamp: '2025-07-26T08:30:15.123Z',
              path: '/api/v1/crm/leads',
              meta: {
                leadScore: 85,
                autoAssigned: true,
                assignedUserId: 5,
                autoTags: ['source:referral', 'high-value'],
                processingTime: 245
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '❌ Invalid lead data or business rule violation'
  })
  @ApiResponse({
    status: 409,
    description: '⚠️ Duplicate lead exists'
  })
  async createLead(
    @Body() createLeadDto: CreateLeadDto,
    @Req() request: Request,
  ) {
    const startTime = Date.now();

    const result = await this.createLeadUseCase.execute({
      name: createLeadDto.name,
      email: createLeadDto.email,
      phone: createLeadDto.phone,
      company: createLeadDto.company,
      source: createLeadDto.source,
      expectedRevenue: createLeadDto.expectedRevenue,
      probability: createLeadDto.probability,
      description: createLeadDto.description,
      assignedUserId: createLeadDto.assignedUserId,
      tags: createLeadDto.tags,
    });

    // Mock response for now
    return {
      success: true,
      data: result,
      message: `Lead created successfully with score ${result.score}`,
      statusCode: 201,
      meta: {
        leadScore: result.score,
        autoAssigned: !!result.lead.assignedUserId,
        assignedUserId: result.lead.assignedUserId,
        processingTime: Date.now() - startTime,
      }
    };
  }

  @Get()
  @ApiOperation({
    summary: '📋 Get Leads List',
    description: `
**Retrieve leads with advanced filtering, sorting, and pagination.**

### Features:
- 🔍 **Advanced Filtering**: Filter by status, source, score, assignment
- 📊 **Sorting**: Sort by score, revenue, date, status
- 📄 **Pagination**: Efficient pagination with metadata
- 📈 **Analytics**: Lead statistics and conversion metrics
- 🎯 **Search**: Full-text search across lead data

### Filter Options:
- Status: new, contacted, qualified, proposal, negotiation, won, lost
- Source: website, referral, social_media, email_campaign, etc.
- Score range: minimum and maximum score values
- Revenue range: expected revenue filtering
- Assignment: assigned/unassigned leads
- Date range: creation date filtering
    `,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by lead status',
    enum: ['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'cancelled']
  })
  @ApiQuery({
    name: 'source',
    required: false,
    description: 'Filter by lead source'
  })
  @ApiQuery({
    name: 'minScore',
    required: false,
    description: 'Minimum lead score (0-100)',
    type: Number
  })
  @ApiQuery({
    name: 'maxScore',
    required: false,
    description: 'Maximum lead score (0-100)',
    type: Number
  })
  @ApiQuery({
    name: 'assignedUserId',
    required: false,
    description: 'Filter by assigned user ID',
    type: Number
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (1-based)',
    type: Number,
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page (max 100)',
    type: Number,
    example: 20
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Sort field',
    enum: ['score', 'expectedRevenue', 'createdAt', 'updatedAt', 'name']
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order',
    enum: ['asc', 'desc']
  })
  @ApiResponse({
    status: 200,
    description: '✅ Leads retrieved successfully'
  })
  async getLeads(
    @Query() filterDto: LeadFilterDto,
    @Req() request: Request,
  ) {
    const startTime = Date.now();

    const result = await this.getLeadsQuery.execute(filterDto);

    // Mock response for now
    return {
      success: true,
      data: result.leads,
      message: `Retrieved ${result.leads.length} leads`,
      statusCode: 200,
      meta: {
        totalLeads: result.total,
        averageScore: result.analytics.averageScore,
        conversionRate: result.analytics.conversionRate,
        topSources: result.analytics.topSources,
        processingTime: Date.now() - startTime,
      }
    };
  }

  @Get(':id')
  @ApiOperation({
    summary: '🔍 Get Lead Details',
    description: 'Retrieve detailed information about a specific lead including history and analytics'
  })
  @ApiParam({
    name: 'id',
    description: 'Lead ID',
    type: Number,
    example: 123
  })
  @ApiResponse({
    status: 200,
    description: '✅ Lead details retrieved successfully'
  })
  @ApiResponse({
    status: 404,
    description: '❌ Lead not found'
  })
  async getLeadById(
    @Param('id') id: number,
    @Req() request: Request,
  ) {
    // Implementation would go here
    // This is just the controller structure
  }

  @Put(':id')
  @ApiOperation({
    summary: '✏️ Update Lead',
    description: 'Update lead information with automatic re-scoring and validation'
  })
  async updateLead(
    @Param('id') id: number,
    @Body() updateLeadDto: UpdateLeadDto,
    @Req() request: Request,
  ) {
    // Implementation would go here
  }

  @Delete(':id')
  @ApiOperation({
    summary: '🗑️ Delete Lead',
    description: 'Soft delete a lead (marks as cancelled)'
  })
  async deleteLead(
    @Param('id') id: number,
    @Req() request: Request,
  ) {
    // Implementation would go here
  }

  @Post(':id/convert')
  @ApiOperation({
    summary: '🔄 Convert Lead to Opportunity',
    description: 'Convert a qualified lead to an opportunity'
  })
  async convertToOpportunity(
    @Param('id') id: number,
    @Req() request: Request,
  ) {
    // Implementation would go here
  }
}
