import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

// Shared modules
import { CommonModule } from '../../common/common.module';
import { DatabaseModule } from '../../infrastructure/database/database.module';

// Domain services
import { LeadScoringService } from './domain/services/lead-scoring.service';
import { PipelineAnalyticsService } from './domain/services/pipeline-analytics.service';
import { ConversionTrackingService } from './domain/services/conversion-tracking.service';

// Application layer
import { CreateLeadUseCase } from './application/use-cases/create-lead.use-case';
import { UpdateLeadUseCase } from './application/use-cases/update-lead.use-case';
import { ConvertLeadToOpportunityUseCase } from './application/use-cases/convert-lead-to-opportunity.use-case';
import { GeneratePipelineReportUseCase } from './application/use-cases/generate-pipeline-report.use-case';

// Queries
import { GetLeadsQuery } from './application/queries/get-leads.query';
import { GetPipelineAnalyticsQuery } from './application/queries/get-pipeline-analytics.query';
import { GetCustomerHistoryQuery } from './application/queries/get-customer-history.query';

// Event handlers
import { LeadCreatedHandler } from './application/handlers/lead-created.handler';
import { OpportunityWonHandler } from './application/handlers/opportunity-won.handler';

// Infrastructure layer
import { OdooCrmAdapter } from './infrastructure/adapters/odoo-crm.adapter';
import { EmailIntegrationAdapter } from './infrastructure/adapters/email-integration.adapter';
import { CalendarSyncAdapter } from './infrastructure/adapters/calendar-sync.adapter';

// Repositories
import { OdooLeadRepository } from './infrastructure/repositories/odoo-lead.repository';
import { OdooOpportunityRepository } from './infrastructure/repositories/odoo-opportunity.repository';
import { OdooCustomerRepository } from './infrastructure/repositories/odoo-customer.repository';

// Mappers
import { LeadMapper } from './infrastructure/mappers/lead.mapper';
import { OpportunityMapper } from './infrastructure/mappers/opportunity.mapper';
import { CustomerMapper } from './infrastructure/mappers/customer.mapper';

// External services
import { EmailService } from './infrastructure/external-services/email.service';
import { SmsService } from './infrastructure/external-services/sms.service';
import { CalendarService } from './infrastructure/external-services/calendar.service';

// Presentation layer
import { LeadsController } from './presentation/controllers/leads.controller';
import { OpportunitiesController } from './presentation/controllers/opportunities.controller';
import { CustomersController } from './presentation/controllers/customers.controller';
import { PipelineController } from './presentation/controllers/pipeline.controller';

// Repository tokens for DI
const LEAD_REPOSITORY_TOKEN = 'ILeadRepository';
const OPPORTUNITY_REPOSITORY_TOKEN = 'IOpportunityRepository';
const CUSTOMER_REPOSITORY_TOKEN = 'ICustomerRepository';

// Use cases
const UseCases = [
  CreateLeadUseCase,
  UpdateLeadUseCase,
  ConvertLeadToOpportunityUseCase,
  GeneratePipelineReportUseCase,
];

// Queries
const Queries = [
  GetLeadsQuery,
  GetPipelineAnalyticsQuery,
  GetCustomerHistoryQuery,
];

// Event handlers
const EventHandlers = [
  LeadCreatedHandler,
  OpportunityWonHandler,
];

// Domain services
const DomainServices = [
  LeadScoringService,
  PipelineAnalyticsService,
  ConversionTrackingService,
];

// Infrastructure adapters
const InfrastructureAdapters = [
  OdooCrmAdapter,
  EmailIntegrationAdapter,
  CalendarSyncAdapter,
];

// Repositories
const Repositories = [
  {
    provide: LEAD_REPOSITORY_TOKEN,
    useClass: OdooLeadRepository,
  },
  {
    provide: OPPORTUNITY_REPOSITORY_TOKEN,
    useClass: OdooOpportunityRepository,
  },
  {
    provide: CUSTOMER_REPOSITORY_TOKEN,
    useClass: OdooCustomerRepository,
  },
];

// Mappers
const Mappers = [
  LeadMapper,
  OpportunityMapper,
  CustomerMapper,
];

// External services
const ExternalServices = [
  EmailService,
  SmsService,
  CalendarService,
];

// Controllers
const Controllers = [
  LeadsController,
  OpportunitiesController,
  CustomersController,
  PipelineController,
];

@Module({
  imports: [
    CommonModule,
    DatabaseModule,
    CqrsModule, // For CQRS pattern support
  ],
  providers: [
    // Domain services
    ...DomainServices,
    
    // Application layer
    ...UseCases,
    ...Queries,
    ...EventHandlers,
    
    // Infrastructure layer
    ...InfrastructureAdapters,
    ...Repositories,
    ...Mappers,
    ...ExternalServices,
  ],
  controllers: Controllers,
  exports: [
    // Export use cases for other modules
    ...UseCases,
    ...Queries,
    
    // Export domain services
    ...DomainServices,
    
    // Export repository tokens
    LEAD_REPOSITORY_TOKEN,
    OPPORTUNITY_REPOSITORY_TOKEN,
    CUSTOMER_REPOSITORY_TOKEN,
  ],
})
export class CrmModule {}

/**
 * CRM Module Configuration
 * 
 * This module implements the CRM bounded context with:
 * 
 * 1. **Domain Layer**:
 *    - Entities: Lead, Opportunity, Customer, Activity, Pipeline
 *    - Value Objects: LeadStatus, OpportunityStage, ContactInfo, RevenueForecast
 *    - Domain Services: Lead scoring, pipeline analytics, conversion tracking
 *    - Domain Events: Lead created, opportunity won, customer converted
 * 
 * 2. **Application Layer**:
 *    - Use Cases: Create lead, convert to opportunity, generate reports
 *    - Queries: Get leads, pipeline analytics, customer history
 *    - Event Handlers: Handle domain events and trigger side effects
 *    - DTOs: Data transfer objects for API communication
 * 
 * 3. **Infrastructure Layer**:
 *    - Adapters: Odoo CRM integration, email, calendar sync
 *    - Repositories: Data persistence implementations
 *    - Mappers: Domain ↔ Odoo model mapping
 *    - External Services: Email, SMS, calendar integrations
 * 
 * 4. **Presentation Layer**:
 *    - Controllers: REST API endpoints for CRM operations
 *    - DTOs: Request/response data structures
 *    - Validators: Input validation logic
 * 
 * **Design Patterns Used**:
 * - Domain-Driven Design (DDD)
 * - Command Query Responsibility Segregation (CQRS)
 * - Event Sourcing (for domain events)
 * - Repository Pattern
 * - Adapter Pattern
 * - Strategy Pattern (for lead scoring)
 * - Factory Pattern (for entity creation)
 * 
 * **Integration Points**:
 * - Odoo CRM models (crm.lead, crm.opportunity, res.partner)
 * - Email systems (SMTP, Exchange, Gmail)
 * - Calendar systems (Google Calendar, Outlook)
 * - SMS providers (Twilio, AWS SNS)
 * - Analytics platforms (for reporting)
 */
