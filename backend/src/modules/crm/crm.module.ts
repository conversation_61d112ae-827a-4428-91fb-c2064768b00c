import { Module } from '@nestjs/common';
// import { CqrsModule } from '@nestjs/cqrs';

// Shared modules (automatically available via @Global decorator)
import { CommonModule } from '../../common/common.module';

// Application layer (only implemented ones)
import { CreateLeadUseCase } from './application/use-cases/create-lead.use-case';
import { UpdateLeadUseCase } from './application/use-cases/update-lead.use-case';

// Queries (only implemented ones)
import { GetLeadsQuery } from './application/queries/get-leads.query';

// Presentation layer (only implemented ones)
import { LeadsController } from './presentation/controllers/leads.controller';

// Use cases (only implemented ones)
const UseCases = [
  CreateLeadUseCase,
  UpdateLeadUseCase,
];

// Queries (only implemented ones)
const Queries = [
  GetLeadsQuery,
];

// Controllers (only implemented ones)
const Controllers = [
  LeadsController,
];

@Module({
  imports: [
    CommonModule,
    // CqrsModule, // For CQRS pattern support (will add later)
    // Note: SharedModule is @Global, so database and auth are automatically available
  ],
  providers: [
    // Application layer
    ...UseCases,
    ...Queries,

    // Note: Other services will be added as they are implemented
  ],
  controllers: Controllers,
  exports: [
    // Export use cases for other modules
    ...UseCases,
    ...Queries,
  ],
})
export class CrmModule {}

/**
 * CRM Module Configuration
 * 
 * This module implements the CRM bounded context with:
 * 
 * 1. **Domain Layer**:
 *    - Entities: Lead, Opportunity, Customer, Activity, Pipeline
 *    - Value Objects: LeadStatus, OpportunityStage, ContactInfo, RevenueForecast
 *    - Domain Services: Lead scoring, pipeline analytics, conversion tracking
 *    - Domain Events: Lead created, opportunity won, customer converted
 * 
 * 2. **Application Layer**:
 *    - Use Cases: Create lead, convert to opportunity, generate reports
 *    - Queries: Get leads, pipeline analytics, customer history
 *    - Event Handlers: Handle domain events and trigger side effects
 *    - DTOs: Data transfer objects for API communication
 * 
 * 3. **Infrastructure Layer**:
 *    - Adapters: Odoo CRM integration, email, calendar sync
 *    - Repositories: Data persistence implementations
 *    - Mappers: Domain ↔ Odoo model mapping
 *    - External Services: Email, SMS, calendar integrations
 * 
 * 4. **Presentation Layer**:
 *    - Controllers: REST API endpoints for CRM operations
 *    - DTOs: Request/response data structures
 *    - Validators: Input validation logic
 * 
 * **Design Patterns Used**:
 * - Domain-Driven Design (DDD)
 * - Command Query Responsibility Segregation (CQRS)
 * - Event Sourcing (for domain events)
 * - Repository Pattern
 * - Adapter Pattern
 * - Strategy Pattern (for lead scoring)
 * - Factory Pattern (for entity creation)
 * 
 * **Integration Points**:
 * - Odoo CRM models (crm.lead, crm.opportunity, res.partner)
 * - Email systems (SMTP, Exchange, Gmail)
 * - Calendar systems (Google Calendar, Outlook)
 * - SMS providers (Twilio, AWS SNS)
 * - Analytics platforms (for reporting)
 */
