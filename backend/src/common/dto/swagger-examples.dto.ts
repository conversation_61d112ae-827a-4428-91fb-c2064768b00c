import { ApiProperty } from '@nestjs/swagger';
import { 
  ApiResponse, 
  SuccessResponse, 
  ApiErrorResponse,
  PaginationMeta,
  ValidationError 
} from '../interfaces/api-response.interface';

/**
 * Swagger Examples for API Documentation
 */

// Success Response Examples
export class SwaggerSuccessResponseExample implements SuccessResponse {
  @ApiProperty({ 
    example: true, 
    description: 'Indicates if the request was successful' 
  })
  success: true;

  @ApiProperty({ 
    example: { id: 1, name: '<PERSON>', email: '<EMAIL>' },
    description: 'The actual response data'
  })
  data: any;

  @ApiProperty({ 
    example: 'Request completed successfully',
    description: 'Human-readable success message'
  })
  message: string;

  @ApiProperty({ 
    example: 200,
    description: 'HTTP status code'
  })
  statusCode: number;

  @ApiProperty({ 
    example: 'v1',
    description: 'API version used'
  })
  apiVersion: string;

  @ApiProperty({ 
    example: '2025-07-26T07:25:06.791Z',
    description: 'ISO timestamp of the response'
  })
  timestamp: string;

  @ApiProperty({ 
    example: '/api/v1/odoo/res.users/search',
    description: 'Request path',
    required: false
  })
  path?: string;

  @ApiProperty({ 
    example: { duration: 234, recordCount: 10 },
    description: 'Additional metadata about the request',
    required: false
  })
  meta?: Record<string, any>;
}

// Error Response Examples
export class SwaggerErrorResponseExample implements ApiErrorResponse {
  @ApiProperty({ 
    example: false, 
    description: 'Indicates the request failed' 
  })
  success: false;

  @ApiProperty({ 
    example: 'Unauthorized',
    description: 'Error type/category'
  })
  error: string;

  @ApiProperty({ 
    example: 'Authentication token required. Please connect to Odoo first.',
    description: 'Human-readable error message'
  })
  message: string;

  @ApiProperty({ 
    example: 401,
    description: 'HTTP status code'
  })
  statusCode: number;

  @ApiProperty({ 
    example: 'v1',
    description: 'API version used'
  })
  apiVersion: string;

  @ApiProperty({ 
    example: '2025-07-26T07:25:06.791Z',
    description: 'ISO timestamp of the response'
  })
  timestamp: string;

  @ApiProperty({ 
    example: '/api/v1/odoo/res.users/search',
    description: 'Request path',
    required: false
  })
  path?: string;

  @ApiProperty({ 
    example: 'UNAUTHORIZED',
    description: 'Machine-readable error code',
    required: false
  })
  errorCode?: string;

  @ApiProperty({ 
    type: [Object],
    example: [
      {
        field: 'email',
        message: 'Email must be a valid email address',
        value: 'invalid-email',
        rule: 'isEmail'
      }
    ],
    description: 'Validation errors for 400 responses',
    required: false
  })
  validationErrors?: ValidationError[];
}

// Pagination Example
export class SwaggerPaginationExample implements PaginationMeta {
  @ApiProperty({ 
    example: 1,
    description: 'Current page number (1-based)'
  })
  page: number;

  @ApiProperty({ 
    example: 10,
    description: 'Number of items per page'
  })
  limit: number;

  @ApiProperty({ 
    example: 150,
    description: 'Total number of items'
  })
  total: number;

  @ApiProperty({ 
    example: 15,
    description: 'Total number of pages'
  })
  totalPages: number;

  @ApiProperty({ 
    example: true,
    description: 'Whether there is a next page'
  })
  hasNext: boolean;

  @ApiProperty({ 
    example: false,
    description: 'Whether there is a previous page'
  })
  hasPrev: boolean;

  @ApiProperty({ 
    example: 2,
    description: 'Next page number (if exists)',
    required: false
  })
  nextPage?: number;

  @ApiProperty({ 
    example: null,
    description: 'Previous page number (if exists)',
    required: false
  })
  prevPage?: number;
}

// Connect Response Example
export class SwaggerConnectResponseExample {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({
    example: {
      token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhbm9uX09qb3hPbU4xIiwib2Rvb0luc3RhbmNlSWQiOiJvZG9vX2FIUjBjSE02THk5diIsIm9kb29Ib3N0IjoiaHR0cHM6Ly9vZG9vMTguYmVzdG1peC5vbmUvIiwib2Rvb0RhdGFiYXNlIjoiYmVzdG1peF8yN182Iiwib2Rvb1VzZXJuYW1lIjoidHVhbi5sZSIsInNlc3Npb25JZCI6InNlc3Npb25fMTc1MzUxNDEwODk2MF81cWFnczI2cXkiLCJpYXQiOjE3NTM1MTQxMDgsImV4cCI6MTc1MzYwMDUwOCwiYXVkIjoib2Rvby1jbGllbnQiLCJpc3MiOiJ1bml2ZXJzYWwtb2Rvby1hZGFwdGVyIn0.U0-7un8Veo4f7Jxb1h-z6u1xUb2bfFg5h1FKu3JJx3Q',
      expiresIn: '24h',
      version: {
        major: 18,
        minor: 0,
        patch: 0,
        series: '18.0',
        edition: 'enterprise',
        serverVersion: '18.0+e',
        protocolVersion: 1
      },
      capabilities: {
        hasJsonRpc: true,
        hasRestApi: false,
        hasGraphQL: false,
        hasWebSocket: true,
        hasTokenAuth: true,
        hasOAuth2: true,
        maxBatchSize: 1000,
        supportedAuthMethods: ['password', 'api_key', 'oauth2', 'token']
      }
    }
  })
  data: any;

  @ApiProperty({ example: 'Connected to Odoo successfully' })
  message: string;

  @ApiProperty({ example: 200 })
  statusCode: number;

  @ApiProperty({ example: 'v1' })
  apiVersion: string;

  @ApiProperty({ example: '2025-07-26T07:23:53.901Z' })
  timestamp: string;

  @ApiProperty({ example: '/api/v1/odoo/connect' })
  path: string;

  @ApiProperty({
    example: {
      connectionTime: 995,
      odooVersion: '18.0',
      protocol: 'JSON-RPC'
    }
  })
  meta: any;
}

// Search Response Example
export class SwaggerSearchResponseExample {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({
    example: [
      { id: 49, name: 'Admin', login: 'admin01' },
      { id: 2, name: 'Administrator', login: 'admin' },
      { id: 26, name: 'Bùi Doãn Quang', login: 'doanquang' }
    ]
  })
  data: any[];

  @ApiProperty({ example: 'Search completed successfully' })
  message: string;

  @ApiProperty({ example: 200 })
  statusCode: number;

  @ApiProperty({ example: 'v1' })
  apiVersion: string;

  @ApiProperty({ example: '2025-07-26T07:25:06.791Z' })
  timestamp: string;

  @ApiProperty({ example: '/api/v1/odoo/res.users/search' })
  path: string;

  @ApiProperty({
    example: { duration: 234, recordCount: 3 }
  })
  meta: any;
}
