import { Injectable } from '@nestjs/common';
import { Request } from 'express';
import {
  ApiResponse,
  ApiErrorResponse,
  SuccessResponse,
  ListResponse,
  CreatedResponse,
  UpdatedResponse,
  DeletedResponse,
  PaginationMeta,
  ValidationError,
  DebugInfo,
} from '../interfaces/api-response.interface';

@Injectable()
export class ResponseBuilderService {
  private readonly apiVersion = 'v1';

  /**
   * Build success response
   */
  success<T>(
    data: T,
    message: string = 'Request successful',
    statusCode: number = 200,
    request?: Request,
    meta?: Record<string, any>,
  ): SuccessResponse<T> {
    return {
      success: true,
      data,
      message,
      statusCode,
      apiVersion: this.apiVersion,
      timestamp: new Date().toISOString(),
      path: request?.url,
      meta,
    };
  }

  /**
   * Build paginated list response
   */
  list<T>(
    data: T[],
    pagination: PaginationMeta,
    message: string = 'List retrieved successfully',
    request?: Request,
    meta?: Record<string, any>,
  ): ListResponse<T> {
    return {
      success: true,
      data,
      message,
      statusCode: 200,
      apiVersion: this.apiVersion,
      timestamp: new Date().toISOString(),
      path: request?.url,
      pagination,
      meta,
    };
  }

  /**
   * Build created response
   */
  created<T>(
    data: T,
    message: string = 'Resource created successfully',
    request?: Request,
    meta?: Record<string, any>,
  ): CreatedResponse<T> {
    return {
      success: true,
      data,
      message,
      statusCode: 201,
      apiVersion: this.apiVersion,
      timestamp: new Date().toISOString(),
      path: request?.url,
      meta,
    };
  }

  /**
   * Build updated response
   */
  updated<T>(
    data: T,
    message: string = 'Resource updated successfully',
    request?: Request,
    meta?: Record<string, any>,
  ): UpdatedResponse<T> {
    return {
      success: true,
      data,
      message,
      statusCode: 200,
      apiVersion: this.apiVersion,
      timestamp: new Date().toISOString(),
      path: request?.url,
      meta,
    };
  }

  /**
   * Build deleted response
   */
  deleted(
    message: string = 'Resource deleted successfully',
    request?: Request,
    meta?: Record<string, any>,
  ): DeletedResponse {
    return {
      success: true,
      data: null,
      message,
      statusCode: 200,
      apiVersion: this.apiVersion,
      timestamp: new Date().toISOString(),
      path: request?.url,
      meta,
    };
  }

  /**
   * Build error response
   */
  error(
    error: string,
    message: string,
    statusCode: number = 500,
    request?: Request,
    errorCode?: string,
    validationErrors?: ValidationError[],
    additionalData?: Record<string, any>,
  ): ApiErrorResponse {
    const response: ApiErrorResponse = {
      success: false,
      error,
      message,
      statusCode,
      apiVersion: this.apiVersion,
      timestamp: new Date().toISOString(),
      path: request?.url,
      errorCode,
      validationErrors,
    };

    // Add debug information in development
    if (process.env.NODE_ENV === 'development' && request) {
      response.debug = this.buildDebugInfo(request, additionalData);
    }

    return response;
  }

  /**
   * Build validation error response
   */
  validationError(
    validationErrors: ValidationError[],
    message: string = 'Validation failed',
    request?: Request,
  ): ApiErrorResponse {
    return this.error(
      'Validation Error',
      message,
      400,
      request,
      'VALIDATION_FAILED',
      validationErrors,
    );
  }

  /**
   * Build unauthorized response
   */
  unauthorized(
    message: string = 'Authentication required',
    request?: Request,
    errorCode: string = 'UNAUTHORIZED',
  ): ApiErrorResponse {
    return this.error('Unauthorized', message, 401, request, errorCode);
  }

  /**
   * Build forbidden response
   */
  forbidden(
    message: string = 'Access denied',
    request?: Request,
    errorCode: string = 'FORBIDDEN',
  ): ApiErrorResponse {
    return this.error('Forbidden', message, 403, request, errorCode);
  }

  /**
   * Build not found response
   */
  notFound(
    message: string = 'Resource not found',
    request?: Request,
    errorCode: string = 'NOT_FOUND',
  ): ApiErrorResponse {
    return this.error('Not Found', message, 404, request, errorCode);
  }

  /**
   * Build conflict response
   */
  conflict(
    message: string = 'Resource conflict',
    request?: Request,
    errorCode: string = 'CONFLICT',
  ): ApiErrorResponse {
    return this.error('Conflict', message, 409, request, errorCode);
  }

  /**
   * Build internal server error response
   */
  internalError(
    message: string = 'Internal server error',
    request?: Request,
    errorCode: string = 'INTERNAL_ERROR',
    additionalData?: Record<string, any>,
  ): ApiErrorResponse {
    return this.error(
      'Internal Server Error',
      message,
      500,
      request,
      errorCode,
      undefined,
      additionalData,
    );
  }

  /**
   * Build pagination metadata
   */
  buildPagination(
    page: number,
    limit: number,
    total: number,
  ): PaginationMeta {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : undefined,
      prevPage: hasPrev ? page - 1 : undefined,
    };
  }

  /**
   * Build debug information for development
   */
  private buildDebugInfo(
    request: Request,
    additionalData?: Record<string, any>,
  ): DebugInfo {
    return {
      requestBody: request.body,
      requestHeaders: this.sanitizeHeaders(request.headers),
      userAgent: request.headers['user-agent'],
      ip: request.ip || request.connection?.remoteAddress,
      requestId: request.headers['x-request-id'] as string,
      additional: additionalData,
    };
  }

  /**
   * Sanitize headers to remove sensitive information
   */
  private sanitizeHeaders(headers: any): Record<string, string> {
    const sanitized = { ...headers };
    
    // Remove sensitive headers
    delete sanitized.authorization;
    delete sanitized.cookie;
    delete sanitized['x-api-key'];
    delete sanitized['x-auth-token'];
    
    return sanitized;
  }
}
