/**
 * Standard API Response Interface
 * Ensures consistent response format across all endpoints
 */

export interface ApiResponse<T = any> {
  /** Indicates if the request was successful */
  success: boolean;
  
  /** Response data (only present on success) */
  data?: T;
  
  /** Human-readable message */
  message: string;
  
  /** HTTP status code */
  statusCode: number;
  
  /** API version */
  apiVersion: string;
  
  /** Request timestamp */
  timestamp: string;
  
  /** Request path */
  path?: string;
  
  /** Pagination metadata (for paginated responses) */
  pagination?: PaginationMeta;
  
  /** Additional metadata */
  meta?: Record<string, any>;
}

export interface ApiErrorResponse extends Omit<ApiResponse, 'data'> {
  /** Error type/category */
  error: string;
  
  /** Error code for programmatic handling */
  errorCode?: string;
  
  /** Validation errors (for 400 responses) */
  validationErrors?: ValidationError[];
  
  /** Debug information (development only) */
  debug?: DebugInfo;
}

export interface PaginationMeta {
  /** Current page number (1-based) */
  page: number;
  
  /** Number of items per page */
  limit: number;
  
  /** Total number of items */
  total: number;
  
  /** Total number of pages */
  totalPages: number;
  
  /** Whether there's a next page */
  hasNext: boolean;
  
  /** Whether there's a previous page */
  hasPrev: boolean;
  
  /** Next page number (if exists) */
  nextPage?: number;
  
  /** Previous page number (if exists) */
  prevPage?: number;
}

export interface ValidationError {
  /** Field name that failed validation */
  field: string;
  
  /** Validation error message */
  message: string;
  
  /** Invalid value that was provided */
  value?: any;
  
  /** Validation rule that failed */
  rule?: string;
}

export interface DebugInfo {
  /** Full error stack trace */
  stack?: string;
  
  /** Request body */
  requestBody?: any;
  
  /** Request headers (sanitized) */
  requestHeaders?: Record<string, string>;
  
  /** User agent */
  userAgent?: string;
  
  /** Client IP address */
  ip?: string;
  
  /** Request ID for tracing */
  requestId?: string;
  
  /** Additional debug data */
  additional?: Record<string, any>;
}

/**
 * Success Response Types
 */
export interface SuccessResponse<T = any> extends ApiResponse<T> {
  success: true;
  data: T;
}

export interface ListResponse<T = any> extends SuccessResponse<T[]> {
  pagination: PaginationMeta;
}

export interface CreatedResponse<T = any> extends SuccessResponse<T> {
  statusCode: 201;
  message: string;
}

export interface UpdatedResponse<T = any> extends SuccessResponse<T> {
  statusCode: 200;
  message: string;
}

export interface DeletedResponse extends SuccessResponse<null> {
  statusCode: 200;
  data: null;
  message: string;
}

/**
 * Error Response Types
 */
export interface BadRequestResponse extends ApiErrorResponse {
  statusCode: 400;
  error: 'Bad Request';
}

export interface UnauthorizedResponse extends ApiErrorResponse {
  statusCode: 401;
  error: 'Unauthorized';
}

export interface ForbiddenResponse extends ApiErrorResponse {
  statusCode: 403;
  error: 'Forbidden';
}

export interface NotFoundResponse extends ApiErrorResponse {
  statusCode: 404;
  error: 'Not Found';
}

export interface ConflictResponse extends ApiErrorResponse {
  statusCode: 409;
  error: 'Conflict';
}

export interface ValidationErrorResponse extends BadRequestResponse {
  validationErrors: ValidationError[];
}

export interface InternalServerErrorResponse extends ApiErrorResponse {
  statusCode: 500;
  error: 'Internal Server Error';
}
