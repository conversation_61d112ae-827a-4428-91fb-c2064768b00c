import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ResponseBuilderService } from '../services/response-builder.service';
import { ApiResponse } from '../interfaces/api-response.interface';

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ResponseInterceptor.name);

  constructor(private readonly responseBuilder: ResponseBuilderService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    
    const startTime = Date.now();

    return next.handle().pipe(
      map((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Log request completion
        this.logger.log(
          `${request.method} ${request.url} - ${response.statusCode} - ${duration}ms`,
        );

        // If data is already a standardized response, return as-is
        if (this.isStandardizedResponse(data)) {
          return data;
        }

        // If data is null/undefined, create appropriate response
        if (data === null || data === undefined) {
          return this.responseBuilder.success(
            null,
            'Request completed successfully',
            response.statusCode,
            request,
            { duration },
          );
        }

        // Wrap raw data in standardized response
        return this.responseBuilder.success(
          data,
          'Request completed successfully',
          response.statusCode,
          request,
          { duration },
        );
      }),
    );
  }

  /**
   * Check if response is already standardized
   */
  private isStandardizedResponse(data: any): data is ApiResponse {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.success === 'boolean' &&
      typeof data.message === 'string' &&
      typeof data.statusCode === 'number' &&
      typeof data.apiVersion === 'string' &&
      typeof data.timestamp === 'string'
    );
  }
}
