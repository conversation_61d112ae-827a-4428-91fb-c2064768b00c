import { Injectable, Logger } from '@nestjs/common';
import {
  OdooConnectionConfig,
  SearchReadOptions,
} from '../../domain/value-objects/odoo-connection-config';
import { OdooConnectionPoolService } from '../../infrastructure/adapters/odoo/odoo-connection-pool.service';
import { UserContextService } from '../../infrastructure/adapters/odoo/user-context.service';

@Injectable()
export class OdooConnectionUseCase {
  private readonly logger = new Logger(OdooConnectionUseCase.name);

  constructor(
    private readonly connectionPoolService: OdooConnectionPoolService,
    private readonly userContextService: UserContextService,
  ) {}

  async connect(config: OdooConnectionConfig): Promise<void> {
    try {
      // Set user context with Odoo configuration
      this.userContextService.setUserContext(config);

      // Get or create connection from pool
      const adapter = await this.connectionPoolService.getConnection(
        this.userContextService.getUserContext()
      );

      this.logger.log(`Successfully connected to Odoo at ${config.host} for user: ${this.userContextService.getUserId()}`);
    } catch (error) {
      this.logger.error('Failed to connect to Odoo', error);
      throw error;
    }
  }

  async getVersionInfo() {
    try {
      const adapter = await this.getAdapter();
      return adapter.getVersionInfo();
    } catch (error) {
      this.logger.error('Failed to get version info', error);
      throw error;
    }
  }

  async getCapabilities() {
    try {
      const adapter = await this.getAdapter();
      return adapter.getCapabilities();
    } catch (error) {
      this.logger.error('Failed to get capabilities', error);
      throw error;
    }
  }

  async searchRead<T = any>(
    model: string,
    domain?: any[],
    options?: SearchReadOptions,
  ): Promise<T[]> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.searchRead<T>(model, domain, options);
    } catch (error) {
      this.logger.error(`Failed to search ${model}`, error);
      throw error;
    }
  }

  async create<T = any>(model: string, values: Partial<T>): Promise<number> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.create(model, values);
    } catch (error) {
      this.logger.error(`Failed to create ${model}`, error);
      throw error;
    }
  }

  async update<T = any>(
    model: string,
    ids: number[],
    values: Partial<T>,
  ): Promise<boolean> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.write(model, ids, values);
    } catch (error) {
      this.logger.error(`Failed to update ${model}`, error);
      throw error;
    }
  }

  async delete(model: string, ids: number[]): Promise<boolean> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.unlink(model, ids);
    } catch (error) {
      this.logger.error(`Failed to delete ${model}`, error);
      throw error;
    }
  }

  async execute(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): Promise<any> {
    try {
      const adapter = await this.getAdapter();
      return await adapter.execute(model, method, args, kwargs);
    } catch (error) {
      this.logger.error(`Failed to execute ${model}.${method}`, error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.userContextService.hasUserContext()) {
        await this.connectionPoolService.removeConnection(
          this.userContextService.getUserContext()
        );
        this.logger.log(`Disconnected user: ${this.userContextService.getUserId()} from Odoo`);
      }
    } catch (error) {
      this.logger.error('Failed to disconnect from Odoo', error);
      throw error;
    }
  }

  /**
   * Get pool statistics for monitoring
   */
  getPoolStats() {
    return this.connectionPoolService.getPoolStats();
  }

  /**
   * Get detailed pool metrics
   */
  getPoolMetrics() {
    return this.connectionPoolService.getPoolMetrics();
  }

  /**
   * Health check for all connections
   */
  async healthCheck() {
    return await this.connectionPoolService.healthCheck();
  }

  /**
   * Execute batch operations across multiple users
   */
  async executeBatch<T>(
    operations: Array<{
      userContext: any;
      operation: (adapter: any) => Promise<T>;
    }>,
  ) {
    return await this.connectionPoolService.executeBatchOperations(operations);
  }

  /**
   * Cleanup stale connections
   */
  async cleanupStaleConnections(maxAgeMinutes: number = 60) {
    return await this.connectionPoolService.cleanupStaleConnections(maxAgeMinutes);
  }

  /**
   * Refresh expiring connections
   */
  async refreshExpiringConnections(refreshThresholdMinutes: number = 25) {
    return await this.connectionPoolService.refreshExpiringConnections(refreshThresholdMinutes);
  }

  /**
   * Remove all connections for current user
   */
  async disconnectUser(): Promise<void> {
    try {
      if (this.userContextService.hasUserContext()) {
        await this.connectionPoolService.removeUserConnections(
          this.userContextService.getUserId()
        );
        this.logger.log(`Disconnected all connections for user: ${this.userContextService.getUserId()}`);
      }
    } catch (error) {
      this.logger.error('Failed to disconnect user connections', error);
      throw error;
    }
  }

  /**
   * Private helper to get adapter from pool
   */
  private async getAdapter() {
    if (!this.userContextService.hasUserContext()) {
      // Try to find existing connection for this user
      const user = this.extractUserFromRequest();
      const cachedConnection = this.connectionPoolService.findUserConnection(user.id);

      if (cachedConnection) {
        // Use existing connection config to set user context
        this.userContextService.setUserContext(cachedConnection.connectionConfig);
        this.logger.debug(`Restored user context for user: ${user.id}`);
      } else {
        throw new Error('User context not set and no existing connection found. Call connect() first.');
      }
    }

    return await this.connectionPoolService.getConnection(
      this.userContextService.getUserContext()
    );
  }

  /**
   * Extract user from request (similar to UserContextService)
   */
  private extractUserFromRequest() {
    // This should match UserContextService logic exactly
    const request = this.userContextService['request'];

    // Method 1: From authenticated user object
    if (request?.user) {
      return {
        id: request.user.id,
        username: request.user.username,
      };
    }

    // Method 2: From session
    if (request?.session && request.session.user) {
      const sessionUser = request.session.user;
      return {
        id: sessionUser.id,
        username: sessionUser.username,
      };
    }

    // Method 3: From JWT token
    const authHeader = request?.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decoded = this.decodeJWT(token);
        return {
          id: decoded.sub || decoded.userId,
          username: decoded.username,
        };
      } catch (error) {
        // JWT decode failed, continue to next method
      }
    }

    // Method 4: From custom headers
    const userId = request?.headers?.['x-user-id'] as string;
    const username = request?.headers?.['x-username'] as string;

    if (userId && username) {
      return { id: userId, username };
    }

    // Fallback for development - use same logic as UserContextService
    if (process.env.NODE_ENV === 'development') {
      const ip = request?.ip || request?.connection?.remoteAddress || 'unknown';
      const userAgent = request?.headers?.['user-agent'] || 'unknown';

      // Create a simple hash of IP + User Agent for consistent anonymous ID
      const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substr(0, 8);
      const anonymousId = `anon_${hash}`;

      return { id: anonymousId, username: 'anonymous' };
    }

    throw new Error('No user found in request');
  }

  /**
   * Decode JWT token (simplified version)
   */
  private decodeJWT(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = Buffer.from(payload, 'base64').toString('utf-8');
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }
}
