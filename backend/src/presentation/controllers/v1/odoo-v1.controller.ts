import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  HttpException,
  UseGuards,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiSecurity,
  ApiConsumes,
  ApiProduces,
  ApiExtraModels,
  getSchemaPath
} from '@nestjs/swagger';
import { OdooConnectionUseCase } from '../../../application/use-cases/odoo-connection.use-case';
import {
  OdooConnectionDto,
  SearchReadDto,
  CreateRecordDto,
  UpdateRecordDto,
  DeleteRecordDto,
} from '../../../application/dtos/odoo-connection.dto';
import { JwtAuthService } from '../../../infrastructure/auth/jwt.service';
import { JwtAuthGuard, Public } from '../../../infrastructure/auth/jwt-auth.guard';
import { ResponseBuilderService } from '../../../common/services/response-builder.service';
import {
  SwaggerSuccessResponseExample,
  SwaggerErrorResponseExample,
  SwaggerConnectResponseExample,
  SwaggerSearchResponseExample
} from '../../../common/dto/swagger-examples.dto';

@ApiTags('Authentication', 'Data Operations', 'Advanced Operations', 'Connection Management')
@Controller({ path: 'odoo', version: '1' })
@UseGuards(JwtAuthGuard)
@ApiExtraModels(
  SwaggerSuccessResponseExample,
  SwaggerErrorResponseExample,
  SwaggerConnectResponseExample,
  SwaggerSearchResponseExample,
  OdooConnectionDto,
  SearchReadDto,
  CreateRecordDto,
  UpdateRecordDto,
  DeleteRecordDto
)
export class OdooV1Controller {
  constructor(
    private readonly odooConnectionUseCase: OdooConnectionUseCase,
    private readonly jwtAuthService: JwtAuthService,
    private readonly responseBuilder: ResponseBuilderService,
  ) {}



  @Post('connect')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '🔐 Connect to Odoo Instance',
    description: `
**Establishes a secure connection to an Odoo instance with intelligent auto-detection.**

### Features:
- 🤖 **Auto-detection**: Automatically detects Odoo version and selects optimal protocol
- 🔄 **Multi-protocol**: Supports XML-RPC, JSON-RPC, and REST API
- 🔐 **Secure**: Returns JWT token for subsequent authenticated requests
- 📊 **Comprehensive**: Provides detailed version and capability information

### Supported Odoo Versions:
- Odoo 13.0+ (Community & Enterprise)
- Odoo 15.0+ (Community & Enterprise)
- Odoo 17.0+ (Community & Enterprise)
- Odoo 18.0+ (Community & Enterprise)

### Usage:
1. Call this endpoint with your Odoo credentials
2. Save the returned JWT token
3. Use the token in the \`Authorization: Bearer <token>\` header for all subsequent API calls

### Security:
- Passwords are encrypted using AES-256-GCM before storage
- JWT tokens expire after 24 hours (configurable)
- Connection pooling ensures efficient resource usage
    `,
    tags: ['Authentication']
  })
  @ApiConsumes('application/json')
  @ApiProduces('application/json')
  @ApiBody({
    type: OdooConnectionDto,
    description: 'Odoo connection parameters',
    examples: {
      'Odoo 18 Enterprise': {
        summary: 'Connect to Odoo 18 Enterprise',
        description: 'Example connection to Odoo 18 Enterprise instance',
        value: {
          host: 'https://odoo18.bestmix.one/',
          database: 'bestmix_27_6',
          username: 'tuan.le',
          password: 'your_password_here',
          protocol: 'https',
          port: 443
        }
      },
      'Odoo 17 Community': {
        summary: 'Connect to Odoo 17 Community',
        description: 'Example connection to Odoo 17 Community instance',
        value: {
          host: 'https://demo.odoo.com',
          database: 'demo_db',
          username: 'admin',
          password: 'admin',
          protocol: 'https',
          port: 443
        }
      },
      'Local Development': {
        summary: 'Connect to Local Odoo',
        description: 'Example connection to local development instance',
        value: {
          host: 'http://localhost:8069',
          database: 'odoo_dev',
          username: 'admin',
          password: 'admin',
          protocol: 'http',
          port: 8069
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '✅ Successfully connected to Odoo',
    type: SwaggerConnectResponseExample,
    content: {
      'application/json': {
        examples: {
          'Odoo 18 Enterprise': {
            summary: 'Successful connection to Odoo 18 Enterprise',
            value: {
              success: true,
              data: {
                token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhbm9uX09qb3hPbU4xIiwib2Rvb0luc3RhbmNlSWQiOiJvZG9vX2FIUjBjSE02THk5diIsIm9kb29Ib3N0IjoiaHR0cHM6Ly9vZG9vMTguYmVzdG1peC5vbmUvIiwib2Rvb0RhdGFiYXNlIjoiYmVzdG1peF8yN182Iiwib2Rvb1VzZXJuYW1lIjoidHVhbi5sZSIsInNlc3Npb25JZCI6InNlc3Npb25fMTc1MzUxNDEwODk2MF81cWFnczI2cXkiLCJpYXQiOjE3NTM1MTQxMDgsImV4cCI6MTc1MzYwMDUwOCwiYXVkIjoib2Rvby1jbGllbnQiLCJpc3MiOiJ1bml2ZXJzYWwtb2Rvby1hZGFwdGVyIn0.U0-7un8Veo4f7Jxb1h-z6u1xUb2bfFg5h1FKu3JJx3Q',
                expiresIn: '24h',
                version: {
                  major: 18,
                  minor: 0,
                  patch: 0,
                  series: '18.0',
                  edition: 'enterprise',
                  serverVersion: '18.0+e',
                  protocolVersion: 1
                },
                capabilities: {
                  hasJsonRpc: true,
                  hasRestApi: false,
                  hasGraphQL: false,
                  hasWebSocket: true,
                  hasTokenAuth: true,
                  hasOAuth2: true,
                  maxBatchSize: 1000,
                  supportedAuthMethods: ['password', 'api_key', 'oauth2', 'token']
                }
              },
              message: 'Connected to Odoo successfully',
              statusCode: 200,
              apiVersion: 'v1',
              timestamp: '2025-07-26T07:23:53.901Z',
              path: '/api/v1/odoo/connect',
              meta: {
                connectionTime: 995,
                odooVersion: '18.0',
                protocol: 'JSON-RPC'
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '❌ Invalid connection parameters',
    type: SwaggerErrorResponseExample,
    content: {
      'application/json': {
        examples: {
          'Validation Error': {
            summary: 'Invalid request parameters',
            value: {
              success: false,
              error: 'Validation Error',
              message: 'Invalid connection parameters provided',
              statusCode: 400,
              apiVersion: 'v1',
              timestamp: '2025-07-26T07:23:53.901Z',
              path: '/api/v1/odoo/connect',
              errorCode: 'VALIDATION_FAILED',
              validationErrors: [
                {
                  field: 'host',
                  message: 'Host must be a valid URL',
                  value: 'invalid-url',
                  rule: 'isUrl'
                }
              ]
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '💥 Connection failed',
    type: SwaggerErrorResponseExample,
    content: {
      'application/json': {
        examples: {
          'Connection Failed': {
            summary: 'Failed to connect to Odoo',
            value: {
              success: false,
              error: 'Internal Server Error',
              message: 'Failed to connect to Odoo: Connection timeout',
              statusCode: 500,
              apiVersion: 'v1',
              timestamp: '2025-07-26T07:23:53.901Z',
              path: '/api/v1/odoo/connect',
              errorCode: 'CONNECTION_FAILED',
              debug: {
                connectionTime: 30000,
                odooHost: 'https://invalid-host.com',
                odooDatabase: 'test_db'
              }
            }
          }
        }
      }
    }
  })
  async connect(@Body() connectionDto: OdooConnectionDto, @Req() request: Request) {
    const startTime = Date.now();

    try {
      await this.odooConnectionUseCase.connect(connectionDto);

      // Get connection info for response
      const version = await this.odooConnectionUseCase.getVersionInfo();
      const capabilities = await this.odooConnectionUseCase.getCapabilities();

      // Generate consistent user ID using same logic as UserContextService
      // Extract IP and User Agent from request
      const ip = request.ip || 'unknown';
      const userAgent = request.headers?.['user-agent'] || 'unknown';
      const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substring(0, 8);
      const webUserId = `anon_${hash}`;

      // Generate instance ID
      const instanceHash = Buffer.from(`${connectionDto.host}:${connectionDto.database}:${connectionDto.username}`)
        .toString('base64')
        .replace(/[^a-zA-Z0-9]/g, '')
        .substring(0, 12);
      const odooInstanceId = `odoo_${instanceHash}`;

      // Generate JWT token
      const tokenData = await this.jwtAuthService.generateToken({
        sub: webUserId,
        odooInstanceId: odooInstanceId,
        odooHost: connectionDto.host,
        odooDatabase: connectionDto.database,
        odooUsername: connectionDto.username,
        sessionId: this.jwtAuthService.generateSessionId(),
      });

      return this.responseBuilder.success(
        {
          token: tokenData.token,
          expiresIn: tokenData.expiresIn,
          version,
          capabilities,
        },
        'Connected to Odoo successfully',
        200,
        request,
        {
          connectionTime: Date.now() - startTime,
          odooVersion: version?.series || 'unknown',
          protocol: capabilities?.hasJsonRpc ? 'JSON-RPC' : 'XML-RPC',
        },
      );
    } catch (error) {
      throw new HttpException(
        this.responseBuilder.internalError(
          'Failed to connect to Odoo: ' + error.message,
          request,
          'CONNECTION_FAILED',
          {
            connectionTime: Date.now() - startTime,
            odooHost: connectionDto.host,
            odooDatabase: connectionDto.database,
          },
        ),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Search records in Odoo model
   */
  @Post(':model/search')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: '🔍 Search Records in Odoo Model',
    description: `
**Search and retrieve records from any Odoo model with advanced filtering.**

### Features:
- 🎯 **Flexible Filtering**: Use Odoo domain syntax for complex queries
- 📊 **Field Selection**: Choose specific fields to retrieve
- 📄 **Pagination**: Built-in offset and limit support
- 🔄 **Sorting**: Order results by any field
- ⚡ **Performance**: Optimized queries with connection pooling

### Domain Syntax Examples:
\`\`\`python
# Simple equality
[['name', '=', 'John Doe']]

# Multiple conditions (AND)
[['active', '=', True], ['email', '!=', False]]

# OR conditions
['|', ['name', 'ilike', 'john'], ['email', 'ilike', 'john']]

# Date ranges
[['create_date', '>=', '2024-01-01'], ['create_date', '<', '2024-12-31']]
\`\`\`
    `,
    tags: ['Data Operations']
  })
  @ApiConsumes('application/json')
  @ApiProduces('application/json')
  @ApiParam({
    name: 'model',
    description: 'Odoo model name (e.g., res.users, res.partner, product.product)',
    example: 'res.users'
  })
  @ApiBody({
    type: SearchReadDto,
    description: 'Search parameters with domain, fields, pagination, and sorting',
    examples: {
      'Search Users': {
        summary: 'Search active users',
        value: {
          domain: [['active', '=', true]],
          fields: ['name', 'login', 'email'],
          offset: 0,
          limit: 10,
          order: 'name asc'
        }
      },
      'Search Companies': {
        summary: 'Search company partners',
        value: {
          domain: [['is_company', '=', true]],
          fields: ['name', 'email', 'phone', 'website'],
          offset: 0,
          limit: 20,
          order: 'name asc'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '✅ Records retrieved successfully',
    type: SwaggerSearchResponseExample
  })
  @ApiResponse({
    status: 401,
    description: '🔒 Authentication required',
    type: SwaggerErrorResponseExample
  })
  @ApiResponse({
    status: 404,
    description: '❌ Model not found',
    type: SwaggerErrorResponseExample
  })
  async searchRecords(
    @Param('model') model: string,
    @Body() searchDto: SearchReadDto,
  ) {
    const result = await this.odooConnectionUseCase.searchRead(
      model,
      searchDto.domain || [],
      {
        fields: searchDto.fields || [],
        offset: searchDto.offset || 0,
        limit: searchDto.limit || 100,
        order: searchDto.order || '',
      }
    );

    return this.responseBuilder.success(
      result,
      `Search completed successfully. Found ${result.length} records.`,
      200,
      undefined,
      {
        recordCount: result.length,
        model,
        searchCriteria: {
          domain: searchDto.domain,
          fields: searchDto.fields,
          offset: searchDto.offset,
          limit: searchDto.limit,
          order: searchDto.order
        }
      }
    );
  }

  /**
   * Get user Odoo instances
   */
  @Get('instances')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: '📋 Get User Odoo Instances',
    description: `
**Retrieve all Odoo instances connected by the current user.**

### Features:
- 📊 **Instance Overview**: List all connected Odoo instances
- 🕒 **Usage Statistics**: Last used timestamps and usage counts
- 🔄 **Connection Status**: Active/inactive status for each instance
- 📈 **Performance Metrics**: Connection health and response times

### Use Cases:
- Dashboard displaying user's connected instances
- Instance management and monitoring
- Connection health checks
- Usage analytics and reporting
    `,
    tags: ['Connection Management']
  })
  @ApiProduces('application/json')
  @ApiResponse({
    status: 200,
    description: '✅ User instances retrieved successfully',
    content: {
      'application/json': {
        examples: {
          'Multiple Instances': {
            summary: 'User with multiple Odoo instances',
            value: {
              success: true,
              data: [
                {
                  instanceId: 'odoo_aHR0cHM6Ly9v',
                  host: 'https://odoo18.bestmix.one/',
                  database: 'bestmix_27_6',
                  username: 'tuan.le',
                  isActive: true,
                  lastUsed: '2025-07-26T07:35:11.872Z'
                },
                {
                  instanceId: 'odoo_bG9jYWxob3N0',
                  host: 'http://localhost:8069',
                  database: 'odoo_dev',
                  username: 'admin',
                  isActive: true,
                  lastUsed: '2025-07-25T15:20:30.123Z'
                }
              ],
              message: 'User instances retrieved successfully',
              statusCode: 200,
              apiVersion: 'v1',
              timestamp: '2025-07-26T07:35:11.872Z',
              path: '/api/v1/odoo/instances',
              meta: { instanceCount: 2, activeInstances: 2 }
            }
          },
          'No Instances': {
            summary: 'User with no connected instances',
            value: {
              success: true,
              data: [],
              message: 'No Odoo instances found. Connect to an instance first.',
              statusCode: 200,
              apiVersion: 'v1',
              timestamp: '2025-07-26T07:35:11.872Z',
              path: '/api/v1/odoo/instances',
              meta: { instanceCount: 0, activeInstances: 0 }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: '🔒 Authentication required',
    type: SwaggerErrorResponseExample
  })
  async getUserInstances() {
    const instances = await this.odooConnectionUseCase.getUserOdooInstances();

    return this.responseBuilder.success(
      instances,
      instances.length > 0
        ? 'User instances retrieved successfully'
        : 'No Odoo instances found. Connect to an instance first.',
      200,
      undefined,
      {
        instanceCount: instances.length,
        activeInstances: instances.filter(i => i.isActive).length
      }
    );
  }

  @Get('version')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: '📋 Get Odoo Version Information',
    description: `
**Returns detailed version information of the connected Odoo instance.**

### Features:
- 🏷️ **Version Details**: Major, minor, patch versions
- 🏢 **Edition Info**: Community vs Enterprise edition
- 🔌 **Protocol Info**: Supported protocols and capabilities
- 🚀 **Performance**: Server response times and health

### Use Cases:
- Version compatibility checks
- Feature availability detection
- Protocol selection optimization
- System monitoring and diagnostics
    `,
    tags: ['Connection Management']
  })
  @ApiProduces('application/json')
  @ApiResponse({
    status: 200,
    description: 'Version information retrieved',
    schema: {
      type: 'object',
      properties: {
        major: { type: 'number', example: 18 },
        minor: { type: 'number', example: 0 },
        patch: { type: 'number', example: 0 },
        series: { type: 'string', example: '18.0' },
        edition: { type: 'string', example: 'enterprise' },
        serverVersion: { type: 'string', example: '********.3' },
        protocolVersion: { type: 'number', example: 1 },
      },
    },
  })
  async getVersion() {
    return await this.odooConnectionUseCase.getVersionInfo();
  }

  @Get('capabilities')
  @ApiOperation({
    summary: 'Get Odoo capabilities',
    description:
      'Returns the capabilities and features available in the connected Odoo instance',
  })
  @ApiResponse({
    status: 200,
    description: 'Capabilities retrieved',
    schema: {
      type: 'object',
      properties: {
        hasJsonRpc: { type: 'boolean', example: true },
        hasRestApi: { type: 'boolean', example: true },
        hasGraphQL: { type: 'boolean', example: false },
        hasWebSocket: { type: 'boolean', example: true },
        hasTokenAuth: { type: 'boolean', example: true },
        hasOAuth2: { type: 'boolean', example: true },
        maxBatchSize: { type: 'number', example: 1000 },
        supportedAuthMethods: {
          type: 'array',
          items: { type: 'string' },
          example: ['password', 'api_key', 'oauth2', 'token'],
        },
      },
    },
  })
  async getCapabilities() {
    return await this.odooConnectionUseCase.getCapabilities();
  }

  @Post(':model/search')
  @ApiOperation({
    summary: 'Search and read records from Odoo model',
    description:
      'Performs search_read operation on the specified Odoo model with domain filtering and field selection',
  })
  @ApiParam({
    name: 'model',
    description:
      'Odoo model name (e.g., res.partner, sale.order, product.product)',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 200,
    description: 'Records retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          name: { type: 'string', example: 'Partner Name' },
          email: { type: 'string', example: '<EMAIL>' },
        },
      },
    },
  })
  async searchRead(
    @Param('model') model: string,
    @Body() searchDto: SearchReadDto,
  ) {
    const { domain, fields, limit, offset, order } = searchDto;
    return await this.odooConnectionUseCase.searchRead(model, domain, {
      fields,
      limit,
      offset,
      order,
    });
  }

  @Post(':model')
  @ApiOperation({
    summary: 'Create a new record in Odoo model',
    description: 'Creates a new record in the specified Odoo model',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 201,
    description: 'Record created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        id: { type: 'number', example: 123 },
        message: { type: 'string', example: 'Record created successfully' },
      },
    },
  })
  async create(
    @Param('model') model: string,
    @Body() createDto: CreateRecordDto,
  ) {
    const recordId = await this.odooConnectionUseCase.create(
      model,
      createDto.values,
    );
    return {
      success: true,
      id: recordId,
      message: 'Record created successfully',
      apiVersion: 'v1',
    };
  }

  @Put(':model')
  @ApiOperation({
    summary: 'Update records in Odoo model',
    description: 'Updates existing records in the specified Odoo model',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 200,
    description: 'Records updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Records updated successfully' },
      },
    },
  })
  async update(
    @Param('model') model: string,
    @Body() updateDto: UpdateRecordDto,
  ) {
    const success = await this.odooConnectionUseCase.update(
      model,
      updateDto.ids,
      updateDto.values,
    );
    return {
      success,
      message: success ? 'Records updated successfully' : 'Update failed',
      apiVersion: 'v1',
    };
  }

  @Delete(':model')
  @ApiOperation({
    summary: 'Delete records from Odoo model',
    description: 'Deletes records from the specified Odoo model',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 200,
    description: 'Records deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Records deleted successfully' },
      },
    },
  })
  async delete(
    @Param('model') model: string,
    @Body() deleteDto: DeleteRecordDto,
  ) {
    const success = await this.odooConnectionUseCase.delete(
      model,
      deleteDto.ids,
    );
    return {
      success,
      message: success ? 'Records deleted successfully' : 'Delete failed',
      apiVersion: 'v1',
    };
  }

  @Post(':model/execute/:method')
  @ApiOperation({
    summary: 'Execute custom method on Odoo model',
    description:
      'Executes a custom method on the specified Odoo model with arguments and keyword arguments',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiParam({
    name: 'method',
    description: 'Method name to execute',
    example: 'search_count',
  })
  @ApiResponse({
    status: 200,
    description: 'Method executed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        result: { description: 'Method execution result' },
        message: { type: 'string', example: 'Method executed successfully' },
      },
    },
  })
  async execute(
    @Param('model') model: string,
    @Param('method') method: string,
    @Body() body: { args?: any[]; kwargs?: any },
  ) {
    const { args = [], kwargs = {} } = body;
    const result = await this.odooConnectionUseCase.execute(
      model,
      method,
      args,
      kwargs,
    );
    return {
      success: true,
      result,
      message: 'Method executed successfully',
      apiVersion: 'v1',
    };
  }

  @Post('disconnect')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disconnect from Odoo instance',
    description:
      'Closes the current connection to the Odoo instance for this user session',
  })
  @ApiResponse({
    status: 200,
    description: 'Disconnected successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Disconnected from Odoo successfully',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async disconnect() {
    await this.odooConnectionUseCase.disconnect();
    return {
      success: true,
      message: 'Disconnected from Odoo successfully',
      apiVersion: 'v1',
    };
  }

  @Post('disconnect-all')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disconnect all user sessions',
    description:
      'Disconnect all Odoo connections for the current user across all instances',
  })
  @ApiResponse({
    status: 200,
    description: 'All user sessions disconnected successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'All user sessions disconnected successfully',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async disconnectAll() {
    await this.odooConnectionUseCase.disconnectUser();
    return {
      success: true,
      message: 'All user sessions disconnected successfully',
      apiVersion: 'v1',
    };
  }



  @Post('instances/:instanceId/disconnect')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disconnect from specific Odoo instance',
    description: 'Disconnect from a specific Odoo instance',
  })
  @ApiParam({
    name: 'instanceId',
    description: 'Odoo instance ID',
    example: 'odoo_abc123',
  })
  @ApiResponse({
    status: 200,
    description: 'Disconnected from instance successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Disconnected from instance successfully',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async disconnectInstance(@Param('instanceId') instanceId: string) {
    await this.odooConnectionUseCase.disconnectInstance(instanceId);
    return {
      success: true,
      message: 'Disconnected from instance successfully',
      apiVersion: 'v1',
    };
  }

  @Post('pool/cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cleanup stale connections',
    description: 'Remove connections that have been idle for too long',
  })
  @ApiResponse({
    status: 200,
    description: 'Stale connections cleaned up',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        cleanedCount: { type: 'number', example: 3 },
        message: { type: 'string', example: 'Cleaned up 3 stale connections' },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async cleanupPool() {
    try {
      const cleanedCount =
        await this.odooConnectionUseCase.cleanupStaleConnections();
      return {
        success: true,
        cleanedCount,
        message: `Cleaned up ${cleanedCount} stale connections`,
        apiVersion: 'v1',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to cleanup pool',
          error: error instanceof Error ? error.message : 'Unknown error',
          apiVersion: 'v1',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('pool/refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Refresh expiring connections',
    description: 'Proactively refresh connections that are about to expire',
  })
  @ApiResponse({
    status: 200,
    description: 'Expiring connections refreshed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        refreshedCount: { type: 'number', example: 2 },
        message: {
          type: 'string',
          example: 'Refreshed 2 expiring connections',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async refreshPool() {
    try {
      const refreshedCount =
        await this.odooConnectionUseCase.refreshExpiringConnections();
      return {
        success: true,
        refreshedCount,
        message: `Refreshed ${refreshedCount} expiring connections`,
        apiVersion: 'v1',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to refresh pool',
          error: error instanceof Error ? error.message : 'Unknown error',
          apiVersion: 'v1',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
