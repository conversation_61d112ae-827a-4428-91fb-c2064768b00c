import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  HttpException,
  UseGuards,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { OdooConnectionUseCase } from '../../../application/use-cases/odoo-connection.use-case';
import {
  OdooConnectionDto,
  SearchReadDto,
  CreateRecordDto,
  UpdateRecordDto,
  DeleteRecordDto,
} from '../../../application/dtos/odoo-connection.dto';
import { JwtAuthService } from '../../../infrastructure/auth/jwt.service';
import { JwtAuthGuard, Public } from '../../../infrastructure/auth/jwt-auth.guard';
import { ResponseBuilderService } from '../../../common/services/response-builder.service';

@ApiTags('Odoo Integration v1')
@Controller({ path: 'odoo', version: '1' })
@UseGuards(JwtAuthGuard)
export class OdooV1Controller {
  constructor(
    private readonly odooConnectionUseCase: OdooConnectionUseCase,
    private readonly jwtAuthService: JwtAuthService,
    private readonly responseBuilder: ResponseBuilderService,
  ) {}



  @Post('connect')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Connect to Odoo instance',
    description:
      'Establishes connection to an Odoo instance with automatic version detection and protocol selection. Returns JWT token for subsequent API calls.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully connected to Odoo',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Connected to Odoo successfully' },
        token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
        expiresIn: { type: 'string', example: '24h' },
        version: {
          type: 'object',
          properties: {
            major: { type: 'number', example: 18 },
            minor: { type: 'number', example: 0 },
            series: { type: 'string', example: '18.0' },
            edition: { type: 'string', example: 'enterprise' },
          },
        },
        capabilities: {
          type: 'object',
          properties: {
            hasJsonRpc: { type: 'boolean', example: true },
            hasRestApi: { type: 'boolean', example: true },
            maxBatchSize: { type: 'number', example: 1000 },
            supportedAuthMethods: {
              type: 'array',
              items: { type: 'string' },
              example: ['password', 'api_key', 'oauth2'],
            },
          },
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid connection parameters' })
  @ApiResponse({ status: 500, description: 'Connection failed' })
  async connect(@Body() connectionDto: OdooConnectionDto, @Req() request: Request) {
    const startTime = Date.now();

    try {
      await this.odooConnectionUseCase.connect(connectionDto);

      // Get connection info for response
      const version = await this.odooConnectionUseCase.getVersionInfo();
      const capabilities = await this.odooConnectionUseCase.getCapabilities();

      // Generate consistent user ID using same logic as UserContextService
      // Extract IP and User Agent from request
      const ip = request.ip || 'unknown';
      const userAgent = request.headers?.['user-agent'] || 'unknown';
      const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substring(0, 8);
      const webUserId = `anon_${hash}`;

      // Generate instance ID
      const instanceHash = Buffer.from(`${connectionDto.host}:${connectionDto.database}:${connectionDto.username}`)
        .toString('base64')
        .replace(/[^a-zA-Z0-9]/g, '')
        .substring(0, 12);
      const odooInstanceId = `odoo_${instanceHash}`;

      // Generate JWT token
      const tokenData = await this.jwtAuthService.generateToken({
        sub: webUserId,
        odooInstanceId: odooInstanceId,
        odooHost: connectionDto.host,
        odooDatabase: connectionDto.database,
        odooUsername: connectionDto.username,
        sessionId: this.jwtAuthService.generateSessionId(),
      });

      return this.responseBuilder.success(
        {
          token: tokenData.token,
          expiresIn: tokenData.expiresIn,
          version,
          capabilities,
        },
        'Connected to Odoo successfully',
        200,
        request,
        {
          connectionTime: Date.now() - startTime,
          odooVersion: version?.series || 'unknown',
          protocol: capabilities?.hasJsonRpc ? 'JSON-RPC' : 'XML-RPC',
        },
      );
    } catch (error) {
      throw new HttpException(
        this.responseBuilder.internalError(
          'Failed to connect to Odoo: ' + error.message,
          request,
          'CONNECTION_FAILED',
          {
            connectionTime: Date.now() - startTime,
            odooHost: connectionDto.host,
            odooDatabase: connectionDto.database,
          },
        ),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('version')
  @ApiOperation({
    summary: 'Get Odoo version information',
    description:
      'Returns detailed version information of the connected Odoo instance',
  })
  @ApiResponse({
    status: 200,
    description: 'Version information retrieved',
    schema: {
      type: 'object',
      properties: {
        major: { type: 'number', example: 18 },
        minor: { type: 'number', example: 0 },
        patch: { type: 'number', example: 0 },
        series: { type: 'string', example: '18.0' },
        edition: { type: 'string', example: 'enterprise' },
        serverVersion: { type: 'string', example: '********.3' },
        protocolVersion: { type: 'number', example: 1 },
      },
    },
  })
  async getVersion() {
    return await this.odooConnectionUseCase.getVersionInfo();
  }

  @Get('capabilities')
  @ApiOperation({
    summary: 'Get Odoo capabilities',
    description:
      'Returns the capabilities and features available in the connected Odoo instance',
  })
  @ApiResponse({
    status: 200,
    description: 'Capabilities retrieved',
    schema: {
      type: 'object',
      properties: {
        hasJsonRpc: { type: 'boolean', example: true },
        hasRestApi: { type: 'boolean', example: true },
        hasGraphQL: { type: 'boolean', example: false },
        hasWebSocket: { type: 'boolean', example: true },
        hasTokenAuth: { type: 'boolean', example: true },
        hasOAuth2: { type: 'boolean', example: true },
        maxBatchSize: { type: 'number', example: 1000 },
        supportedAuthMethods: {
          type: 'array',
          items: { type: 'string' },
          example: ['password', 'api_key', 'oauth2', 'token'],
        },
      },
    },
  })
  async getCapabilities() {
    return await this.odooConnectionUseCase.getCapabilities();
  }

  @Post(':model/search')
  @ApiOperation({
    summary: 'Search and read records from Odoo model',
    description:
      'Performs search_read operation on the specified Odoo model with domain filtering and field selection',
  })
  @ApiParam({
    name: 'model',
    description:
      'Odoo model name (e.g., res.partner, sale.order, product.product)',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 200,
    description: 'Records retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number', example: 1 },
          name: { type: 'string', example: 'Partner Name' },
          email: { type: 'string', example: '<EMAIL>' },
        },
      },
    },
  })
  async searchRead(
    @Param('model') model: string,
    @Body() searchDto: SearchReadDto,
  ) {
    const { domain, fields, limit, offset, order } = searchDto;
    return await this.odooConnectionUseCase.searchRead(model, domain, {
      fields,
      limit,
      offset,
      order,
    });
  }

  @Post(':model')
  @ApiOperation({
    summary: 'Create a new record in Odoo model',
    description: 'Creates a new record in the specified Odoo model',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 201,
    description: 'Record created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        id: { type: 'number', example: 123 },
        message: { type: 'string', example: 'Record created successfully' },
      },
    },
  })
  async create(
    @Param('model') model: string,
    @Body() createDto: CreateRecordDto,
  ) {
    const recordId = await this.odooConnectionUseCase.create(
      model,
      createDto.values,
    );
    return {
      success: true,
      id: recordId,
      message: 'Record created successfully',
      apiVersion: 'v1',
    };
  }

  @Put(':model')
  @ApiOperation({
    summary: 'Update records in Odoo model',
    description: 'Updates existing records in the specified Odoo model',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 200,
    description: 'Records updated successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Records updated successfully' },
      },
    },
  })
  async update(
    @Param('model') model: string,
    @Body() updateDto: UpdateRecordDto,
  ) {
    const success = await this.odooConnectionUseCase.update(
      model,
      updateDto.ids,
      updateDto.values,
    );
    return {
      success,
      message: success ? 'Records updated successfully' : 'Update failed',
      apiVersion: 'v1',
    };
  }

  @Delete(':model')
  @ApiOperation({
    summary: 'Delete records from Odoo model',
    description: 'Deletes records from the specified Odoo model',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiResponse({
    status: 200,
    description: 'Records deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Records deleted successfully' },
      },
    },
  })
  async delete(
    @Param('model') model: string,
    @Body() deleteDto: DeleteRecordDto,
  ) {
    const success = await this.odooConnectionUseCase.delete(
      model,
      deleteDto.ids,
    );
    return {
      success,
      message: success ? 'Records deleted successfully' : 'Delete failed',
      apiVersion: 'v1',
    };
  }

  @Post(':model/execute/:method')
  @ApiOperation({
    summary: 'Execute custom method on Odoo model',
    description:
      'Executes a custom method on the specified Odoo model with arguments and keyword arguments',
  })
  @ApiParam({
    name: 'model',
    description: 'Odoo model name',
    example: 'res.partner',
  })
  @ApiParam({
    name: 'method',
    description: 'Method name to execute',
    example: 'search_count',
  })
  @ApiResponse({
    status: 200,
    description: 'Method executed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        result: { description: 'Method execution result' },
        message: { type: 'string', example: 'Method executed successfully' },
      },
    },
  })
  async execute(
    @Param('model') model: string,
    @Param('method') method: string,
    @Body() body: { args?: any[]; kwargs?: any },
  ) {
    const { args = [], kwargs = {} } = body;
    const result = await this.odooConnectionUseCase.execute(
      model,
      method,
      args,
      kwargs,
    );
    return {
      success: true,
      result,
      message: 'Method executed successfully',
      apiVersion: 'v1',
    };
  }

  @Post('disconnect')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disconnect from Odoo instance',
    description:
      'Closes the current connection to the Odoo instance for this user session',
  })
  @ApiResponse({
    status: 200,
    description: 'Disconnected successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Disconnected from Odoo successfully',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async disconnect() {
    await this.odooConnectionUseCase.disconnect();
    return {
      success: true,
      message: 'Disconnected from Odoo successfully',
      apiVersion: 'v1',
    };
  }

  @Post('disconnect-all')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disconnect all user sessions',
    description:
      'Disconnect all Odoo connections for the current user across all instances',
  })
  @ApiResponse({
    status: 200,
    description: 'All user sessions disconnected successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'All user sessions disconnected successfully',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async disconnectAll() {
    await this.odooConnectionUseCase.disconnectUser();
    return {
      success: true,
      message: 'All user sessions disconnected successfully',
      apiVersion: 'v1',
    };
  }

  @Get('instances')
  @ApiOperation({
    summary: 'Get user Odoo instances',
    description: 'List all Odoo instances connected by the current user',
  })
  @ApiResponse({
    status: 200,
    description: 'User Odoo instances retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        instances: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              instanceId: { type: 'string', example: 'odoo_abc123' },
              host: { type: 'string', example: 'https://odoo18.bestmix.one/' },
              database: { type: 'string', example: 'bestmix_27_6' },
              username: { type: 'string', example: 'tuan.le' },
              isActive: { type: 'boolean', example: true },
              lastUsed: { type: 'string', example: '2025-07-26T06:05:11.000Z' },
            },
          },
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async getUserInstances() {
    const instances = await this.odooConnectionUseCase.getUserOdooInstances();
    return {
      success: true,
      instances,
      apiVersion: 'v1',
    };
  }

  @Post('instances/:instanceId/disconnect')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disconnect from specific Odoo instance',
    description: 'Disconnect from a specific Odoo instance',
  })
  @ApiParam({
    name: 'instanceId',
    description: 'Odoo instance ID',
    example: 'odoo_abc123',
  })
  @ApiResponse({
    status: 200,
    description: 'Disconnected from instance successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Disconnected from instance successfully',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async disconnectInstance(@Param('instanceId') instanceId: string) {
    await this.odooConnectionUseCase.disconnectInstance(instanceId);
    return {
      success: true,
      message: 'Disconnected from instance successfully',
      apiVersion: 'v1',
    };
  }

  @Post('pool/cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cleanup stale connections',
    description: 'Remove connections that have been idle for too long',
  })
  @ApiResponse({
    status: 200,
    description: 'Stale connections cleaned up',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        cleanedCount: { type: 'number', example: 3 },
        message: { type: 'string', example: 'Cleaned up 3 stale connections' },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async cleanupPool() {
    try {
      const cleanedCount =
        await this.odooConnectionUseCase.cleanupStaleConnections();
      return {
        success: true,
        cleanedCount,
        message: `Cleaned up ${cleanedCount} stale connections`,
        apiVersion: 'v1',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to cleanup pool',
          error: error instanceof Error ? error.message : 'Unknown error',
          apiVersion: 'v1',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('pool/refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Refresh expiring connections',
    description: 'Proactively refresh connections that are about to expire',
  })
  @ApiResponse({
    status: 200,
    description: 'Expiring connections refreshed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        refreshedCount: { type: 'number', example: 2 },
        message: {
          type: 'string',
          example: 'Refreshed 2 expiring connections',
        },
        apiVersion: { type: 'string', example: 'v1' },
      },
    },
  })
  async refreshPool() {
    try {
      const refreshedCount =
        await this.odooConnectionUseCase.refreshExpiringConnections();
      return {
        success: true,
        refreshedCount,
        message: `Refreshed ${refreshedCount} expiring connections`,
        apiVersion: 'v1',
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to refresh pool',
          error: error instanceof Error ? error.message : 'Unknown error',
          apiVersion: 'v1',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
