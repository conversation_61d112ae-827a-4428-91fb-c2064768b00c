import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { PerformanceMonitorService } from '../../infrastructure/adapters/odoo/performance-monitor.service';
import { PoolMetrics, HealthCheckResult } from '../../infrastructure/adapters/odoo/odoo-connection-pool.service';
import { OdooConnectionUseCase } from '../../shared/application/use-cases/odoo-connection.use-case';

@ApiTags('API Information')
@Controller('info')
export class ApiVersionController {
  constructor(
    private readonly odooConnectionUseCase: OdooConnectionUseCase,
    private readonly performanceMonitor: PerformanceMonitorService,
  ) {}
  @Get()
  @ApiOperation({ summary: 'Get API information and available versions' })
  @ApiResponse({
    status: 200,
    description: 'API information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Universal Odoo Adapter API' },
        description: { type: 'string' },
        currentVersion: { type: 'string', example: 'v1' },
        availableVersions: {
          type: 'array',
          items: { type: 'string' },
          example: ['v1'],
        },
        endpoints: {
          type: 'object',
          properties: {
            v1: { type: 'string', example: '/api/v1' },
          },
        },
        documentation: {
          type: 'object',
          properties: {
            swagger: { type: 'string', example: '/api/docs' },
            v1: { type: 'string', example: '/api/v1/docs' },
          },
        },
      },
    },
  })
  getApiInfo() {
    return {
      name: 'Universal Odoo Adapter API',
      description:
        'A comprehensive API for connecting to multiple Odoo versions with automatic protocol selection',
      currentVersion: 'v1',
      availableVersions: ['v1'],
      endpoints: {
        v1: '/api/v1',
      },
      documentation: {
        swagger: '/api/docs',
        v1: '/api/v1/docs',
      },
      features: {
        v1: [
          'Multi-version Odoo support (13, 15, 17, 18+)',
          'Multi-protocol support (XML-RPC, JSON-RPC, REST)',
          'Automatic version detection',
          'Intelligent protocol selection',
          'Field mapping between versions',
          'CRUD operations',
          'Custom method execution',
        ],
      },
      compatibility: {
        odooVersions: ['13.0', '15.0', '17.0', '18.0+'],
        protocols: ['XML-RPC', 'JSON-RPC', 'REST API'],
        authMethods: ['Password', 'API Key', 'OAuth2', 'Token'],
      },
    };
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'API is healthy' })
  getHealth() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: 'v1',
      uptime: process.uptime(),
    };
  }

  @Get('pool-stats')
  @ApiOperation({ summary: 'Get Odoo connection pool statistics' })
  @ApiResponse({
    status: 200,
    description: 'Connection pool statistics',
    schema: {
      type: 'object',
      properties: {
        size: { type: 'number', example: 5 },
        maxSize: { type: 'number', example: 100 },
        connections: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              key: { type: 'string', example: 'user123:odoo.example.com:database:username' },
              lastUsed: { type: 'string', example: '2025-01-26T10:00:00.000Z' },
              host: { type: 'string', example: 'odoo.example.com' },
              database: { type: 'string', example: 'production_db' },
            },
          },
        },
      },
    },
  })
  getPoolStats() {
    return this.odooConnectionUseCase.getPoolStats();
  }

  @Get('pool-metrics')
  @ApiOperation({ summary: 'Get detailed connection pool metrics' })
  @ApiResponse({
    status: 200,
    description: 'Detailed pool metrics for monitoring',
    schema: {
      type: 'object',
      properties: {
        size: { type: 'number', example: 5 },
        maxSize: { type: 'number', example: 100 },
        utilizationPercent: { type: 'number', example: 5.0 },
        averageAgeMinutes: { type: 'number', example: 15.5 },
        oldestConnectionMinutes: { type: 'number', example: 25.0 },
        newestConnectionMinutes: { type: 'number', example: 2.0 },
      },
    },
  })
  getPoolMetrics() {
    return this.odooConnectionUseCase.getPoolMetrics();
  }

  @Get('pool-health')
  @ApiOperation({ summary: 'Health check for all connections in pool' })
  @ApiResponse({
    status: 200,
    description: 'Health status of all connections',
    schema: {
      type: 'object',
      properties: {
        totalConnections: { type: 'number', example: 5 },
        healthyConnections: { type: 'number', example: 4 },
        unhealthyConnections: { type: 'number', example: 1 },
        details: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              key: { type: 'string' },
              healthy: { type: 'boolean' },
              lastUsed: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async getPoolHealth() {
    return await this.odooConnectionUseCase.healthCheck();
  }

  @Get('performance')
  @ApiOperation({ summary: 'Get performance statistics for Odoo operations' })
  @ApiQuery({
    name: 'timeWindow',
    required: false,
    description: 'Time window in milliseconds (e.g., 3600000 for last hour)',
    example: 3600000
  })
  @ApiResponse({
    status: 200,
    description: 'Performance statistics',
    schema: {
      type: 'object',
      properties: {
        totalOperations: { type: 'number', example: 150 },
        averageDuration: { type: 'number', example: 245.5 },
        successRate: { type: 'number', example: 98.5 },
        errorCount: { type: 'number', example: 2 },
        slowestOperation: {
          type: 'object',
          properties: {
            operation: { type: 'string', example: 'searchRead' },
            model: { type: 'string', example: 'sale.order' },
            duration: { type: 'number', example: 1250 },
            timestamp: { type: 'string', example: '2025-01-26T10:00:00.000Z' },
          },
        },
        operationCounts: {
          type: 'object',
          example: {
            'searchRead:sale.order': 45,
            'searchRead:res.partner': 32,
            'create:sale.order': 15,
          },
        },
      },
    },
  })
  getPerformanceStats(@Query('timeWindow') timeWindow?: string) {
    const timeWindowMs = timeWindow ? parseInt(timeWindow, 10) : undefined;
    return this.performanceMonitor.getStats(timeWindowMs);
  }

  @Get('performance/errors')
  @ApiOperation({ summary: 'Get recent error details' })
  @ApiQuery({
    name: 'count',
    required: false,
    description: 'Number of recent errors to return',
    example: 10
  })
  @ApiResponse({
    status: 200,
    description: 'Recent error details',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          operation: { type: 'string', example: 'searchRead' },
          model: { type: 'string', example: 'sale.order' },
          duration: { type: 'number', example: 1250 },
          timestamp: { type: 'string', example: '2025-01-26T10:00:00.000Z' },
          error: { type: 'string', example: 'Connection timeout' },
        },
      },
    },
  })
  getRecentErrors(@Query('count') count?: string) {
    const errorCount = count ? parseInt(count, 10) : 10;
    return this.performanceMonitor.getRecentErrors(errorCount);
  }
}
