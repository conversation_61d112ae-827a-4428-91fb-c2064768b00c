import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtAuthService } from './jwt.service';
import { JwtStrategy } from './jwt.strategy';
import { JwtAuthGuard } from './jwt-auth.guard';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const secret = configService.get<string>('JWT_SECRET') || 'default-dev-secret-change-in-production';
        const expiresIn = configService.get<string>('JWT_EXPIRATION') || '24h';
        
        return {
          secret,
          signOptions: {
            expiresIn,
            issuer: 'universal-odoo-adapter',
            audience: 'odoo-client',
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [
    JwtAuthService,
    JwtStrategy,
    JwtAuthGuard,
  ],
  exports: [
    JwtAuthService,
    JwtAuthGuard,
    PassportModule,
  ],
})
export class AuthModule {}
