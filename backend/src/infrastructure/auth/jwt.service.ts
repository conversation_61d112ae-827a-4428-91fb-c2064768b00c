import { Injectable, Logger } from '@nestjs/common';
import { JwtService as NestJwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

export interface JwtPayload {
  sub: string;           // webUserId (unique identifier)
  odooInstanceId: string; // Odoo instance ID
  odooHost: string;      // Odoo host URL
  odooDatabase: string;  // Odoo database name
  odooUsername: string;  // Odoo username
  sessionId: string;     // Unique session ID
  iat?: number;          // Issued at
  exp?: number;          // Expiration time
}

export interface AuthenticatedUser {
  id: string;
  odooInstanceId: string;
  odooHost: string;
  odooDatabase: string;
  odooUsername: string;
  sessionId: string;
}

@Injectable()
export class JwtAuthService {
  private readonly logger = new Logger(JwtAuthService.name);

  constructor(
    private readonly jwtService: NestJwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generate JWT token for authenticated user
   */
  async generateToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): Promise<{
    token: string;
    expiresIn: string;
  }> {
    try {
      const expiresIn = this.configService.get<string>('JWT_EXPIRATION') || '24h';
      
      const token = await this.jwtService.signAsync(payload, {
        expiresIn,
      });

      this.logger.debug(`Generated JWT token for user ${payload.sub}, instance ${payload.odooInstanceId}`);
      
      return {
        token,
        expiresIn,
      };
    } catch (error) {
      this.logger.error('Failed to generate JWT token', error);
      throw new Error('Token generation failed');
    }
  }

  /**
   * Verify and decode JWT token
   */
  async verifyToken(token: string): Promise<JwtPayload> {
    try {
      const payload = await this.jwtService.verifyAsync<JwtPayload>(token);
      
      this.logger.debug(`Verified JWT token for user ${payload.sub}`);
      
      return payload;
    } catch (error) {
      this.logger.error('JWT token verification failed', error);
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Extract user information from JWT payload
   */
  extractUser(payload: JwtPayload): AuthenticatedUser {
    return {
      id: payload.sub,
      odooInstanceId: payload.odooInstanceId,
      odooHost: payload.odooHost,
      odooDatabase: payload.odooDatabase,
      odooUsername: payload.odooUsername,
      sessionId: payload.sessionId,
    };
  }

  /**
   * Generate session ID
   */
  generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if token is expired (without throwing error)
   */
  isTokenExpired(token: string): boolean {
    try {
      const payload = this.jwtService.decode(token) as JwtPayload;
      if (!payload || !payload.exp) {
        return true;
      }
      
      return Date.now() >= payload.exp * 1000;
    } catch (error) {
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(token: string): Date | null {
    try {
      const payload = this.jwtService.decode(token) as JwtPayload;
      if (!payload || !payload.exp) {
        return null;
      }
      
      return new Date(payload.exp * 1000);
    } catch (error) {
      return null;
    }
  }

  /**
   * Refresh token (generate new token with same payload but extended expiration)
   */
  async refreshToken(oldToken: string): Promise<{
    token: string;
    expiresIn: string;
  }> {
    try {
      const payload = await this.verifyToken(oldToken);
      
      // Remove iat and exp from payload for new token
      const { iat, exp, ...tokenPayload } = payload;
      
      return await this.generateToken(tokenPayload);
    } catch (error) {
      this.logger.error('Failed to refresh token', error);
      throw new Error('Token refresh failed');
    }
  }
}
