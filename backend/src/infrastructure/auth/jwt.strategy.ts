import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { JwtPayload, AuthenticatedUser, JwtAuthService } from './jwt.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtAuthService: JwtAuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') || 'default-dev-secret-change-in-production',
    });

    // Warn if using default secret
    const secret = configService.get<string>('JWT_SECRET');
    if (!secret || secret === 'default-dev-secret-change-in-production') {
      this.logger.warn('Using default JWT secret. Set JWT_SECRET environment variable in production!');
    }
  }

  /**
   * Validate JWT payload and return user object
   * This method is called automatically by Passport after JWT verification
   */
  async validate(payload: JwtPayload): Promise<AuthenticatedUser> {
    try {
      // Extract user information from JWT payload
      const user = this.jwtAuthService.extractUser(payload);
      
      this.logger.debug(`JWT validation successful for user ${user.id}, instance ${user.odooInstanceId}`);
      
      return user;
    } catch (error) {
      this.logger.error('JWT payload validation failed', error);
      throw new Error('Invalid token payload');
    }
  }
}
