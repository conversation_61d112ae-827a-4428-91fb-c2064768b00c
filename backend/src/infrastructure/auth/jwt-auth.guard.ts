import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';

export const IS_PUBLIC_KEY = 'isPublic';

/**
 * JWT Authentication Guard
 * Protects endpoints by requiring valid JW<PERSON> token in Authorization header
 */
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private reflector: Reflector) {
    super();
  }

  /**
   * Determine if route should be protected
   */
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // For development mode, allow anonymous access to certain endpoints
    if (process.env.NODE_ENV === 'development') {
      const request = context.switchToHttp().getRequest();
      const path = request.url;
      
      // Allow anonymous access to info endpoints in development
      if (path.includes('/info') || path.includes('/docs')) {
        return true;
      }
    }

    return super.canActivate(context);
  }

  /**
   * Handle authentication errors
   */
  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    
    if (err || !user) {
      const errorMessage = info?.message || err?.message || 'Authentication failed';
      
      this.logger.warn(`Authentication failed for ${request.method} ${request.url}: ${errorMessage}`);
      
      throw new UnauthorizedException({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication token required. Please connect to Odoo first to get a token.',
        statusCode: 401,
        timestamp: new Date().toISOString(),
        path: request.url,
      });
    }

    // Log successful authentication
    this.logger.debug(`Authentication successful for user ${user.id} on ${request.method} ${request.url}`);
    
    return user;
  }
}

/**
 * Decorator to mark routes as public (no authentication required)
 */
import { SetMetadata } from '@nestjs/common';

export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);
