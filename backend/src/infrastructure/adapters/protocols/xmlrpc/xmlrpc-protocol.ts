import { Injectable, Logger } from '@nestjs/common';
import * as xmlrpc from 'xmlrpc';
import { IOdooProtocol } from '../../../../domain/repositories/odoo-adapter.interface';
import {
  OdooConnectionConfig,
  ProtocolType,
  AuthMethod,
} from '../../../../domain/value-objects/odoo-connection-config';

@Injectable()
export class XmlRpcProtocol implements IOdooProtocol {
  private readonly logger = new Logger(XmlRpcProtocol.name);
  readonly type = ProtocolType.XMLRPC;
  readonly supportedMethods = ['execute', 'execute_kw'];

  private objectClient: any;
  private commonClient: any;
  private uid: number | null = null;
  private config: OdooConnectionConfig;

  async connect(config: OdooConnectionConfig): Promise<void> {
    this.config = config;

    // Parse URL to extract protocol, host, and port
    let protocol = 'https';
    let cleanHost = config.host;
    let port = 443;

    if (config.host.startsWith('http://')) {
      protocol = 'http';
      cleanHost = config.host.replace(/^http:\/\//, '');
      port = 80;
    } else if (config.host.startsWith('https://')) {
      protocol = 'https';
      cleanHost = config.host.replace(/^https:\/\//, '');
      port = 443;
    }

    // Remove trailing slash
    cleanHost = cleanHost.replace(/\/$/, '');

    // Override port if specified in config
    if ((config as any).port) {
      port = (config as any).port;
    }

    try {
      // Create common client for authentication
      this.commonClient = xmlrpc.createClient({
        host: cleanHost,
        port,
        path: '/xmlrpc/2/common',
        basic_auth: protocol === 'https' ? undefined : null,
      });

      // Create object client for operations
      this.objectClient = xmlrpc.createClient({
        host: cleanHost,
        port,
        path: '/xmlrpc/2/object',
        basic_auth: protocol === 'https' ? undefined : null,
      });

      this.logger.log(
        `Connected to Odoo XML-RPC at ${protocol}://${cleanHost}:${port}`,
      );
    } catch (error) {
      this.logger.error('Failed to connect to XML-RPC', error);
      throw new Error(`XML-RPC connection failed: ${error.message}`);
    }
  }

  async authenticate(method: AuthMethod, credentials: any): Promise<number> {
    if (method !== AuthMethod.PASSWORD) {
      throw new Error('XML-RPC only supports password authentication');
    }

    return new Promise((resolve, reject) => {
      this.commonClient.methodCall(
        'authenticate',
        [
          credentials.database || this.config.database,
          credentials.username || this.config.username,
          credentials.password || this.config.password,
          {},
        ],
        (error: any, value: any) => {
          if (error) {
            this.logger.error('XML-RPC authentication failed', error);
            reject(new Error(`Authentication failed: ${error.message}`));
          } else if (!value) {
            reject(new Error('Invalid credentials'));
          } else {
            this.uid = value;
            this.logger.log(`Authenticated as user ID: ${this.uid}`);
            resolve(value);
          }
        },
      );
    });
  }

  async execute(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): Promise<any> {
    if (!this.uid) {
      throw new Error('Not authenticated. Call authenticate() first.');
    }

    return new Promise((resolve, reject) => {
      const params = [
        this.config.database,
        this.uid,
        this.config.password,
        model,
        method,
        args,
        kwargs || {},
      ];

      this.objectClient.methodCall(
        'execute_kw',
        params,
        (error: any, value: any) => {
          if (error) {
            this.logger.error(
              `XML-RPC execute failed for ${model}.${method}`,
              error,
            );
            reject(new Error(`Execute failed: ${error.message}`));
          } else {
            resolve(value);
          }
        },
      );
    });
  }

  async disconnect(): Promise<void> {
    this.uid = null;
    this.objectClient = null;
    this.commonClient = null;
    this.logger.log('Disconnected from XML-RPC');
  }
}
