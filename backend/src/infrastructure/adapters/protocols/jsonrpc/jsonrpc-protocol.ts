import { Injectable, Logger } from '@nestjs/common';
import { IOdooProtocol } from '../../../../domain/repositories/odoo-adapter.interface';
import {
  OdooConnectionConfig,
  ProtocolType,
  AuthMethod,
} from '../../../../domain/value-objects/odoo-connection-config';

@Injectable()
export class JsonRpcProtocol implements IOdooProtocol {
  private readonly logger = new Logger(JsonRpcProtocol.name);
  readonly type = ProtocolType.JSONRPC;
  readonly supportedMethods = ['execute', 'execute_kw'];

  private sessionId: string | null = null;
  private baseUrl: string;
  private config: OdooConnectionConfig;
  private uid: number;

  async connect(config: OdooConnectionConfig): Promise<void> {
    this.config = config;

    // Use the host as-is if it includes protocol, otherwise assume https
    let baseUrl = config.host;
    if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
      baseUrl = `https://${baseUrl}`;
    }

    // Remove trailing slash
    this.baseUrl = baseUrl.replace(/\/$/, '');

    this.logger.log(`Connected to Odoo JSON-RPC at ${this.baseUrl}`);
  }

  async authenticate(method: AuthMethod, credentials: any): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/web/session/authenticate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'call',
          params: {
            db: credentials.database || this.config.database,
            login: credentials.username || this.config.username,
            password: credentials.password || this.config.password,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error.message || 'Authentication failed');
      }

      if (!result.result || !result.result.uid) {
        throw new Error('Invalid credentials');
      }

      // Debug: Log the full result to see available fields
      this.logger.debug('Authentication result:', JSON.stringify(result.result, null, 2));

      // Extract session ID from response or cookies
      this.sessionId = result.result.session_id || result.result.sessionId;

      // If no session in response, try to extract from Set-Cookie header
      if (!this.sessionId) {
        const setCookieHeader = response.headers.get('set-cookie');
        if (setCookieHeader) {
          const sessionMatch = setCookieHeader.match(/session_id=([^;]+)/);
          if (sessionMatch) {
            this.sessionId = sessionMatch[1];
          }
        }
      }

      this.uid = result.result.uid;

      this.logger.log(`Authenticated via JSON-RPC as user ID: ${this.uid}`);
      this.logger.debug(`Session ID: ${this.sessionId}`);
      this.logger.debug(`Set-Cookie header: ${response.headers.get('set-cookie')}`);

      if (!this.sessionId) {
        this.logger.warn('No session ID found in response or cookies');
      }

      return this.uid;
    } catch (error) {
      this.logger.error('JSON-RPC authentication failed', error);
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  async execute(
    model: string,
    method: string,
    args: any[],
    kwargs?: any,
  ): Promise<any> {
    if (!this.sessionId) {
      throw new Error('Not authenticated. Call authenticate() first.');
    }

    try {
      const response = await fetch(`${this.baseUrl}/jsonrpc`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: `session_id=${this.sessionId}`,
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'call',
          params: {
            service: 'object',
            method: 'execute_kw',
            args: [
              this.config.database,
              this.uid,
              this.config.password,
              model,
              method,
              args,
              kwargs || {}
            ],
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.error) {
        this.logger.error('JSON-RPC execute error:', JSON.stringify(result.error, null, 2));
        throw new Error(result.error.message || result.error.data?.message || 'Execute failed');
      }

      return result.result;
    } catch (error) {
      this.logger.error(
        `JSON-RPC execute failed for ${model}.${method}`,
        error,
      );
      throw new Error(`Execute failed: ${error.message}`);
    }
  }

  async disconnect(): Promise<void> {
    this.sessionId = null;
    this.logger.log('Disconnected from JSON-RPC');
  }
}
