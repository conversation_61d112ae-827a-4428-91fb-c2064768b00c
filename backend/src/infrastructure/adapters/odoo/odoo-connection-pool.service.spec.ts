import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { OdooConnectionPoolService } from './odoo-connection-pool.service';
import { UniversalOdooAdapter } from './universal-odoo-adapter';
import { PerformanceMonitorService } from './performance-monitor.service';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';
import { OdooConnectionConfig } from '../../../shared/domain/value-objects/odoo-connection-config';

interface UserContext {
  userId: string;
  sessionId: string;
  odooConfig: OdooConnectionConfig;
}

describe('OdooConnectionPoolService', () => {
  let service: OdooConnectionPoolService;
  let performanceMonitor: PerformanceMonitorService;
  let mockAdapter: jest.Mocked<UniversalOdooAdapter>;

  const mockUserContext: UserContext = {
    userId: 'test-user-1',
    sessionId: 'test-session-1',
    odooConfig: {
      host: 'https://test.odoo.com',
      database: 'test_db',
      username: 'test_user',
      password: 'test_password',
    },
  };

  beforeEach(async () => {
    // Create mock adapter
    mockAdapter = {
      setConnectionConfig: jest.fn(),
      connect: jest.fn().mockResolvedValue(undefined),
      authenticate: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
      searchRead: jest.fn().mockResolvedValue([{ id: 1, name: 'Test User' }]),
      getVersionInfo: jest.fn().mockReturnValue({ series: '17.0', major: 17 }),
      getCapabilities: jest.fn().mockReturnValue({ hasJsonRpc: true }),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OdooConnectionPoolService,
        PerformanceMonitorService,
        {
          provide: XmlRpcProtocol,
          useValue: { connect: jest.fn(), execute: jest.fn() },
        },
        {
          provide: JsonRpcProtocol,
          useValue: { connect: jest.fn(), execute: jest.fn() },
        },
        {
          provide: RestApiProtocol,
          useValue: { connect: jest.fn(), execute: jest.fn() },
        },
        {
          provide: OdooV18Adapter,
          useValue: { mapMethod: jest.fn(), mapFields: jest.fn() },
        },
        {
          provide: OdooV17Adapter,
          useValue: { mapMethod: jest.fn(), mapFields: jest.fn() },
        },
        {
          provide: OdooV15Adapter,
          useValue: { mapMethod: jest.fn(), mapFields: jest.fn() },
        },
        {
          provide: OdooV13Adapter,
          useValue: { mapMethod: jest.fn(), mapFields: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<OdooConnectionPoolService>(OdooConnectionPoolService);
    performanceMonitor = module.get<PerformanceMonitorService>(PerformanceMonitorService);

    // Mock the createConnection method to return our mock adapter
    jest.spyOn(service as any, 'createConnection').mockResolvedValue(mockAdapter);
  });

  afterEach(async () => {
    await service.onModuleDestroy();
    jest.clearAllMocks();
  });

  describe('Enhanced Connection Pool Features', () => {
    it('should initialize with background tasks', async () => {
      const logSpy = jest.spyOn(Logger.prototype, 'log');
      
      await service.onModuleInit();
      
      expect(logSpy).toHaveBeenCalledWith('Initializing Odoo Connection Pool...');
      expect(logSpy).toHaveBeenCalledWith(expect.stringContaining('Pool Configuration:'));
      expect(logSpy).toHaveBeenCalledWith('Odoo Connection Pool initialized successfully');
    });

    it('should create new connection with enhanced metadata', async () => {
      const adapter = await service.getConnection(mockUserContext);
      
      expect(adapter).toBe(mockAdapter);
      expect(mockAdapter.setConnectionConfig).toHaveBeenCalledWith(mockUserContext.odooConfig);
      expect(mockAdapter.connect).toHaveBeenCalled();
      expect(mockAdapter.authenticate).toHaveBeenCalled();
      
      const metrics = service.getPoolMetrics();
      expect(metrics.size).toBe(1);
      expect(metrics.totalUsageCount).toBe(1);
    });

    it('should reuse healthy cached connections', async () => {
      // First call creates connection
      const adapter1 = await service.getConnection(mockUserContext);
      expect(mockAdapter.connect).toHaveBeenCalledTimes(1);
      
      // Second call should reuse connection
      const adapter2 = await service.getConnection(mockUserContext);
      expect(adapter1).toBe(adapter2);
      expect(mockAdapter.connect).toHaveBeenCalledTimes(1);
      
      const metrics = service.getPoolMetrics();
      expect(metrics.size).toBe(1);
      expect(metrics.totalUsageCount).toBe(2);
    });

    it('should perform health checks on connections', async () => {
      await service.getConnection(mockUserContext);
      
      const healthResult = await service.healthCheck();
      
      expect(healthResult.totalConnections).toBe(1);
      expect(healthResult.healthyConnections).toBe(1);
      expect(healthResult.unhealthyConnections).toBe(0);
      expect(healthResult.details).toHaveLength(1);
      expect(healthResult.details[0].healthy).toBe(true);
      expect(healthResult.details[0].responseTime).toBeGreaterThan(0);
      expect(healthResult.poolMetrics).toBeDefined();
    });

    it('should handle unhealthy connections', async () => {
      await service.getConnection(mockUserContext);
      
      // Make the adapter fail validation
      mockAdapter.searchRead.mockRejectedValueOnce(new Error('Connection lost'));
      
      const healthResult = await service.healthCheck();
      
      expect(healthResult.totalConnections).toBe(1);
      expect(healthResult.healthyConnections).toBe(0);
      expect(healthResult.unhealthyConnections).toBe(1);
      expect(healthResult.details[0].healthy).toBe(false);
      expect(healthResult.details[0].error).toBeDefined();
    });

    it('should provide enhanced pool metrics', async () => {
      await service.getConnection(mockUserContext);
      await service.getConnection({
        ...mockUserContext,
        userId: 'test-user-2',
      });
      
      const metrics = service.getPoolMetrics();
      
      expect(metrics.size).toBe(2);
      expect(metrics.utilizationPercent).toBeGreaterThan(0);
      expect(metrics.totalUsageCount).toBe(2);
      expect(metrics.averageUsageCount).toBe(1);
      expect(metrics.healthyConnections).toBe(2);
      expect(metrics.unhealthyConnections).toBe(0);
      expect(metrics.lastHealthCheckTime).toBeNull(); // No background check yet
    });

    it('should record performance metrics', async () => {
      const recordMetricSpy = jest.spyOn(performanceMonitor, 'recordMetric');
      
      await service.getConnection(mockUserContext);
      
      expect(recordMetricSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'getConnection',
          success: true,
          duration: expect.any(Number),
          timestamp: expect.any(Date),
        })
      );
    });

    it('should cleanup stale connections', async () => {
      await service.getConnection(mockUserContext);
      
      // Simulate old connection by mocking the lastUsed date
      const connections = Array.from((service as any).connectionPool.entries());
      connections[0][1].lastUsed = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
      
      const cleanedCount = await service.cleanupStaleConnections(60); // 1 hour threshold
      
      expect(cleanedCount).toBe(1);
      expect(mockAdapter.disconnect).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle connection creation failures', async () => {
      jest.spyOn(service as any, 'createConnection').mockRejectedValueOnce(new Error('Connection failed'));
      
      await expect(service.getConnection(mockUserContext)).rejects.toThrow('Connection failed');
    });

    it('should record failed operations in performance metrics', async () => {
      const recordMetricSpy = jest.spyOn(performanceMonitor, 'recordMetric');
      jest.spyOn(service as any, 'createConnection').mockRejectedValueOnce(new Error('Connection failed'));
      
      await expect(service.getConnection(mockUserContext)).rejects.toThrow();
      
      expect(recordMetricSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'getConnection',
          success: false,
          error: 'Connection failed',
        })
      );
    });
  });
});
