import { Injectable, Logger } from '@nestjs/common';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';

export interface UserOdooMapping {
  webUserId: string;
  odooInstanceId: string;
  odooConfig: OdooConnectionConfig;
  isActive: boolean;
  createdAt: Date;
  lastUsed: Date;
}

export interface CreateMappingDto {
  webUserId: string;
  odooInstanceId?: string;
  odooConfig: OdooConnectionConfig;
}

/**
 * Service to manage mappings between web users and their Odoo instances
 * In production, this should be backed by a database
 */
@Injectable()
export class UserOdooMappingService {
  private readonly logger = new Logger(UserOdooMappingService.name);
  
  // In-memory storage for development - replace with database in production
  private mappings = new Map<string, UserOdooMapping[]>();

  /**
   * Create or update a user-odoo mapping
   */
  async createMapping(dto: CreateMappingDto): Promise<UserOdooMapping> {
    const { webUserId, odooConfig } = dto;
    const odooInstanceId = dto.odooInstanceId || this.generateInstanceId(odooConfig);
    
    const mapping: UserOdooMapping = {
      webUserId,
      odooInstanceId,
      odooConfig: {
        ...odooConfig,
        password: this.encryptPassword(odooConfig.password), // Encrypt password
      },
      isActive: true,
      createdAt: new Date(),
      lastUsed: new Date(),
    };

    // Get existing mappings for user
    const userMappings = this.mappings.get(webUserId) || [];
    
    // Check if mapping already exists
    const existingIndex = userMappings.findIndex(
      m => m.odooInstanceId === odooInstanceId
    );

    if (existingIndex >= 0) {
      // Update existing mapping
      userMappings[existingIndex] = mapping;
      this.logger.log(`Updated Odoo mapping for user ${webUserId}, instance ${odooInstanceId}`);
    } else {
      // Add new mapping
      userMappings.push(mapping);
      this.logger.log(`Created new Odoo mapping for user ${webUserId}, instance ${odooInstanceId}`);
    }

    this.mappings.set(webUserId, userMappings);
    return mapping;
  }

  /**
   * Get all Odoo instances for a web user
   */
  async getUserMappings(webUserId: string): Promise<UserOdooMapping[]> {
    const mappings = this.mappings.get(webUserId) || [];
    return mappings.filter(m => m.isActive);
  }

  /**
   * Get specific Odoo instance for a user
   */
  async getUserMapping(webUserId: string, odooInstanceId?: string): Promise<UserOdooMapping | null> {
    const userMappings = await this.getUserMappings(webUserId);
    
    if (odooInstanceId) {
      return userMappings.find(m => m.odooInstanceId === odooInstanceId) || null;
    }
    
    // Return the most recently used mapping if no instance ID specified
    return userMappings.sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())[0] || null;
  }

  /**
   * Update last used timestamp
   */
  async updateLastUsed(webUserId: string, odooInstanceId: string): Promise<void> {
    const userMappings = this.mappings.get(webUserId) || [];
    const mapping = userMappings.find(m => m.odooInstanceId === odooInstanceId);
    
    if (mapping) {
      mapping.lastUsed = new Date();
      this.logger.debug(`Updated last used for user ${webUserId}, instance ${odooInstanceId}`);
    }
  }

  /**
   * Deactivate a mapping
   */
  async deactivateMapping(webUserId: string, odooInstanceId: string): Promise<boolean> {
    const userMappings = this.mappings.get(webUserId) || [];
    const mapping = userMappings.find(m => m.odooInstanceId === odooInstanceId);
    
    if (mapping) {
      mapping.isActive = false;
      this.logger.log(`Deactivated Odoo mapping for user ${webUserId}, instance ${odooInstanceId}`);
      return true;
    }
    
    return false;
  }

  /**
   * Remove all mappings for a user
   */
  async removeUserMappings(webUserId: string): Promise<void> {
    this.mappings.delete(webUserId);
    this.logger.log(`Removed all Odoo mappings for user ${webUserId}`);
  }

  /**
   * Get decrypted Odoo config for use
   */
  async getDecryptedConfig(mapping: UserOdooMapping): Promise<OdooConnectionConfig> {
    return {
      ...mapping.odooConfig,
      password: this.decryptPassword(mapping.odooConfig.password),
    };
  }

  /**
   * Generate unique instance ID for Odoo configuration
   */
  private generateInstanceId(config: OdooConnectionConfig): string {
    const hash = Buffer.from(`${config.host}:${config.database}:${config.username}`)
      .toString('base64')
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 12);
    return `odoo_${hash}`;
  }

  /**
   * Encrypt password (simple implementation - use proper encryption in production)
   */
  private encryptPassword(password: string): string {
    // TODO: Use proper encryption like AES-256
    // For now, just base64 encode (NOT SECURE)
    return Buffer.from(password).toString('base64');
  }

  /**
   * Decrypt password
   */
  private decryptPassword(encryptedPassword: string): string {
    // TODO: Use proper decryption
    // For now, just base64 decode
    return Buffer.from(encryptedPassword, 'base64').toString('utf-8');
  }

  /**
   * Get statistics about mappings
   */
  async getStats(): Promise<{
    totalUsers: number;
    totalMappings: number;
    activeMappings: number;
  }> {
    let totalMappings = 0;
    let activeMappings = 0;

    for (const userMappings of this.mappings.values()) {
      totalMappings += userMappings.length;
      activeMappings += userMappings.filter(m => m.isActive).length;
    }

    return {
      totalUsers: this.mappings.size,
      totalMappings,
      activeMappings,
    };
  }
}
