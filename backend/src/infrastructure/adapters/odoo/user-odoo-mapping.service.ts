import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';
import { UserOdooMapping, UserOdooMappingDocument } from '../../database/schemas/user-odoo-mapping.schema';
import * as crypto from 'crypto';

export interface UserOdooMappingDto {
  webUserId: string;
  odooInstanceId: string;
  odooConfig: OdooConnectionConfig;
  isActive: boolean;
  createdAt: Date;
  lastUsed: Date;
  description?: string;
  tags?: string[];
}

export interface CreateMappingDto {
  webUserId: string;
  odooInstanceId?: string;
  odooConfig: OdooConnectionConfig;
  description?: string;
  tags?: string[];
}

/**
 * MongoDB-based service to manage mappings between web users and their Odoo instances
 * Production-ready with encryption, indexing, and comprehensive error handling
 */
@Injectable()
export class UserOdooMappingService {
  private readonly logger = new Logger(UserOdooMappingService.name);
  private readonly encryptionKey: string;

  constructor(
    @InjectModel(UserOdooMapping.name)
    private readonly userOdooMappingModel: Model<UserOdooMappingDocument>,
  ) {
    // Initialize encryption key from environment
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-dev-key-change-in-production';
    if (this.encryptionKey === 'default-dev-key-change-in-production') {
      this.logger.warn('Using default encryption key. Set ENCRYPTION_KEY environment variable in production!');
    }
  }

  /**
   * Create or update a user-odoo mapping
   */
  async createMapping(dto: CreateMappingDto): Promise<UserOdooMappingDto> {
    try {
      const { webUserId, odooConfig } = dto;
      const odooInstanceId = dto.odooInstanceId || this.generateInstanceId(odooConfig);

      const mappingData = {
        webUserId,
        odooInstanceId,
        odooHost: odooConfig.host,
        odooDatabase: odooConfig.database,
        odooUsername: odooConfig.username,
        odooPasswordEncrypted: this.encryptPassword(odooConfig.password),
        odooProtocol: odooConfig.protocol || 'https',
        odooPort: odooConfig.port || (odooConfig.protocol === 'https' ? 443 : 80),
        isActive: true,
        lastUsed: new Date(),
        description: dto.description,
        tags: dto.tags || [],
        totalUsageCount: 0,
      };

      // Use upsert to create or update
      const mapping = await this.userOdooMappingModel.findOneAndUpdate(
        { webUserId, odooInstanceId },
        mappingData,
        {
          upsert: true,
          new: true,
          runValidators: true,
        }
      );

      this.logger.log(`${mapping.isNew ? 'Created' : 'Updated'} Odoo mapping for user ${webUserId}, instance ${odooInstanceId}`);

      return this.mapDocumentToDto(mapping);
    } catch (error) {
      this.logger.error(`Failed to create mapping for user ${dto.webUserId}`, error);
      throw new Error(`Failed to create Odoo mapping: ${error.message}`);
    }
  }

  /**
   * Get all active Odoo instances for a web user
   */
  async getUserMappings(webUserId: string): Promise<UserOdooMappingDto[]> {
    try {
      const mappings = await this.userOdooMappingModel
        .find({ webUserId, isActive: true })
        .sort({ lastUsed: -1 })
        .exec();

      return mappings.map(mapping => this.mapDocumentToDto(mapping));
    } catch (error) {
      this.logger.error(`Failed to get mappings for user ${webUserId}`, error);
      throw new Error(`Failed to retrieve user mappings: ${error.message}`);
    }
  }

  /**
   * Get specific Odoo instance for a user
   */
  async getUserMapping(webUserId: string, odooInstanceId?: string): Promise<UserOdooMappingDto | null> {
    try {
      let mapping: UserOdooMappingDocument | null;

      if (odooInstanceId) {
        // Get specific instance
        mapping = await this.userOdooMappingModel
          .findOne({ webUserId, odooInstanceId, isActive: true })
          .exec();
      } else {
        // Get most recently used instance
        mapping = await this.userOdooMappingModel
          .findOne({ webUserId, isActive: true })
          .sort({ lastUsed: -1 })
          .exec();
      }

      return mapping ? this.mapDocumentToDto(mapping) : null;
    } catch (error) {
      this.logger.error(`Failed to get mapping for user ${webUserId}, instance ${odooInstanceId}`, error);
      throw new Error(`Failed to retrieve user mapping: ${error.message}`);
    }
  }

  /**
   * Update last used timestamp and increment usage count
   */
  async updateLastUsed(webUserId: string, odooInstanceId: string): Promise<void> {
    try {
      await this.userOdooMappingModel.updateOne(
        { webUserId, odooInstanceId, isActive: true },
        {
          $set: { lastUsed: new Date() },
          $inc: { totalUsageCount: 1 }
        }
      );

      this.logger.debug(`Updated last used for user ${webUserId}, instance ${odooInstanceId}`);
    } catch (error) {
      this.logger.error(`Failed to update last used for user ${webUserId}, instance ${odooInstanceId}`, error);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Deactivate a mapping (soft delete)
   */
  async deactivateMapping(webUserId: string, odooInstanceId: string): Promise<boolean> {
    try {
      const result = await this.userOdooMappingModel.updateOne(
        { webUserId, odooInstanceId },
        { $set: { isActive: false } }
      );

      if (result.modifiedCount > 0) {
        this.logger.log(`Deactivated Odoo mapping for user ${webUserId}, instance ${odooInstanceId}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Failed to deactivate mapping for user ${webUserId}, instance ${odooInstanceId}`, error);
      throw new Error(`Failed to deactivate mapping: ${error.message}`);
    }
  }

  /**
   * Remove all mappings for a user (soft delete)
   */
  async removeUserMappings(webUserId: string): Promise<void> {
    try {
      const result = await this.userOdooMappingModel.updateMany(
        { webUserId },
        { $set: { isActive: false } }
      );

      this.logger.log(`Deactivated ${result.modifiedCount} Odoo mappings for user ${webUserId}`);
    } catch (error) {
      this.logger.error(`Failed to remove mappings for user ${webUserId}`, error);
      throw new Error(`Failed to remove user mappings: ${error.message}`);
    }
  }

  /**
   * Get decrypted Odoo config for use
   */
  async getDecryptedConfig(mapping: UserOdooMappingDto): Promise<OdooConnectionConfig> {
    return {
      host: mapping.odooConfig.host,
      database: mapping.odooConfig.database,
      username: mapping.odooConfig.username,
      password: this.decryptPassword(mapping.odooConfig.password),
      protocol: mapping.odooConfig.protocol,
      port: mapping.odooConfig.port,
    };
  }

  /**
   * Mark connection as successful
   */
  async markConnectionSuccess(webUserId: string, odooInstanceId: string): Promise<void> {
    try {
      await this.userOdooMappingModel.updateOne(
        { webUserId, odooInstanceId },
        {
          $set: {
            lastSuccessfulConnection: new Date(),
            connectionAttempts: 0,
          },
          $unset: { lastConnectionError: 1 }
        }
      );
    } catch (error) {
      this.logger.error(`Failed to mark connection success for user ${webUserId}, instance ${odooInstanceId}`, error);
    }
  }

  /**
   * Mark connection as failed
   */
  async markConnectionFailure(webUserId: string, odooInstanceId: string, errorMessage: string): Promise<void> {
    try {
      await this.userOdooMappingModel.updateOne(
        { webUserId, odooInstanceId },
        {
          $set: { lastConnectionError: errorMessage },
          $inc: { connectionAttempts: 1 }
        }
      );
    } catch (error) {
      this.logger.error(`Failed to mark connection failure for user ${webUserId}, instance ${odooInstanceId}`, error);
    }
  }

  /**
   * Generate unique instance ID for Odoo configuration
   */
  private generateInstanceId(config: OdooConnectionConfig): string {
    const hash = Buffer.from(`${config.host}:${config.database}:${config.username}`)
      .toString('base64')
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 12);
    return `odoo_${hash}`;
  }

  /**
   * Encrypt password using AES-256-GCM
   */
  private encryptPassword(password: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);

      let encrypted = cipher.update(password, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const authTag = cipher.getAuthTag();

      // Combine IV, authTag, and encrypted data
      return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error('Failed to encrypt password', error);
      throw new Error('Password encryption failed');
    }
  }

  /**
   * Decrypt password using AES-256-GCM
   */
  private decryptPassword(encryptedPassword: string): string {
    try {
      const parts = encryptedPassword.split(':');
      if (parts.length !== 3) {
        // Fallback for old base64 encoding
        return Buffer.from(encryptedPassword, 'base64').toString('utf-8');
      }

      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];

      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt password', error);
      throw new Error('Password decryption failed');
    }
  }

  /**
   * Get comprehensive statistics about mappings
   */
  async getStats(): Promise<{
    totalUsers: number;
    totalMappings: number;
    activeMappings: number;
    totalUsageCount: number;
    topHosts: Array<{ host: string; count: number }>;
  }> {
    try {
      const [
        totalMappings,
        activeMappings,
        uniqueUsers,
        usageStats,
        hostStats
      ] = await Promise.all([
        this.userOdooMappingModel.countDocuments(),
        this.userOdooMappingModel.countDocuments({ isActive: true }),
        this.userOdooMappingModel.distinct('webUserId'),
        this.userOdooMappingModel.aggregate([
          { $group: { _id: null, totalUsage: { $sum: '$totalUsageCount' } } }
        ]),
        this.userOdooMappingModel.aggregate([
          { $match: { isActive: true } },
          { $group: { _id: '$odooHost', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ])
      ]);

      return {
        totalUsers: uniqueUsers.length,
        totalMappings,
        activeMappings,
        totalUsageCount: usageStats[0]?.totalUsage || 0,
        topHosts: hostStats.map(stat => ({ host: stat._id, count: stat.count })),
      };
    } catch (error) {
      this.logger.error('Failed to get mapping statistics', error);
      throw new Error(`Failed to retrieve statistics: ${error.message}`);
    }
  }

  /**
   * Map MongoDB document to DTO
   */
  private mapDocumentToDto(doc: UserOdooMappingDocument): UserOdooMappingDto {
    return {
      webUserId: doc.webUserId,
      odooInstanceId: doc.odooInstanceId,
      odooConfig: {
        host: doc.odooHost,
        database: doc.odooDatabase,
        username: doc.odooUsername,
        password: doc.odooPasswordEncrypted, // Keep encrypted in DTO
        protocol: doc.odooProtocol as 'http' | 'https',
        port: doc.odooPort,
      },
      isActive: doc.isActive,
      createdAt: doc.createdAt || new Date(),
      lastUsed: doc.lastUsed,
      description: doc.description,
      tags: doc.tags,
    };
  }
}
