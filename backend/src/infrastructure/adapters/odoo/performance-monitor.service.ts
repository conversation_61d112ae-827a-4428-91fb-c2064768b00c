import { Injectable, Logger } from '@nestjs/common';

export interface PerformanceMetric {
  operation: string;
  model?: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

export interface PerformanceStats {
  totalOperations: number;
  averageDuration: number;
  successRate: number;
  slowestOperation: PerformanceMetric | null;
  fastestOperation: PerformanceMetric | null;
  errorCount: number;
  operationCounts: Record<string, number>;
}

@Injectable()
export class PerformanceMonitorService {
  private readonly logger = new Logger(PerformanceMonitorService.name);
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 1000; // Keep last 1000 metrics

  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the last N metrics to prevent memory issues
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations
    if (metric.duration > 5000) { // 5 seconds
      this.logger.warn(
        `Slow operation detected: ${metric.operation}${
          metric.model ? ` on ${metric.model}` : ''
        } took ${metric.duration}ms`
      );
    }

    // Log errors
    if (!metric.success && metric.error) {
      this.logger.error(
        `Operation failed: ${metric.operation}${
          metric.model ? ` on ${metric.model}` : ''
        } - ${metric.error}`
      );
    }
  }

  getStats(timeWindowMs?: number): PerformanceStats {
    const now = new Date();
    const cutoff = timeWindowMs ? new Date(now.getTime() - timeWindowMs) : null;
    
    const relevantMetrics = cutoff 
      ? this.metrics.filter(m => m.timestamp >= cutoff)
      : this.metrics;

    if (relevantMetrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        successRate: 0,
        slowestOperation: null,
        fastestOperation: null,
        errorCount: 0,
        operationCounts: {},
      };
    }

    const successfulMetrics = relevantMetrics.filter(m => m.success);
    const durations = relevantMetrics.map(m => m.duration);
    const operationCounts: Record<string, number> = {};

    relevantMetrics.forEach(metric => {
      const key = metric.model ? `${metric.operation}:${metric.model}` : metric.operation;
      operationCounts[key] = (operationCounts[key] || 0) + 1;
    });

    return {
      totalOperations: relevantMetrics.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      successRate: (successfulMetrics.length / relevantMetrics.length) * 100,
      slowestOperation: relevantMetrics.reduce((prev, current) => 
        (prev.duration > current.duration) ? prev : current
      ),
      fastestOperation: relevantMetrics.reduce((prev, current) => 
        (prev.duration < current.duration) ? prev : current
      ),
      errorCount: relevantMetrics.filter(m => !m.success).length,
      operationCounts,
    };
  }

  getRecentErrors(count: number = 10): PerformanceMetric[] {
    return this.metrics
      .filter(m => !m.success)
      .slice(-count)
      .reverse();
  }

  getSlowestOperations(count: number = 10): PerformanceMetric[] {
    return [...this.metrics]
      .sort((a, b) => b.duration - a.duration)
      .slice(0, count);
  }

  clearMetrics(): void {
    this.metrics = [];
    this.logger.log('Performance metrics cleared');
  }

  logPerformanceReport(): void {
    const stats = this.getStats();
    
    this.logger.log('=== Performance Report ===');
    this.logger.log(`Total Operations: ${stats.totalOperations}`);
    this.logger.log(`Average Duration: ${stats.averageDuration.toFixed(2)}ms`);
    this.logger.log(`Success Rate: ${stats.successRate.toFixed(2)}%`);
    this.logger.log(`Error Count: ${stats.errorCount}`);
    
    if (stats.slowestOperation) {
      this.logger.log(
        `Slowest Operation: ${stats.slowestOperation.operation} (${stats.slowestOperation.duration}ms)`
      );
    }
    
    this.logger.log('Operation Counts:');
    Object.entries(stats.operationCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .forEach(([operation, count]) => {
        this.logger.log(`  ${operation}: ${count}`);
      });
  }
}
