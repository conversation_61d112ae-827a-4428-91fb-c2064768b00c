import { Injectable } from '@nestjs/common';
import { BaseVersionAdapter } from './base-version-adapter';
import { OdooCapabilities } from '../../../../shared/domain/repositories/odoo-adapter.interface';
import { AuthMethod } from '../../../../shared/domain/value-objects/odoo-connection-config';

@Injectable()
export class OdooV13Adapter extends BaseVersionAdapter {
  readonly version = '13.0';
  readonly capabilities: OdooCapabilities = {
    hasJsonRpc: true,
    hasRestApi: false,
    hasGraphQL: false,
    hasWebSocket: false,
    hasTokenAuth: false,
    hasOAuth2: false,
    maxBatchSize: 500,
    supportedAuthMethods: [AuthMethod.PASSWORD],
  };

  constructor() {
    super();
    this.initializeFieldMappings();
  }

  private initializeFieldMappings(): void {
    // V13 specific field mappings - handle @api.one decorator removal
    this.setFieldMapping('res.partner', {
      // Example mappings for v13 changes
      deprecated_field_v13: null, // Field removed in v13
      old_field_name: 'new_field_name', // Field renamed
    });

    this.setFieldMapping('sale.order', {
      // V13 sale order specific mappings
      confirmation_date: 'date_order', // Example mapping
    });

    this.setFieldMapping('account.invoice', {
      // In v13, account.invoice was replaced with account.move
      // This mapping helps with backward compatibility
    });
  }

  mapDomain(domain: any[]): any[] {
    // V13 domain handling - some operators might have changed
    return domain.map((clause) => {
      if (Array.isArray(clause) && clause.length === 3) {
        const [field, operator, value] = clause;

        // Handle specific v13 domain changes
        if (operator === 'ilike' && typeof value === 'string') {
          // V13 might handle ilike differently
          return [field, operator, value];
        }
      }

      return clause;
    });
  }

  handleResponse(response: any): any {
    // V13 specific response handling
    if (Array.isArray(response)) {
      return response.map((record) => this.processV13Record(record));
    }

    return this.processV13Record(response);
  }

  private processV13Record(record: any): any {
    if (!record || typeof record !== 'object') {
      return record;
    }

    // V13 specific record processing
    // Handle @api.one decorator removal effects
    // Convert single values to arrays where needed

    const processedRecord = { ...record };

    // Example: Handle many2one fields that might return different formats
    Object.keys(processedRecord).forEach((key) => {
      const value = processedRecord[key];

      // Handle many2one fields - in v13 they might return [id, name] or just id
      if (
        Array.isArray(value) &&
        value.length === 2 &&
        typeof value[0] === 'number'
      ) {
        // This is likely a many2one field [id, name]
        processedRecord[key] = value[0]; // Return just the ID for consistency
        processedRecord[`${key}_name`] = value[1]; // Store name separately
      }
    });

    return processedRecord;
  }
}
