import { Injectable } from '@nestjs/common';
import { BaseVersionAdapter } from './base-version-adapter';
import { OdooCapabilities } from '../../../../shared/domain/repositories/odoo-adapter.interface';
import { AuthMethod } from '../../../../shared/domain/value-objects/odoo-connection-config';

@Injectable()
export class OdooV17Adapter extends BaseVersionAdapter {
  readonly version = '17.0';
  readonly capabilities: OdooCapabilities = {
    hasJsonRpc: true,
    hasRestApi: false, // via modules
    hasGraphQL: false, // via modules
    hasWebSocket: false,
    hasTokenAuth: false,
    hasOAuth2: true,
    maxBatchSize: 800,
    supportedAuthMethods: [
      AuthMethod.PASSWORD,
      AuthMethod.API_KEY,
      AuthMethod.OAUTH2,
    ],
  };

  constructor() {
    super();
    this.initializeFieldMappings();
  }

  private initializeFieldMappings(): void {
    // V17 specific field mappings
    this.setFieldMapping('res.partner', {
      // Example: if any fields changed in v17
    });

    this.setFieldMapping('sale.order', {
      // V17 sale order specific mappings
    });

    this.setFieldMapping('account.move', {
      // V17 invoice specific mappings
    });
  }

  mapDomain(domain: any[]): any[] {
    // V17 domain handling
    return domain;
  }

  handleResponse(response: any): any {
    // V17 specific response handling
    if (Array.isArray(response)) {
      return response.map((record) => this.processRecord(record));
    }

    return this.processRecord(response);
  }

  private processRecord(record: any): any {
    if (!record || typeof record !== 'object') {
      return record;
    }

    // V17 specific record processing
    return record;
  }
}
