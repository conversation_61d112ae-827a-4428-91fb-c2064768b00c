import { Injectable } from '@nestjs/common';
import { BaseVersionAdapter } from './base-version-adapter';
import { OdooCapabilities } from '../../../../shared/domain/repositories/odoo-adapter.interface';
import { AuthMethod } from '../../../../shared/domain/value-objects/odoo-connection-config';

@Injectable()
export class OdooV18Adapter extends BaseVersionAdapter {
  readonly version = '18.0';
  readonly capabilities: OdooCapabilities = {
    hasJsonRpc: true,
    hasRestApi: false, // Not built-in, requires additional modules
    hasGraphQL: false, // via modules
    hasWebSocket: true,
    hasTokenAuth: true,
    hasOAuth2: true,
    maxBatchSize: 1000,
    supportedAuthMethods: [
      AuthMethod.PASSWORD,
      AuthMethod.API_KEY,
      AuthMethod.OAUTH2,
      AuthMethod.TOKEN,
    ],
  };

  constructor() {
    super();
    this.initializeFieldMappings();
  }

  private initializeFieldMappings(): void {
    // V18 specific field mappings
    this.setFieldMapping('res.partner', {
      // Example: if any fields changed in v18
      // 'old_field': 'new_field',
      // 'deprecated_field': null
    });

    this.setFieldMapping('sale.order', {
      // V18 sale order specific mappings
    });

    this.setFieldMapping('account.move', {
      // V18 invoice specific mappings
    });
  }

  mapDomain(domain: any[]): any[] {
    // V18 maintains backward compatibility for domains
    return domain;
  }

  handleResponse(response: any): any {
    // V18 specific response handling
    if (Array.isArray(response)) {
      return response.map((record) => this.processRecord(record));
    }

    return this.processRecord(response);
  }

  private processRecord(record: any): any {
    if (!record || typeof record !== 'object') {
      return record;
    }

    // V18 specific record processing
    // Handle new field formats, data types, etc.
    return record;
  }
}
