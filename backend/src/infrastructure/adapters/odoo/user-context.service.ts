import { Injectable, Logger, Inject, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { UserContext } from './odoo-connection-pool.service';
import { OdooConnectionConfig } from '../../../shared/domain/value-objects/odoo-connection-config';

export interface AuthenticatedUser {
  id: string;
  username: string;
  email?: string;
  sessionId?: string;
  // Add other user properties as needed
}

export interface RequestWithUser extends Request {
  user?: AuthenticatedUser;
  sessionId?: string;
  sessionID?: string; // Express session ID
  session?: any; // Express session object
}

@Injectable({ scope: Scope.REQUEST })
export class UserContextService {
  private readonly logger = new Logger(UserContextService.name);
  private userContext: UserContext | null = null;

  constructor(@Inject(REQUEST) private readonly request: RequestWithUser) {}

  /**
   * Set user context with Odoo configuration
   */
  setUserContext(odooConfig: OdooConnectionConfig): void {
    const user = this.extractUserFromRequest();
    
    this.userContext = {
      userId: user.id,
      sessionId: user.sessionId || this.generateSessionId(),
      odooConfig,
    };

    this.logger.debug(`User context set for user: ${user.id}`);
  }

  /**
   * Get current user context
   */
  getUserContext(): UserContext {
    if (!this.userContext) {
      throw new Error('User context not set. Call setUserContext() first.');
    }
    return this.userContext;
  }

  /**
   * Check if user context is set
   */
  hasUserContext(): boolean {
    return this.userContext !== null;
  }

  /**
   * Get user ID from context
   */
  getUserId(): string {
    const context = this.getUserContext();
    return context.userId;
  }

  /**
   * Get session ID from context
   */
  getSessionId(): string {
    const context = this.getUserContext();
    return context.sessionId;
  }

  /**
   * Update Odoo configuration in current context
   */
  updateOdooConfig(odooConfig: OdooConnectionConfig): void {
    if (!this.userContext) {
      throw new Error('User context not set. Call setUserContext() first.');
    }
    
    this.userContext.odooConfig = odooConfig;
    this.logger.debug(`Updated Odoo config for user: ${this.userContext.userId}`);
  }

  /**
   * Extract user information from request
   * Production-ready authentication with multiple strategies
   */
  private extractUserFromRequest(): AuthenticatedUser {
    // Priority 1: From Passport JWT (after guard validation)
    if (this.request.user) {
      const user = this.request.user as any; // Type assertion for Passport user
      this.logger.debug(`Passport JWT authenticated user: ${user.id}`);
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        sessionId: user.sessionId || this.generateSessionId(),
      };
    }

    // Priority 2: Manual JWT Token Authentication (fallback)
    const authHeader = this.request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const decoded = this.verifyJWT(token); // Use proper JWT verification

        this.logger.debug(`Manual JWT authenticated user: ${decoded.sub || decoded.userId}`);
        return {
          id: decoded.sub || decoded.userId,
          username: decoded.odooUsername || decoded.username,
          email: decoded.email,
          sessionId: decoded.sessionId || this.generateSessionId(),
        };
      } catch (error) {
        this.logger.error('JWT verification failed', error);
        throw new Error('Invalid or expired JWT token');
      }
    }

    // Priority 2: Passport.js authenticated user object
    if (this.request.user) {
      const user = this.request.user as any; // Type assertion for Passport user
      this.logger.debug(`Passport authenticated user: ${user.id}`);
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        sessionId: user.sessionId || this.request.sessionId || this.generateSessionId(),
      };
    }

    // Priority 3: Express session-based authentication
    if (this.request.session && (this.request.session as any).user) {
      const sessionUser = (this.request.session as any).user;
      this.logger.debug(`Session authenticated user: ${sessionUser.id}`);
      return {
        id: sessionUser.id,
        username: sessionUser.username,
        email: sessionUser.email,
        sessionId: this.request.sessionID || this.request.sessionId,
      };
    }

    // Priority 4: Custom headers (for testing/development)
    const userId = this.request.headers['x-user-id'] as string;
    const username = this.request.headers['x-username'] as string;

    if (userId && username) {
      this.logger.debug(`Header authenticated user: ${userId}`);
      return {
        id: userId,
        username: username,
        sessionId: this.generateSessionId(),
      };
    }

    // Priority 5: Development mode anonymous user
    if (process.env.NODE_ENV === 'development') {
      const anonymousId = this.generateAnonymousUserId();
      this.logger.warn(`No authenticated user found, using anonymous user: ${anonymousId}`);
      return {
        id: anonymousId,
        username: 'anonymous',
        sessionId: this.generateSessionId(),
      };
    }

    // Production: Reject unauthenticated requests
    throw new Error('Authentication required. Please provide valid JWT token or login credentials.');
  }

  /**
   * Verify JWT token (production-ready with proper validation)
   * TODO: Replace with proper JWT library like jsonwebtoken
   */
  private verifyJWT(token: string): any {
    try {
      // In production, use proper JWT verification:
      // const jwt = require('jsonwebtoken');
      // return jwt.verify(token, process.env.JWT_SECRET);

      // For now, decode and validate basic structure
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString('utf-8'));

      // Basic validation
      if (!payload.sub && !payload.userId) {
        throw new Error('JWT missing user identifier');
      }

      // Check expiration
      if (payload.exp && Date.now() >= payload.exp * 1000) {
        throw new Error('JWT token expired');
      }

      return payload;
    } catch (error) {
      throw new Error(`JWT verification failed: ${error.message}`);
    }
  }

  /**
   * Decode JWT token (legacy method - use verifyJWT instead)
   * @deprecated Use verifyJWT for production
   */
  private decodeJWT(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = Buffer.from(payload, 'base64').toString('utf-8');
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate anonymous user ID for development
   */
  private generateAnonymousUserId(): string {
    const ip = this.request.ip || this.request.connection.remoteAddress || 'unknown';
    const userAgent = this.request.headers['user-agent'] || 'unknown';
    
    // Create a simple hash of IP + User Agent for consistent anonymous ID
    const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substr(0, 8);
    return `anon_${hash}`;
  }

  /**
   * Get request metadata for logging/debugging
   */
  getRequestMetadata() {
    return {
      ip: this.request.ip,
      userAgent: this.request.headers['user-agent'],
      method: this.request.method,
      url: this.request.url,
      timestamp: new Date().toISOString(),
    };
  }
}
