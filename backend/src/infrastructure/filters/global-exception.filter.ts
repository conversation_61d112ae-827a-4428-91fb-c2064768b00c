import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ResponseBuilderService } from '../../common/services/response-builder.service';
import { ValidationError } from '../../common/interfaces/api-response.interface';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  constructor(private readonly responseBuilder: ResponseBuilderService) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let errorResponse;

    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const responseObj = exceptionResponse as any;
        const message = responseObj.message || exception.message;
        const errorType = responseObj.error || this.getErrorTypeFromStatus(status);
        const validationErrors = this.extractValidationErrors(responseObj);

        errorResponse = this.responseBuilder.error(
          errorType,
          message,
          status,
          request,
          responseObj.errorCode,
          validationErrors,
          { originalException: responseObj },
        );
      } else {
        errorResponse = this.responseBuilder.error(
          this.getErrorTypeFromStatus(status),
          exceptionResponse as string,
          status,
          request,
        );
      }
    } else if (exception instanceof Error) {
      errorResponse = this.responseBuilder.internalError(
        exception.message,
        request,
        'INTERNAL_ERROR',
        {
          errorName: exception.name,
          stack: exception.stack,
        },
      );
    } else {
      errorResponse = this.responseBuilder.internalError(
        'Unknown error occurred',
        request,
        'UNKNOWN_ERROR',
        { exception },
      );
    }

    // Log the error
    this.logger.error(
      `${request.method} ${request.url} - ${errorResponse.statusCode} ${errorResponse.error}: ${errorResponse.message}`,
      exception instanceof Error ? exception.stack : exception,
    );

    response.status(errorResponse.statusCode).json(errorResponse);
  }

  /**
   * Get error type from HTTP status code
   */
  private getErrorTypeFromStatus(status: number): string {
    switch (status) {
      case 400: return 'Bad Request';
      case 401: return 'Unauthorized';
      case 403: return 'Forbidden';
      case 404: return 'Not Found';
      case 409: return 'Conflict';
      case 422: return 'Unprocessable Entity';
      case 429: return 'Too Many Requests';
      case 500: return 'Internal Server Error';
      case 502: return 'Bad Gateway';
      case 503: return 'Service Unavailable';
      case 504: return 'Gateway Timeout';
      default: return 'HTTP Error';
    }
  }

  /**
   * Extract validation errors from exception response
   */
  private extractValidationErrors(responseObj: any): ValidationError[] | undefined {
    if (responseObj.validationErrors && Array.isArray(responseObj.validationErrors)) {
      return responseObj.validationErrors;
    }

    // Handle class-validator format
    if (responseObj.message && Array.isArray(responseObj.message)) {
      return responseObj.message.map((msg: any) => ({
        field: msg.property || 'unknown',
        message: Object.values(msg.constraints || {}).join(', ') || msg,
        value: msg.value,
        rule: Object.keys(msg.constraints || {})[0],
      }));
    }

    return undefined;
  }
}
