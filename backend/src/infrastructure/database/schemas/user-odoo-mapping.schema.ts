import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserOdooMappingDocument = UserOdooMapping & Document;

@Schema({
  collection: 'user_odoo_mappings',
  timestamps: true, // Automatically adds createdAt and updatedAt
})
export class UserOdooMapping {
  @Prop({ required: true, index: true })
  webUserId: string;

  @Prop({ required: true, index: true })
  odooInstanceId: string;

  @Prop({ required: true })
  odooHost: string;

  @Prop({ required: true })
  odooDatabase: string;

  @Prop({ required: true })
  odooUsername: string;

  @Prop({ required: true })
  odooPasswordEncrypted: string;

  @Prop({ default: 'https' })
  odooProtocol: string;

  @Prop({ default: 443 })
  odooPort: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: Date.now })
  lastUsed: Date;

  @Prop()
  lastConnectionError?: string;

  @Prop({ default: 0 })
  connectionAttempts: number;

  @Prop()
  lastSuccessfulConnection?: Date;

  // Metadata for analytics
  @Prop({ default: 0 })
  totalUsageCount: number;

  @Prop()
  tags?: string[]; // For categorizing instances

  @Prop()
  description?: string; // User-friendly description

  // Timestamps (handled by mongoose timestamps: true)
  createdAt?: Date;
  updatedAt?: Date;
}

export const UserOdooMappingSchema = SchemaFactory.createForClass(UserOdooMapping);

// Compound indexes for efficient queries
UserOdooMappingSchema.index({ webUserId: 1, odooInstanceId: 1 }, { unique: true });
UserOdooMappingSchema.index({ webUserId: 1, isActive: 1 });
UserOdooMappingSchema.index({ webUserId: 1, lastUsed: -1 });
UserOdooMappingSchema.index({ odooHost: 1, odooDatabase: 1, odooUsername: 1 });

// Virtual for full Odoo config
UserOdooMappingSchema.virtual('odooConfig').get(function() {
  return {
    host: this.odooHost,
    database: this.odooDatabase,
    username: this.odooUsername,
    password: this.odooPasswordEncrypted, // Will be decrypted by service
    protocol: this.odooProtocol,
    port: this.odooPort,
  };
});

// Methods
UserOdooMappingSchema.methods.updateLastUsed = function() {
  this.lastUsed = new Date();
  this.totalUsageCount += 1;
  return this.save();
};

UserOdooMappingSchema.methods.markConnectionSuccess = function() {
  this.lastSuccessfulConnection = new Date();
  this.connectionAttempts = 0;
  this.lastConnectionError = undefined;
  return this.save();
};

UserOdooMappingSchema.methods.markConnectionFailure = function(error: string) {
  this.connectionAttempts += 1;
  this.lastConnectionError = error;
  return this.save();
};
