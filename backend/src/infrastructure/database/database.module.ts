import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserOdooMapping, UserOdooMappingSchema } from './schemas/user-odoo-mapping.schema';

@Module({
  imports: [
    // MongoDB connection with configuration
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const uri = configService.get<string>('MONGODB_URI') || 
                   'mongodb://localhost:27017/universal-odoo-adapter';
        
        return {
          uri,
          useNewUrlParser: true,
          useUnifiedTopology: true,
          // Connection pool settings
          maxPoolSize: 10,
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000,
          // Retry settings
          retryWrites: true,
          retryReads: true,
          // Compression
          compressors: ['zlib'],
          // Monitoring
          monitorCommands: process.env.NODE_ENV === 'development',
        };
      },
      inject: [ConfigService],
    }),

    // Register schemas
    MongooseModule.forFeature([
      { name: UserOdooMapping.name, schema: UserOdooMappingSchema },
    ]),
  ],
  exports: [
    MongooseModule,
  ],
})
export class DatabaseModule {}
