import { Module } from '@nestjs/common';

// Presentation layer (Gateway controllers)
import { OdooV1Controller } from '../presentation/controllers/v1/odoo-v1.controller';
import { ApiVersionController } from '../presentation/controllers/gateway/api-version.controller';

/**
 * Global Odoo Module - Contains only gateway controllers
 *
 * Note: Core Odoo services are now provided by SharedModule
 * This module only contains the generic API gateway controllers
 */
@Module({
  imports: [], // No imports needed - SharedModule provides all services
  providers: [], // No providers - all moved to SharedModule
  controllers: [
    ApiVersionController, // API information and health endpoints
    OdooV1Controller,     // Generic Odoo operations (gateway)
  ],
  exports: [], // No exports - SharedModule handles all exports
})
export class OdooModule {}
