import { Module } from '@nestjs/common';

// Protocol implementations
import { XmlRpcProtocol } from './adapters/protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from './adapters/protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from './adapters/protocols/rest/rest-protocol';

// Version adapters
import { OdooV18Adapter } from './adapters/odoo/version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './adapters/odoo/version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './adapters/odoo/version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './adapters/odoo/version-adapters/odoo-v13-adapter';

// Main adapter
import { UniversalOdooAdapter } from './adapters/odoo/universal-odoo-adapter';
import { OdooConnectionPoolService } from './adapters/odoo/odoo-connection-pool.service';
import { UserContextService } from './adapters/odoo/user-context.service';
import { PerformanceMonitorService } from './adapters/odoo/performance-monitor.service';

// Application layer
import { OdooConnectionUseCase } from '../application/use-cases/odoo-connection.use-case';

// Presentation layer
import { OdooV1Controller } from '../presentation/controllers/v1/odoo-v1.controller';
import { ApiVersionController } from '../presentation/controllers/api-version.controller';

// Domain interfaces - using string token for DI
const ODOO_ADAPTER_TOKEN = 'IOdooAdapter';

@Module({
  providers: [
    // Protocol implementations
    XmlRpcProtocol,
    JsonRpcProtocol,
    RestApiProtocol,

    // Version adapters
    OdooV18Adapter,
    OdooV17Adapter,
    OdooV15Adapter,
    OdooV13Adapter,

    // Main adapter and services
    UniversalOdooAdapter,
    OdooConnectionPoolService,
    UserContextService,
    PerformanceMonitorService,
    {
      provide: ODOO_ADAPTER_TOKEN,
      useClass: UniversalOdooAdapter,
    },

    // Use cases
    OdooConnectionUseCase,
  ],
  controllers: [
    ApiVersionController,
    OdooV1Controller, // Version 1 controller
  ],
  exports: [
    ODOO_ADAPTER_TOKEN,
    OdooConnectionUseCase,
    UniversalOdooAdapter,
    OdooConnectionPoolService,
    UserContextService,
    PerformanceMonitorService,
  ],
})
export class OdooModule {}
