import { Module, Global } from '@nestjs/common';
import { DatabaseModule } from '../infrastructure/database/database.module';
import { AuthModule } from '../infrastructure/auth/auth.module';

// Shared application services
import { OdooConnectionUseCase } from './application/use-cases/odoo-connection.use-case';

// Shared infrastructure services
import { UniversalOdooAdapter } from '../infrastructure/adapters/odoo/universal-odoo-adapter';
import { OdooConnectionPoolService } from '../infrastructure/adapters/odoo/odoo-connection-pool.service';
import { UserContextService } from '../infrastructure/adapters/odoo/user-context.service';
import { UserOdooMappingService } from '../infrastructure/adapters/odoo/user-odoo-mapping.service';
import { PerformanceMonitorService } from '../infrastructure/adapters/odoo/performance-monitor.service';

// Protocol implementations
import { XmlRpcProtocol } from '../infrastructure/adapters/protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../infrastructure/adapters/protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../infrastructure/adapters/protocols/rest/rest-protocol';

// Version adapters
import { OdooV18Adapter } from '../infrastructure/adapters/odoo/version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from '../infrastructure/adapters/odoo/version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from '../infrastructure/adapters/odoo/version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from '../infrastructure/adapters/odoo/version-adapters/odoo-v13-adapter';

// Domain interfaces - using string token for DI
const ODOO_ADAPTER_TOKEN = 'IOdooAdapter';

/**
 * Shared Module - Contains common functionality used across all modules
 * This is the "Shared Kernel" in DDD terminology
 * 
 * @Global decorator makes this module available globally without explicit imports
 */
@Global()
@Module({
  imports: [
    DatabaseModule,
    AuthModule,
  ],
  providers: [
    // Protocol implementations
    XmlRpcProtocol,
    JsonRpcProtocol,
    RestApiProtocol,

    // Version adapters
    OdooV18Adapter,
    OdooV17Adapter,
    OdooV15Adapter,
    OdooV13Adapter,

    // Core Odoo services
    UniversalOdooAdapter,
    OdooConnectionPoolService,
    UserContextService,
    UserOdooMappingService,
    PerformanceMonitorService,
    {
      provide: ODOO_ADAPTER_TOKEN,
      useClass: UniversalOdooAdapter,
    },

    // Shared application services
    OdooConnectionUseCase,
  ],
  exports: [
    // Export database and auth modules
    DatabaseModule,
    AuthModule,

    // Export core Odoo services for use in domain modules
    ODOO_ADAPTER_TOKEN,
    OdooConnectionUseCase,
    UniversalOdooAdapter,
    OdooConnectionPoolService,
    UserContextService,
    UserOdooMappingService,
    PerformanceMonitorService,

    // Export protocol implementations
    XmlRpcProtocol,
    JsonRpcProtocol,
    RestApiProtocol,

    // Export version adapters
    OdooV18Adapter,
    OdooV17Adapter,
    OdooV15Adapter,
    OdooV13Adapter,
  ],
})
export class SharedModule {}

/**
 * Shared Module Configuration
 * 
 * This module provides the shared kernel for all domain modules:
 * 
 * 1. **Shared Domain Logic**:
 *    - Base entities and value objects
 *    - Common repository interfaces
 *    - Shared domain services
 * 
 * 2. **Shared Application Services**:
 *    - OdooConnectionUseCase (core connection logic)
 *    - Common DTOs and interfaces
 * 
 * 3. **Shared Infrastructure**:
 *    - Universal Odoo adapter
 *    - Connection pooling
 *    - User context management
 *    - Authentication services
 *    - Database services
 * 
 * 4. **Protocol & Version Support**:
 *    - XML-RPC, JSON-RPC, REST protocols
 *    - Odoo version adapters (13, 15, 17, 18)
 *    - Automatic version detection
 * 
 * **Usage in Domain Modules**:
 * Domain modules can inject any of the exported services:
 * 
 * ```typescript
 * @Injectable()
 * export class CrmService {
 *   constructor(
 *     private readonly odooConnection: OdooConnectionUseCase,
 *     @Inject('IOdooAdapter') private readonly adapter: IOdooAdapter,
 *   ) {}
 * }
 * ```
 * 
 * **Benefits**:
 * - Single source of truth for Odoo connectivity
 * - Consistent authentication and user management
 * - Shared connection pooling for performance
 * - Centralized configuration and monitoring
 * - Easy testing with mock implementations
 */
