import { IsString, IsOptional, <PERSON>N<PERSON>ber, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class OdooConnectionDto {
  @ApiProperty({ description: 'Odoo server host' })
  @IsString()
  host: string;

  @ApiProperty({ description: 'Database name' })
  @IsString()
  database: string;

  @ApiProperty({ description: 'Username' })
  @IsString()
  username: string;

  @ApiProperty({ description: 'Password' })
  @IsString()
  password: string;

  @ApiPropertyOptional({ description: 'Port number', default: 80 })
  @IsOptional()
  @IsNumber()
  port?: number;

  @ApiPropertyOptional({
    description: 'Protocol',
    enum: ['http', 'https'],
    default: 'http',
  })
  @IsOptional()
  @IsEnum(['http', 'https'])
  protocol?: 'http' | 'https';
}

export class SearchReadDto {
  @ApiPropertyOptional({ description: 'Domain filter', type: [Array] })
  @IsOptional()
  domain?: any[];

  @ApiPropertyOptional({ description: 'Fields to retrieve', type: [String] })
  @IsOptional()
  fields?: string[];

  @ApiPropertyOptional({ description: 'Limit number of records' })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({ description: 'Offset for pagination' })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiPropertyOptional({ description: 'Order by field' })
  @IsOptional()
  @IsString()
  order?: string;
}

export class CreateRecordDto {
  @ApiProperty({ description: 'Record values', type: Object })
  values: Record<string, any>;
}

export class UpdateRecordDto {
  @ApiProperty({ description: 'Record IDs', type: [Number] })
  @IsNumber({}, { each: true })
  ids: number[];

  @ApiProperty({ description: 'Values to update', type: Object })
  values: Record<string, any>;
}

export class DeleteRecordDto {
  @ApiProperty({ description: 'Record IDs to delete', type: [Number] })
  @IsNumber({}, { each: true })
  ids: number[];
}
