/**
 * Base Entity for all Odoo-related domain entities
 * Provides common properties and behaviors
 */
export abstract class OdooBaseModel {
  constructor(
    public readonly id: number,
    public readonly createdAt?: Date,
    public readonly updatedAt?: Date,
  ) {
    this.createdAt = createdAt || new Date();
    this.updatedAt = updatedAt || new Date();
  }

  /**
   * Check if entity is new (not persisted)
   */
  isNew(): boolean {
    return this.id === 0 || this.id === undefined;
  }

  /**
   * Check if entity has been modified
   */
  isModified(): boolean {
    return this.updatedAt && this.createdAt && 
           this.updatedAt.getTime() > this.createdAt.getTime();
  }

  /**
   * Get entity age in days
   */
  getAgeInDays(): number {
    if (!this.createdAt) return 0;
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - this.createdAt.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Get last modified time in hours
   */
  getLastModifiedHours(): number {
    if (!this.updatedAt) return 0;
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - this.updatedAt.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60));
  }

  /**
   * Abstract method for converting to plain object
   */
  abstract toPlainObject(): any;

  /**
   * Common equality check based on ID
   */
  equals(other: OdooBaseModel): boolean {
    return this.id === other.id && this.constructor === other.constructor;
  }

  /**
   * String representation
   */
  toString(): string {
    return `${this.constructor.name}(id=${this.id})`;
  }
}
