import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserOdooMappingDocument = UserOdooMapping & Document;

@Schema({
  collection: 'user_odoo_mappings',
  timestamps: true, // Automatically adds createdAt and updatedAt
})
export class UserOdooMapping {
  @Prop({ required: true, index: true })
  webUserId: string;

  @Prop({ required: true, index: true })
  odooInstanceId: string;

  @Prop({ required: true })
  odooHost: string;

  @Prop({ required: true })
  odooDatabase: string;

  @Prop({ required: true })
  odooUsername: string;

  @Prop({ required: true })
  odooPasswordEncrypted: string;

  @Prop({ default: 'https' })
  odooProtocol: string;

  @Prop({ default: 443 })
  odooPort: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: Date.now })
  lastUsed: Date;

  @Prop({ type: Object })
  odooConfig: {
    host: string;
    database: string;
    username: string;
    password: string;
    protocol: string;
    port: number;
  };

  // Virtual fields for easier access
  get connectionKey(): string {
    return `${this.webUserId}:${this.odooHost}:${this.odooDatabase}:${this.odooUsername}`;
  }

  get isExpired(): boolean {
    const now = new Date();
    const lastUsedTime = new Date(this.lastUsed);
    const diffHours = (now.getTime() - lastUsedTime.getTime()) / (1000 * 60 * 60);
    return diffHours > 24; // Consider expired after 24 hours
  }

  // Instance methods
  updateLastUsed(): void {
    this.lastUsed = new Date();
  }

  deactivate(): void {
    this.isActive = false;
  }

  activate(): void {
    this.isActive = true;
  }

  // Note: Static methods will be added to the schema below

  // Compound indexes for better query performance
  static getIndexes() {
    return [
      { webUserId: 1, odooInstanceId: 1 }, // Unique compound index
      { webUserId: 1, isActive: 1 }, // For finding user's active connections
      { lastUsed: 1 }, // For cleanup operations
      { isActive: 1, lastUsed: 1 }, // For finding active connections to cleanup
    ];
  }
}

export const UserOdooMappingSchema = SchemaFactory.createForClass(UserOdooMapping);

// Add compound indexes
UserOdooMappingSchema.index({ webUserId: 1, odooInstanceId: 1 }, { unique: true });
UserOdooMappingSchema.index({ webUserId: 1, isActive: 1 });
UserOdooMappingSchema.index({ lastUsed: 1 });
UserOdooMappingSchema.index({ isActive: 1, lastUsed: 1 });

// Add virtual fields
UserOdooMappingSchema.virtual('connectionKey').get(function() {
  return `${this.webUserId}:${this.odooHost}:${this.odooDatabase}:${this.odooUsername}`;
});

UserOdooMappingSchema.virtual('isExpired').get(function() {
  const now = new Date();
  const lastUsedTime = new Date(this.lastUsed);
  const diffHours = (now.getTime() - lastUsedTime.getTime()) / (1000 * 60 * 60);
  return diffHours > 24;
});

// Add instance methods
UserOdooMappingSchema.methods.updateLastUsed = function() {
  this.lastUsed = new Date();
  return this.save();
};

UserOdooMappingSchema.methods.deactivate = function() {
  this.isActive = false;
  return this.save();
};

UserOdooMappingSchema.methods.activate = function() {
  this.isActive = true;
  return this.save();
};

// Add static methods
UserOdooMappingSchema.statics.findByWebUserId = function(webUserId: string) {
  return this.find({ webUserId, isActive: true });
};

UserOdooMappingSchema.statics.findByInstanceId = function(webUserId: string, odooInstanceId: string) {
  return this.findOne({ webUserId, odooInstanceId, isActive: true });
};

UserOdooMappingSchema.statics.findActiveConnections = function() {
  return this.find({ isActive: true });
};

UserOdooMappingSchema.statics.findExpiredConnections = function() {
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return this.find({ lastUsed: { $lt: twentyFourHoursAgo } });
};
