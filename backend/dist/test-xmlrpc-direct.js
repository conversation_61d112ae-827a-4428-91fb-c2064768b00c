#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testXmlRpcDirect = testXmlRpcDirect;
const common_1 = require("@nestjs/common");
const xmlrpc = require("xmlrpc");
async function testXmlRpcDirect() {
    const logger = new common_1.Logger('XmlRpcDirectTest');
    const host = 'odoo18.bestmix.one';
    const port = 443;
    const database = 'bestmix_27_6';
    const username = 'tuan.le';
    const password = 'drb6mtw3bah8byu*VEV';
    logger.log('🔍 Testing direct XML-RPC connection...');
    logger.log(`Host: ${host}:${port}`);
    logger.log(`Database: ${database}`);
    logger.log(`Username: ${username}`);
    try {
        logger.log('\n=== Test 1: Version Check ===');
        const commonClient = xmlrpc.createClient({
            host,
            port,
            path: '/xmlrpc/2/common',
        });
        const version = await new Promise((resolve, reject) => {
            commonClient.methodCall('version', [], (error, value) => {
                if (error) {
                    reject(error);
                }
                else {
                    resolve(value);
                }
            });
        });
        logger.log(`Version: ${JSON.stringify(version, null, 2)}`);
        logger.log('\n=== Test 2: Authentication ===');
        const uid = await new Promise((resolve, reject) => {
            commonClient.methodCall('authenticate', [database, username, password, {}], (error, value) => {
                if (error) {
                    reject(error);
                }
                else {
                    resolve(value);
                }
            });
        });
        logger.log(`User ID: ${uid}`);
        if (uid) {
            logger.log('\n=== Test 3: Data Query ===');
            const objectClient = xmlrpc.createClient({
                host,
                port,
                path: '/xmlrpc/2/object',
            });
            const users = await new Promise((resolve, reject) => {
                objectClient.methodCall('execute_kw', [
                    database,
                    uid,
                    password,
                    'res.users',
                    'search_read',
                    [[['id', '=', uid]]],
                    { fields: ['id', 'name', 'login', 'email'], limit: 1 }
                ], (error, value) => {
                    if (error) {
                        reject(error);
                    }
                    else {
                        resolve(value);
                    }
                });
            });
            logger.log(`Current User: ${JSON.stringify(users, null, 2)}`);
        }
        logger.log('\n✅ XML-RPC direct test completed successfully');
    }
    catch (error) {
        logger.error('❌ XML-RPC direct test failed:', error);
        logger.error('Error details:', {
            message: error.message,
            code: error.code,
            stack: error.stack
        });
    }
}
if (require.main === module) {
    testXmlRpcDirect()
        .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=test-xmlrpc-direct.js.map