#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testSimpleConnection = testSimpleConnection;
const common_1 = require("@nestjs/common");
async function testSimpleConnection() {
    const logger = new common_1.Logger('SimpleConnectionTest');
    const testUrl = 'https://odoo18.bestmix.one/';
    logger.log('🔍 Testing simple HTTP connection...');
    logger.log(`URL: ${testUrl}`);
    try {
        logger.log('\n=== Test 1: Basic HTTP Request ===');
        const response = await fetch(testUrl);
        logger.log(`Status: ${response.status} ${response.statusText}`);
        logger.log(`Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);
        logger.log('\n=== Test 2: Odoo Detection ===');
        const text = await response.text();
        if (text.includes('odoo') || text.includes('Odoo')) {
            logger.log('✅ Detected Odoo instance');
        }
        else {
            logger.log('❌ Not detected as Odoo instance');
        }
        logger.log('\n=== Test 3: Version Detection ===');
        try {
            const versionUrl = `${testUrl}web/webclient/version_info`;
            const versionResponse = await fetch(versionUrl);
            logger.log(`Version endpoint status: ${versionResponse.status}`);
            if (versionResponse.ok) {
                const versionData = await versionResponse.json();
                logger.log(`Version data: ${JSON.stringify(versionData, null, 2)}`);
            }
        }
        catch (versionError) {
            logger.warn('Version detection failed:', versionError.message);
        }
        logger.log('\n=== Test 4: XML-RPC Endpoint ===');
        try {
            const xmlrpcUrl = `${testUrl}xmlrpc/2/common`;
            const xmlrpcResponse = await fetch(xmlrpcUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'text/xml',
                },
                body: `<?xml version="1.0"?>
<methodCall>
  <methodName>version</methodName>
  <params></params>
</methodCall>`
            });
            logger.log(`XML-RPC endpoint status: ${xmlrpcResponse.status}`);
            if (xmlrpcResponse.ok) {
                const xmlrpcText = await xmlrpcResponse.text();
                logger.log(`XML-RPC response: ${xmlrpcText.substring(0, 200)}...`);
            }
        }
        catch (xmlrpcError) {
            logger.warn('XML-RPC test failed:', xmlrpcError.message);
        }
        logger.log('\n=== Test 5: JSON-RPC Endpoint ===');
        try {
            const jsonrpcUrl = `${testUrl}jsonrpc`;
            const jsonrpcResponse = await fetch(jsonrpcUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        service: 'common',
                        method: 'version',
                        args: []
                    },
                    id: 1
                })
            });
            logger.log(`JSON-RPC endpoint status: ${jsonrpcResponse.status}`);
            if (jsonrpcResponse.ok) {
                const jsonrpcData = await jsonrpcResponse.json();
                logger.log(`JSON-RPC response: ${JSON.stringify(jsonrpcData, null, 2)}`);
            }
        }
        catch (jsonrpcError) {
            logger.warn('JSON-RPC test failed:', jsonrpcError.message);
        }
        logger.log('\n✅ Simple connection test completed');
    }
    catch (error) {
        logger.error('❌ Simple connection test failed:', error);
        logger.error('Error details:', {
            message: error.message,
            cause: error.cause,
            stack: error.stack
        });
    }
}
if (require.main === module) {
    testSimpleConnection()
        .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=test-simple-connection.js.map