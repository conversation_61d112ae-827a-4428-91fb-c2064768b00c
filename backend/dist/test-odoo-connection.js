#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testOdooConnection = testOdooConnection;
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const odoo_module_1 = require("./src/infrastructure/odoo.module");
const odoo_connection_pool_service_1 = require("./src/infrastructure/adapters/odoo/odoo-connection-pool.service");
async function testOdooConnection() {
    const logger = new common_1.Logger('OdooConnectionTest');
    try {
        logger.log('🚀 Starting Odoo Connection Test...');
        const app = await core_1.NestFactory.createApplicationContext(odoo_module_1.OdooModule);
        const connectionPool = app.get(odoo_connection_pool_service_1.OdooConnectionPoolService);
        await connectionPool.onModuleInit();
        const testConfig = {
            host: 'https://odoo18.bestmix.one/',
            database: 'bestmix_27_6',
            username: 'tuan.le',
            password: 'drb6mtw3bah8byu*VEV',
            preferredProtocol: 'jsonrpc',
        };
        const userContext = {
            userId: 'test-user-tuan',
            sessionId: `test-session-${Date.now()}`,
            odooConfig: testConfig,
        };
        logger.log('📡 Testing connection to Odoo instance...');
        logger.log(`Host: ${testConfig.host}`);
        logger.log(`Database: ${testConfig.database}`);
        logger.log(`Username: ${testConfig.username}`);
        logger.log('\n=== Test 1: Connection Pool ===');
        const startTime = Date.now();
        const adapter = await connectionPool.getConnection(userContext);
        const connectionTime = Date.now() - startTime;
        logger.log(`✅ Connection established in ${connectionTime}ms`);
        logger.log('\n=== Test 2: Version Detection ===');
        const versionInfo = adapter.getVersionInfo();
        logger.log(`Odoo Version: ${versionInfo?.series} (${versionInfo?.edition})`);
        logger.log(`Major Version: ${versionInfo?.major}`);
        logger.log('\n=== Test 3: Capabilities ===');
        const capabilities = adapter.getCapabilities();
        logger.log(`JSON-RPC Support: ${capabilities?.hasJsonRpc ? '✅' : '❌'}`);
        logger.log(`REST API Support: ${capabilities?.hasRestApi ? '✅' : '❌'}`);
        logger.log(`Supported Auth Methods: ${capabilities?.supportedAuthMethods?.join(', ') || 'N/A'}`);
        logger.log('\n=== Test 4: Data Operations ===');
        const currentUser = await adapter.searchRead('res.users', [['id', '=', 1]], {
            fields: ['id', 'name', 'login', 'email'],
            limit: 1
        });
        logger.log(`Current User: ${JSON.stringify(currentUser[0], null, 2)}`);
        const partners = await adapter.searchRead('res.partner', [['is_company', '=', true]], {
            fields: ['id', 'name', 'email', 'phone'],
            limit: 5
        });
        logger.log(`Found ${partners.length} companies:`);
        partners.forEach((partner, index) => {
            logger.log(`  ${index + 1}. ${partner.name} (ID: ${partner.id})`);
        });
        const products = await adapter.searchRead('product.product', [['sale_ok', '=', true]], {
            fields: ['id', 'name', 'default_code', 'list_price'],
            limit: 5
        });
        logger.log(`Found ${products.length} saleable products:`);
        products.forEach((product, index) => {
            logger.log(`  ${index + 1}. ${product.name} - $${product.list_price} (Code: ${product.default_code || 'N/A'})`);
        });
        logger.log('\n=== Test 5: Pool Health Check ===');
        const healthCheck = await connectionPool.healthCheck();
        logger.log(`Total Connections: ${healthCheck.totalConnections}`);
        logger.log(`Healthy Connections: ${healthCheck.healthyConnections}`);
        logger.log(`Unhealthy Connections: ${healthCheck.unhealthyConnections}`);
        logger.log('\n=== Test 6: Pool Metrics ===');
        const metrics = connectionPool.getPoolMetrics();
        logger.log(`Pool Size: ${metrics.size}/${metrics.maxSize} (${metrics.utilizationPercent.toFixed(1)}%)`);
        logger.log(`Total Usage Count: ${metrics.totalUsageCount}`);
        logger.log(`Average Usage Count: ${metrics.averageUsageCount.toFixed(1)}`);
        logger.log(`Healthy Connections: ${metrics.healthyConnections}`);
        logger.log('\n=== Test 7: Performance Test ===');
        const perfStartTime = Date.now();
        const performancePromises = [
            adapter.searchRead('res.users', [], { limit: 10 }),
            adapter.searchRead('res.partner', [['customer_rank', '>', 0]], { limit: 10 }),
            adapter.searchRead('product.product', [], { limit: 10 }),
            adapter.searchRead('sale.order', [], { limit: 5 }),
            adapter.searchRead('account.move', [], { limit: 5 }),
        ];
        const results = await Promise.allSettled(performancePromises);
        const perfDuration = Date.now() - perfStartTime;
        const successCount = results.filter(r => r.status === 'fulfilled').length;
        logger.log(`Performance Test: ${successCount}/${results.length} operations succeeded in ${perfDuration}ms`);
        results.forEach((result, index) => {
            const operations = ['Users', 'Customers', 'Products', 'Sales Orders', 'Invoices'];
            if (result.status === 'fulfilled') {
                logger.log(`  ✅ ${operations[index]}: ${result.value.length} records`);
            }
            else {
                logger.log(`  ❌ ${operations[index]}: ${result.reason.message}`);
            }
        });
        logger.log('\n=== Test 8: Connection Reuse ===');
        const reuseStartTime = Date.now();
        const adapter2 = await connectionPool.getConnection(userContext);
        const reuseTime = Date.now() - reuseStartTime;
        logger.log(`Connection reuse time: ${reuseTime}ms`);
        logger.log(`Same adapter instance: ${adapter === adapter2 ? '✅' : '❌'}`);
        logger.log('\n=== 🎉 Test Summary ===');
        logger.log('✅ Connection established successfully');
        logger.log('✅ Version detection working');
        logger.log('✅ Capabilities detection working');
        logger.log('✅ Data operations working');
        logger.log('✅ Pool health monitoring working');
        logger.log('✅ Performance metrics working');
        logger.log('✅ Connection reuse working');
        logger.log('\n🚀 All tests passed! Odoo connection is working perfectly.');
        await connectionPool.onModuleDestroy();
        await app.close();
    }
    catch (error) {
        logger.error('❌ Test failed:', error);
        logger.error('Stack trace:', error.stack);
        process.exit(1);
    }
}
if (require.main === module) {
    testOdooConnection()
        .then(() => {
        console.log('\n✅ Test completed successfully');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=test-odoo-connection.js.map