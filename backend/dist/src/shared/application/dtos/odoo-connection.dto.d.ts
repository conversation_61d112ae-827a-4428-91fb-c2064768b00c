export declare class OdooConnectionDto {
    host: string;
    database: string;
    username: string;
    password: string;
    port?: number;
    protocol?: 'http' | 'https';
}
export declare class SearchReadDto {
    domain?: any[];
    fields?: string[];
    limit?: number;
    offset?: number;
    order?: string;
}
export declare class CreateRecordDto {
    values: Record<string, any>;
}
export declare class UpdateRecordDto {
    ids: number[];
    values: Record<string, any>;
}
export declare class DeleteRecordDto {
    ids: number[];
}
