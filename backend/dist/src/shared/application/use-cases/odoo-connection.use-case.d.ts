import { OdooConnectionConfig, SearchReadOptions } from '../../domain/value-objects/odoo-connection-config';
import { OdooConnectionPoolService } from '../../../infrastructure/adapters/odoo/odoo-connection-pool.service';
import { UserContextService } from '../../../infrastructure/adapters/odoo/user-context.service';
import { UserOdooMappingService } from '../../../infrastructure/adapters/odoo/user-odoo-mapping.service';
export declare class OdooConnectionUseCase {
    private readonly connectionPoolService;
    private readonly userContextService;
    private readonly userOdooMappingService;
    private readonly logger;
    constructor(connectionPoolService: OdooConnectionPoolService, userContextService: UserContextService, userOdooMappingService: UserOdooMappingService);
    connect(config: OdooConnectionConfig, odooInstanceId?: string): Promise<void>;
    getVersionInfo(): Promise<import("../../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null>;
    getCapabilities(): Promise<import("../../../domain/value-objects/odoo-connection-config").OdooCapabilities | null>;
    searchRead<T = any>(model: string, domain?: any[], options?: SearchReadOptions): Promise<T[]>;
    create<T = any>(model: string, values: Partial<T>): Promise<number>;
    update<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean>;
    delete(model: string, ids: number[]): Promise<boolean>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    disconnect(): Promise<void>;
    getPoolStats(): {
        size: number;
        maxSize: number;
        connections: {
            key: string;
            lastUsed: Date;
            host: string;
            database: string;
        }[];
    };
    getPoolMetrics(): import("../../../infrastructure/adapters/odoo/odoo-connection-pool.service").PoolMetrics;
    healthCheck(): Promise<{
        totalConnections: number;
        healthyConnections: number;
        unhealthyConnections: number;
        details: import("../../../infrastructure/adapters/odoo/odoo-connection-pool.service").HealthCheckResult[];
        poolMetrics: import("../../../infrastructure/adapters/odoo/odoo-connection-pool.service").PoolMetrics;
    }>;
    executeBatch<T>(operations: Array<{
        userContext: any;
        operation: (adapter: any) => Promise<T>;
    }>): Promise<{
        success: boolean;
        result?: T | undefined;
        error?: string;
        userId: string;
    }[]>;
    cleanupStaleConnections(maxAgeMinutes?: number): Promise<number>;
    refreshExpiringConnections(refreshThresholdMinutes?: number): Promise<number>;
    disconnectUser(): Promise<void>;
    getUserOdooInstances(): Promise<Array<{
        instanceId: string;
        host: string;
        database: string;
        username: string;
        isActive: boolean;
        lastUsed: Date;
    }>>;
    disconnectInstance(odooInstanceId: string): Promise<void>;
    private getAdapter;
    private extractUserFromRequest;
    private decodeJWT;
}
