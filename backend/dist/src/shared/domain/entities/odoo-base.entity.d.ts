export declare abstract class OdooBaseModel {
    readonly id: number;
    readonly createdAt?: Date | undefined;
    readonly updatedAt?: Date | undefined;
    constructor(id: number, createdAt?: Date | undefined, updatedAt?: Date | undefined);
    isNew(): boolean;
    isModified(): boolean;
    getAgeInDays(): number;
    getLastModifiedHours(): number;
    abstract toPlainObject(): any;
    equals(other: OdooBaseModel): boolean;
    toString(): string;
}
