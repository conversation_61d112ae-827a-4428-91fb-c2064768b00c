{"version": 3, "file": "odoo-base.entity.js", "sourceRoot": "", "sources": ["../../../../../src/shared/domain/entities/odoo-base.entity.ts"], "names": [], "mappings": ";;;AAIA,MAAsB,aAAa;IAEf;IACA;IACA;IAHlB,YACkB,EAAU,EACV,SAAgB,EAChB,SAAgB;QAFhB,OAAE,GAAF,EAAE,CAAQ;QACV,cAAS,GAAT,SAAS,CAAO;QAChB,cAAS,GAAT,SAAS,CAAO;QAEhC,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;IAC3C,CAAC;IAKD,KAAK;QACH,OAAO,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC;IAChD,CAAC;IAKD,UAAU;QACR,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS;YACnC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9D,CAAC;IAKD,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAKD,oBAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAUD,MAAM,CAAC,KAAoB;QACzB,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW,CAAC;IACxE,CAAC;IAKD,QAAQ;QACN,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,OAAO,IAAI,CAAC,EAAE,GAAG,CAAC;IACnD,CAAC;CACF;AA/DD,sCA+DC"}