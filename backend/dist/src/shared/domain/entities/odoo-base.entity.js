"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooBaseModel = void 0;
class OdooBaseModel {
    id;
    createdAt;
    updatedAt;
    constructor(id, createdAt, updatedAt) {
        this.id = id;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.createdAt = createdAt || new Date();
        this.updatedAt = updatedAt || new Date();
    }
    isNew() {
        return this.id === 0 || this.id === undefined;
    }
    isModified() {
        return !!(this.updatedAt && this.createdAt &&
            this.updatedAt.getTime() > this.createdAt.getTime());
    }
    getAgeInDays() {
        if (!this.createdAt)
            return 0;
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - this.createdAt.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    getLastModifiedHours() {
        if (!this.updatedAt)
            return 0;
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - this.updatedAt.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60));
    }
    equals(other) {
        return this.id === other.id && this.constructor === other.constructor;
    }
    toString() {
        return `${this.constructor.name}(id=${this.id})`;
    }
}
exports.OdooBaseModel = OdooBaseModel;
//# sourceMappingURL=odoo-base.entity.js.map