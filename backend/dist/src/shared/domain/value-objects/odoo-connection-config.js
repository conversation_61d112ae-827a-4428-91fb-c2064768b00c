"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthMethod = exports.ProtocolType = void 0;
var ProtocolType;
(function (ProtocolType) {
    ProtocolType["XMLRPC"] = "xmlrpc";
    ProtocolType["JSONRPC"] = "jsonrpc";
    ProtocolType["REST"] = "rest";
    ProtocolType["GRAPHQL"] = "graphql";
    ProtocolType["WEBSOCKET"] = "websocket";
})(ProtocolType || (exports.ProtocolType = ProtocolType = {}));
var AuthMethod;
(function (AuthMethod) {
    AuthMethod["PASSWORD"] = "password";
    AuthMethod["API_KEY"] = "api_key";
    AuthMethod["OAUTH2"] = "oauth2";
    AuthMethod["TOKEN"] = "token";
})(AuthMethod || (exports.AuthMethod = AuthMethod = {}));
//# sourceMappingURL=odoo-connection-config.js.map