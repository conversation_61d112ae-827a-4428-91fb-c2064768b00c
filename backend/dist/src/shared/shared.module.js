"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedModule = void 0;
const common_1 = require("@nestjs/common");
const database_module_1 = require("./infrastructure/database/database.module");
const auth_module_1 = require("../infrastructure/auth/auth.module");
const odoo_connection_use_case_1 = require("./application/use-cases/odoo-connection.use-case");
const universal_odoo_adapter_1 = require("../infrastructure/adapters/odoo/universal-odoo-adapter");
const odoo_connection_pool_service_1 = require("../infrastructure/adapters/odoo/odoo-connection-pool.service");
const user_context_service_1 = require("../infrastructure/adapters/odoo/user-context.service");
const user_odoo_mapping_service_1 = require("../infrastructure/adapters/odoo/user-odoo-mapping.service");
const performance_monitor_service_1 = require("../infrastructure/adapters/odoo/performance-monitor.service");
const xmlrpc_protocol_1 = require("../infrastructure/adapters/protocols/xmlrpc/xmlrpc-protocol");
const jsonrpc_protocol_1 = require("../infrastructure/adapters/protocols/jsonrpc/jsonrpc-protocol");
const rest_protocol_1 = require("../infrastructure/adapters/protocols/rest/rest-protocol");
const odoo_v18_adapter_1 = require("../infrastructure/adapters/odoo/version-adapters/odoo-v18-adapter");
const odoo_v17_adapter_1 = require("../infrastructure/adapters/odoo/version-adapters/odoo-v17-adapter");
const odoo_v15_adapter_1 = require("../infrastructure/adapters/odoo/version-adapters/odoo-v15-adapter");
const odoo_v13_adapter_1 = require("../infrastructure/adapters/odoo/version-adapters/odoo-v13-adapter");
const ODOO_ADAPTER_TOKEN = 'IOdooAdapter';
let SharedModule = class SharedModule {
};
exports.SharedModule = SharedModule;
exports.SharedModule = SharedModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            database_module_1.DatabaseModule,
            auth_module_1.AuthModule,
        ],
        providers: [
            xmlrpc_protocol_1.XmlRpcProtocol,
            jsonrpc_protocol_1.JsonRpcProtocol,
            rest_protocol_1.RestApiProtocol,
            odoo_v18_adapter_1.OdooV18Adapter,
            odoo_v17_adapter_1.OdooV17Adapter,
            odoo_v15_adapter_1.OdooV15Adapter,
            odoo_v13_adapter_1.OdooV13Adapter,
            universal_odoo_adapter_1.UniversalOdooAdapter,
            odoo_connection_pool_service_1.OdooConnectionPoolService,
            user_context_service_1.UserContextService,
            user_odoo_mapping_service_1.UserOdooMappingService,
            performance_monitor_service_1.PerformanceMonitorService,
            {
                provide: ODOO_ADAPTER_TOKEN,
                useClass: universal_odoo_adapter_1.UniversalOdooAdapter,
            },
            odoo_connection_use_case_1.OdooConnectionUseCase,
        ],
        exports: [
            database_module_1.DatabaseModule,
            auth_module_1.AuthModule,
            ODOO_ADAPTER_TOKEN,
            odoo_connection_use_case_1.OdooConnectionUseCase,
            universal_odoo_adapter_1.UniversalOdooAdapter,
            odoo_connection_pool_service_1.OdooConnectionPoolService,
            user_context_service_1.UserContextService,
            user_odoo_mapping_service_1.UserOdooMappingService,
            performance_monitor_service_1.PerformanceMonitorService,
            xmlrpc_protocol_1.XmlRpcProtocol,
            jsonrpc_protocol_1.JsonRpcProtocol,
            rest_protocol_1.RestApiProtocol,
            odoo_v18_adapter_1.OdooV18Adapter,
            odoo_v17_adapter_1.OdooV17Adapter,
            odoo_v15_adapter_1.OdooV15Adapter,
            odoo_v13_adapter_1.OdooV13Adapter,
        ],
    })
], SharedModule);
//# sourceMappingURL=shared.module.js.map