"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const config_1 = require("@nestjs/config");
const user_odoo_mapping_schema_1 = require("./schemas/user-odoo-mapping.schema");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const uri = configService.get('MONGODB_URI') ||
                        'mongodb://localhost:27017/universal-odoo-adapter';
                    return {
                        uri,
                        useNewUrlParser: true,
                        useUnifiedTopology: true,
                        maxPoolSize: 10,
                        serverSelectionTimeoutMS: 5000,
                        socketTimeoutMS: 45000,
                        retryWrites: true,
                        retryReads: true,
                        compressors: ['zlib'],
                        monitorCommands: process.env.NODE_ENV === 'development',
                    };
                },
                inject: [config_1.ConfigService],
            }),
            mongoose_1.MongooseModule.forFeature([
                { name: user_odoo_mapping_schema_1.UserOdooMapping.name, schema: user_odoo_mapping_schema_1.UserOdooMappingSchema },
            ]),
        ],
        exports: [
            mongoose_1.MongooseModule,
        ],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map