"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOdooMappingSchema = exports.UserOdooMapping = void 0;
const mongoose_1 = require("@nestjs/mongoose");
let UserOdooMapping = class UserOdooMapping {
    webUserId;
    odooInstanceId;
    odooHost;
    odooDatabase;
    odooUsername;
    odooPasswordEncrypted;
    odooProtocol;
    odooPort;
    isActive;
    lastUsed;
    odooConfig;
    get connectionKey() {
        return `${this.webUserId}:${this.odooHost}:${this.odooDatabase}:${this.odooUsername}`;
    }
    get isExpired() {
        const now = new Date();
        const lastUsedTime = new Date(this.lastUsed);
        const diffHours = (now.getTime() - lastUsedTime.getTime()) / (1000 * 60 * 60);
        return diffHours > 24;
    }
    updateLastUsed() {
        this.lastUsed = new Date();
    }
    deactivate() {
        this.isActive = false;
    }
    activate() {
        this.isActive = true;
    }
    static getIndexes() {
        return [
            { webUserId: 1, odooInstanceId: 1 },
            { webUserId: 1, isActive: 1 },
            { lastUsed: 1 },
            { isActive: 1, lastUsed: 1 },
        ];
    }
};
exports.UserOdooMapping = UserOdooMapping;
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "webUserId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooInstanceId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooHost", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooDatabase", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooUsername", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooPasswordEncrypted", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 'https' }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooProtocol", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 443 }),
    __metadata("design:type", Number)
], UserOdooMapping.prototype, "odooPort", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], UserOdooMapping.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], UserOdooMapping.prototype, "lastUsed", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object }),
    __metadata("design:type", Object)
], UserOdooMapping.prototype, "odooConfig", void 0);
exports.UserOdooMapping = UserOdooMapping = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'user_odoo_mappings',
        timestamps: true,
    })
], UserOdooMapping);
exports.UserOdooMappingSchema = mongoose_1.SchemaFactory.createForClass(UserOdooMapping);
exports.UserOdooMappingSchema.index({ webUserId: 1, odooInstanceId: 1 }, { unique: true });
exports.UserOdooMappingSchema.index({ webUserId: 1, isActive: 1 });
exports.UserOdooMappingSchema.index({ lastUsed: 1 });
exports.UserOdooMappingSchema.index({ isActive: 1, lastUsed: 1 });
exports.UserOdooMappingSchema.virtual('connectionKey').get(function () {
    return `${this.webUserId}:${this.odooHost}:${this.odooDatabase}:${this.odooUsername}`;
});
exports.UserOdooMappingSchema.virtual('isExpired').get(function () {
    const now = new Date();
    const lastUsedTime = new Date(this.lastUsed);
    const diffHours = (now.getTime() - lastUsedTime.getTime()) / (1000 * 60 * 60);
    return diffHours > 24;
});
exports.UserOdooMappingSchema.methods.updateLastUsed = function () {
    this.lastUsed = new Date();
    return this.save();
};
exports.UserOdooMappingSchema.methods.deactivate = function () {
    this.isActive = false;
    return this.save();
};
exports.UserOdooMappingSchema.methods.activate = function () {
    this.isActive = true;
    return this.save();
};
exports.UserOdooMappingSchema.statics.findByWebUserId = function (webUserId) {
    return this.find({ webUserId, isActive: true });
};
exports.UserOdooMappingSchema.statics.findByInstanceId = function (webUserId, odooInstanceId) {
    return this.findOne({ webUserId, odooInstanceId, isActive: true });
};
exports.UserOdooMappingSchema.statics.findActiveConnections = function () {
    return this.find({ isActive: true });
};
exports.UserOdooMappingSchema.statics.findExpiredConnections = function () {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    return this.find({ lastUsed: { $lt: twentyFourHoursAgo } });
};
//# sourceMappingURL=user-odoo-mapping.schema.js.map