{"version": 3, "file": "user-odoo-mapping.schema.js", "sourceRoot": "", "sources": ["../../../../../../src/shared/infrastructure/database/schemas/user-odoo-mapping.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AASxD,IAAM,eAAe,GAArB,MAAM,eAAe;IAE1B,SAAS,CAAS;IAGlB,cAAc,CAAS;IAGvB,QAAQ,CAAS;IAGjB,YAAY,CAAS;IAGrB,YAAY,CAAS;IAGrB,qBAAqB,CAAS;IAG9B,YAAY,CAAS;IAGrB,QAAQ,CAAS;IAGjB,QAAQ,CAAU;IAGlB,QAAQ,CAAO;IAGf,UAAU,CAOR;IAGF,IAAI,aAAa;QACf,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;IACxF,CAAC;IAED,IAAI,SAAS;QACX,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9E,OAAO,SAAS,GAAG,EAAE,CAAC;IACxB,CAAC;IAGD,cAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;IAC7B,CAAC;IAED,UAAU;QACR,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAKD,MAAM,CAAC,UAAU;QACf,OAAO;YACL,EAAE,SAAS,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE;YACnC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;YAC7B,EAAE,QAAQ,EAAE,CAAC,EAAE;YACf,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;SAC7B,CAAC;IACJ,CAAC;CACF,CAAA;AA7EY,0CAAe;AAE1B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;kDACpB;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;uDACf;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACR;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACJ;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDACJ;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8DACK;AAG9B;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;;qDACN;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;iDACN;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iDACN;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;8BAClB,IAAI;iDAAC;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;mDAQrB;0BAvCS,eAAe;IAJ3B,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,oBAAoB;QAChC,UAAU,EAAE,IAAI;KACjB,CAAC;GACW,eAAe,CA6E3B;AAEY,QAAA,qBAAqB,GAAG,wBAAa,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;AAGnF,6BAAqB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACnF,6BAAqB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3D,6BAAqB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7C,6BAAqB,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAG1D,6BAAqB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC;IACjD,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxF,CAAC,CAAC,CAAC;AAEH,6BAAqB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC;IAC7C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC9E,OAAO,SAAS,GAAG,EAAE,CAAC;AACxB,CAAC,CAAC,CAAC;AAGH,6BAAqB,CAAC,OAAO,CAAC,cAAc,GAAG;IAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;IAC3B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAEF,6BAAqB,CAAC,OAAO,CAAC,UAAU,GAAG;IACzC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACtB,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAEF,6BAAqB,CAAC,OAAO,CAAC,QAAQ,GAAG;IACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACrB,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAGF,6BAAqB,CAAC,OAAO,CAAC,eAAe,GAAG,UAAS,SAAiB;IACxE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAClD,CAAC,CAAC;AAEF,6BAAqB,CAAC,OAAO,CAAC,gBAAgB,GAAG,UAAS,SAAiB,EAAE,cAAsB;IACjG,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACrE,CAAC,CAAC;AAEF,6BAAqB,CAAC,OAAO,CAAC,qBAAqB,GAAG;IACpD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF,6BAAqB,CAAC,OAAO,CAAC,sBAAsB,GAAG;IACrD,MAAM,kBAAkB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACtE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;AAC9D,CAAC,CAAC"}