import { Document } from 'mongoose';
export type UserOdooMappingDocument = UserOdooMapping & Document;
export declare class UserOdooMapping {
    webUserId: string;
    odooInstanceId: string;
    odooHost: string;
    odooDatabase: string;
    odooUsername: string;
    odooPasswordEncrypted: string;
    odooProtocol: string;
    odooPort: number;
    isActive: boolean;
    lastUsed: Date;
    odooConfig: {
        host: string;
        database: string;
        username: string;
        password: string;
        protocol: string;
        port: number;
    };
    get connectionKey(): string;
    get isExpired(): boolean;
    updateLastUsed(): void;
    deactivate(): void;
    activate(): void;
    static getIndexes(): ({
        webUserId: number;
        odooInstanceId: number;
        isActive?: undefined;
        lastUsed?: undefined;
    } | {
        webUserId: number;
        isActive: number;
        odooInstanceId?: undefined;
        lastUsed?: undefined;
    } | {
        lastUsed: number;
        webUserId?: undefined;
        odooInstanceId?: undefined;
        isActive?: undefined;
    } | {
        isActive: number;
        lastUsed: number;
        webUserId?: undefined;
        odooInstanceId?: undefined;
    })[];
}
export declare const UserOdooMappingSchema: import("mongoose").Schema<UserOdooMapping, import("mongoose").Model<UserOdooMapping, any, any, any, Document<unknown, any, UserOdooMapping, any> & UserOdooMapping & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, UserOdooMapping, Document<unknown, {}, import("mongoose").FlatRecord<UserOdooMapping>, {}> & import("mongoose").FlatRecord<UserOdooMapping> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
