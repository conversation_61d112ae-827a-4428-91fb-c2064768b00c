"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooV1Controller = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const odoo_connection_use_case_1 = require("../../../application/use-cases/odoo-connection.use-case");
const odoo_connection_dto_1 = require("../../../application/dtos/odoo-connection.dto");
const jwt_service_1 = require("../../../infrastructure/auth/jwt.service");
const jwt_auth_guard_1 = require("../../../infrastructure/auth/jwt-auth.guard");
const response_builder_service_1 = require("../../../common/services/response-builder.service");
const swagger_examples_dto_1 = require("../../../common/dto/swagger-examples.dto");
let OdooV1Controller = class OdooV1Controller {
    odooConnectionUseCase;
    jwtAuthService;
    responseBuilder;
    constructor(odooConnectionUseCase, jwtAuthService, responseBuilder) {
        this.odooConnectionUseCase = odooConnectionUseCase;
        this.jwtAuthService = jwtAuthService;
        this.responseBuilder = responseBuilder;
    }
    async connect(connectionDto, request) {
        const startTime = Date.now();
        try {
            await this.odooConnectionUseCase.connect(connectionDto);
            const version = await this.odooConnectionUseCase.getVersionInfo();
            const capabilities = await this.odooConnectionUseCase.getCapabilities();
            const ip = request.ip || 'unknown';
            const userAgent = request.headers?.['user-agent'] || 'unknown';
            const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substring(0, 8);
            const webUserId = `anon_${hash}`;
            const instanceHash = Buffer.from(`${connectionDto.host}:${connectionDto.database}:${connectionDto.username}`)
                .toString('base64')
                .replace(/[^a-zA-Z0-9]/g, '')
                .substring(0, 12);
            const odooInstanceId = `odoo_${instanceHash}`;
            const tokenData = await this.jwtAuthService.generateToken({
                sub: webUserId,
                odooInstanceId: odooInstanceId,
                odooHost: connectionDto.host,
                odooDatabase: connectionDto.database,
                odooUsername: connectionDto.username,
                sessionId: this.jwtAuthService.generateSessionId(),
            });
            return this.responseBuilder.success({
                token: tokenData.token,
                expiresIn: tokenData.expiresIn,
                version,
                capabilities,
            }, 'Connected to Odoo successfully', 200, request, {
                connectionTime: Date.now() - startTime,
                odooVersion: version?.series || 'unknown',
                protocol: capabilities?.hasJsonRpc ? 'JSON-RPC' : 'XML-RPC',
            });
        }
        catch (error) {
            throw new common_1.HttpException(this.responseBuilder.internalError('Failed to connect to Odoo: ' + error.message, request, 'CONNECTION_FAILED', {
                connectionTime: Date.now() - startTime,
                odooHost: connectionDto.host,
                odooDatabase: connectionDto.database,
            }), common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async searchRecords(model, searchDto) {
        const result = await this.odooConnectionUseCase.searchRead(model, searchDto.domain || [], {
            fields: searchDto.fields || [],
            offset: searchDto.offset || 0,
            limit: searchDto.limit || 100,
            order: searchDto.order || '',
        });
        return this.responseBuilder.success(result, `Search completed successfully. Found ${result.length} records.`, 200, undefined, {
            recordCount: result.length,
            model,
            searchCriteria: {
                domain: searchDto.domain,
                fields: searchDto.fields,
                offset: searchDto.offset,
                limit: searchDto.limit,
                order: searchDto.order
            }
        });
    }
    async getUserInstances() {
        const instances = await this.odooConnectionUseCase.getUserOdooInstances();
        return this.responseBuilder.success(instances, instances.length > 0
            ? 'User instances retrieved successfully'
            : 'No Odoo instances found. Connect to an instance first.', 200, undefined, {
            instanceCount: instances.length,
            activeInstances: instances.filter(i => i.isActive).length
        });
    }
    async getVersion() {
        return await this.odooConnectionUseCase.getVersionInfo();
    }
    async getCapabilities() {
        return await this.odooConnectionUseCase.getCapabilities();
    }
    async searchRead(model, searchDto) {
        const { domain, fields, limit, offset, order } = searchDto;
        return await this.odooConnectionUseCase.searchRead(model, domain, {
            fields,
            limit,
            offset,
            order,
        });
    }
    async create(model, createDto) {
        const recordId = await this.odooConnectionUseCase.create(model, createDto.values);
        return {
            success: true,
            id: recordId,
            message: 'Record created successfully',
            apiVersion: 'v1',
        };
    }
    async update(model, updateDto) {
        const success = await this.odooConnectionUseCase.update(model, updateDto.ids, updateDto.values);
        return {
            success,
            message: success ? 'Records updated successfully' : 'Update failed',
            apiVersion: 'v1',
        };
    }
    async delete(model, deleteDto) {
        const success = await this.odooConnectionUseCase.delete(model, deleteDto.ids);
        return {
            success,
            message: success ? 'Records deleted successfully' : 'Delete failed',
            apiVersion: 'v1',
        };
    }
    async execute(model, method, body) {
        const { args = [], kwargs = {} } = body;
        const result = await this.odooConnectionUseCase.execute(model, method, args, kwargs);
        return {
            success: true,
            result,
            message: 'Method executed successfully',
            apiVersion: 'v1',
        };
    }
    async disconnect() {
        await this.odooConnectionUseCase.disconnect();
        return {
            success: true,
            message: 'Disconnected from Odoo successfully',
            apiVersion: 'v1',
        };
    }
    async disconnectAll() {
        await this.odooConnectionUseCase.disconnectUser();
        return {
            success: true,
            message: 'All user sessions disconnected successfully',
            apiVersion: 'v1',
        };
    }
    async disconnectInstance(instanceId) {
        await this.odooConnectionUseCase.disconnectInstance(instanceId);
        return {
            success: true,
            message: 'Disconnected from instance successfully',
            apiVersion: 'v1',
        };
    }
    async cleanupPool() {
        try {
            const cleanedCount = await this.odooConnectionUseCase.cleanupStaleConnections();
            return {
                success: true,
                cleanedCount,
                message: `Cleaned up ${cleanedCount} stale connections`,
                apiVersion: 'v1',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to cleanup pool',
                error: error instanceof Error ? error.message : 'Unknown error',
                apiVersion: 'v1',
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async refreshPool() {
        try {
            const refreshedCount = await this.odooConnectionUseCase.refreshExpiringConnections();
            return {
                success: true,
                refreshedCount,
                message: `Refreshed ${refreshedCount} expiring connections`,
                apiVersion: 'v1',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to refresh pool',
                error: error instanceof Error ? error.message : 'Unknown error',
                apiVersion: 'v1',
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.OdooV1Controller = OdooV1Controller;
__decorate([
    (0, common_1.Post)('connect'),
    (0, jwt_auth_guard_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: '🔐 Connect to Odoo Instance',
        description: `
**Establishes a secure connection to an Odoo instance with intelligent auto-detection.**

### Features:
- 🤖 **Auto-detection**: Automatically detects Odoo version and selects optimal protocol
- 🔄 **Multi-protocol**: Supports XML-RPC, JSON-RPC, and REST API
- 🔐 **Secure**: Returns JWT token for subsequent authenticated requests
- 📊 **Comprehensive**: Provides detailed version and capability information

### Supported Odoo Versions:
- Odoo 13.0+ (Community & Enterprise)
- Odoo 15.0+ (Community & Enterprise)
- Odoo 17.0+ (Community & Enterprise)
- Odoo 18.0+ (Community & Enterprise)

### Usage:
1. Call this endpoint with your Odoo credentials
2. Save the returned JWT token
3. Use the token in the \`Authorization: Bearer <token>\` header for all subsequent API calls

### Security:
- Passwords are encrypted using AES-256-GCM before storage
- JWT tokens expire after 24 hours (configurable)
- Connection pooling ensures efficient resource usage
    `,
        tags: ['Authentication']
    }),
    (0, swagger_1.ApiConsumes)('application/json'),
    (0, swagger_1.ApiProduces)('application/json'),
    (0, swagger_1.ApiBody)({
        type: odoo_connection_dto_1.OdooConnectionDto,
        description: 'Odoo connection parameters',
        examples: {
            'Odoo 18 Enterprise': {
                summary: 'Connect to Odoo 18 Enterprise',
                description: 'Example connection to Odoo 18 Enterprise instance',
                value: {
                    host: 'https://odoo18.bestmix.one/',
                    database: 'bestmix_27_6',
                    username: 'tuan.le',
                    password: 'your_password_here',
                    protocol: 'https',
                    port: 443
                }
            },
            'Odoo 17 Community': {
                summary: 'Connect to Odoo 17 Community',
                description: 'Example connection to Odoo 17 Community instance',
                value: {
                    host: 'https://demo.odoo.com',
                    database: 'demo_db',
                    username: 'admin',
                    password: 'admin',
                    protocol: 'https',
                    port: 443
                }
            },
            'Local Development': {
                summary: 'Connect to Local Odoo',
                description: 'Example connection to local development instance',
                value: {
                    host: 'http://localhost:8069',
                    database: 'odoo_dev',
                    username: 'admin',
                    password: 'admin',
                    protocol: 'http',
                    port: 8069
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '✅ Successfully connected to Odoo',
        type: swagger_examples_dto_1.SwaggerConnectResponseExample,
        content: {
            'application/json': {
                examples: {
                    'Odoo 18 Enterprise': {
                        summary: 'Successful connection to Odoo 18 Enterprise',
                        value: {
                            success: true,
                            data: {
                                token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhbm9uX09qb3hPbU4xIiwib2Rvb0luc3RhbmNlSWQiOiJvZG9vX2FIUjBjSE02THk5diIsIm9kb29Ib3N0IjoiaHR0cHM6Ly9vZG9vMTguYmVzdG1peC5vbmUvIiwib2Rvb0RhdGFiYXNlIjoiYmVzdG1peF8yN182Iiwib2Rvb1VzZXJuYW1lIjoidHVhbi5sZSIsInNlc3Npb25JZCI6InNlc3Npb25fMTc1MzUxNDEwODk2MF81cWFnczI2cXkiLCJpYXQiOjE3NTM1MTQxMDgsImV4cCI6MTc1MzYwMDUwOCwiYXVkIjoib2Rvby1jbGllbnQiLCJpc3MiOiJ1bml2ZXJzYWwtb2Rvby1hZGFwdGVyIn0.U0-7un8Veo4f7Jxb1h-z6u1xUb2bfFg5h1FKu3JJx3Q',
                                expiresIn: '24h',
                                version: {
                                    major: 18,
                                    minor: 0,
                                    patch: 0,
                                    series: '18.0',
                                    edition: 'enterprise',
                                    serverVersion: '18.0+e',
                                    protocolVersion: 1
                                },
                                capabilities: {
                                    hasJsonRpc: true,
                                    hasRestApi: false,
                                    hasGraphQL: false,
                                    hasWebSocket: true,
                                    hasTokenAuth: true,
                                    hasOAuth2: true,
                                    maxBatchSize: 1000,
                                    supportedAuthMethods: ['password', 'api_key', 'oauth2', 'token']
                                }
                            },
                            message: 'Connected to Odoo successfully',
                            statusCode: 200,
                            apiVersion: 'v1',
                            timestamp: '2025-07-26T07:23:53.901Z',
                            path: '/api/v1/odoo/connect',
                            meta: {
                                connectionTime: 995,
                                odooVersion: '18.0',
                                protocol: 'JSON-RPC'
                            }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '❌ Invalid connection parameters',
        type: swagger_examples_dto_1.SwaggerErrorResponseExample,
        content: {
            'application/json': {
                examples: {
                    'Validation Error': {
                        summary: 'Invalid request parameters',
                        value: {
                            success: false,
                            error: 'Validation Error',
                            message: 'Invalid connection parameters provided',
                            statusCode: 400,
                            apiVersion: 'v1',
                            timestamp: '2025-07-26T07:23:53.901Z',
                            path: '/api/v1/odoo/connect',
                            errorCode: 'VALIDATION_FAILED',
                            validationErrors: [
                                {
                                    field: 'host',
                                    message: 'Host must be a valid URL',
                                    value: 'invalid-url',
                                    rule: 'isUrl'
                                }
                            ]
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '💥 Connection failed',
        type: swagger_examples_dto_1.SwaggerErrorResponseExample,
        content: {
            'application/json': {
                examples: {
                    'Connection Failed': {
                        summary: 'Failed to connect to Odoo',
                        value: {
                            success: false,
                            error: 'Internal Server Error',
                            message: 'Failed to connect to Odoo: Connection timeout',
                            statusCode: 500,
                            apiVersion: 'v1',
                            timestamp: '2025-07-26T07:23:53.901Z',
                            path: '/api/v1/odoo/connect',
                            errorCode: 'CONNECTION_FAILED',
                            debug: {
                                connectionTime: 30000,
                                odooHost: 'https://invalid-host.com',
                                odooDatabase: 'test_db'
                            }
                        }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [odoo_connection_dto_1.OdooConnectionDto, Object]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "connect", null);
__decorate([
    (0, common_1.Post)(':model/search'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '🔍 Search Records in Odoo Model',
        description: `
**Search and retrieve records from any Odoo model with advanced filtering.**

### Features:
- 🎯 **Flexible Filtering**: Use Odoo domain syntax for complex queries
- 📊 **Field Selection**: Choose specific fields to retrieve
- 📄 **Pagination**: Built-in offset and limit support
- 🔄 **Sorting**: Order results by any field
- ⚡ **Performance**: Optimized queries with connection pooling

### Domain Syntax Examples:
\`\`\`python
# Simple equality
[['name', '=', 'John Doe']]

# Multiple conditions (AND)
[['active', '=', True], ['email', '!=', False]]

# OR conditions
['|', ['name', 'ilike', 'john'], ['email', 'ilike', 'john']]

# Date ranges
[['create_date', '>=', '2024-01-01'], ['create_date', '<', '2024-12-31']]
\`\`\`
    `,
        tags: ['Data Operations']
    }),
    (0, swagger_1.ApiConsumes)('application/json'),
    (0, swagger_1.ApiProduces)('application/json'),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name (e.g., res.users, res.partner, product.product)',
        example: 'res.users'
    }),
    (0, swagger_1.ApiBody)({
        type: odoo_connection_dto_1.SearchReadDto,
        description: 'Search parameters with domain, fields, pagination, and sorting',
        examples: {
            'Search Users': {
                summary: 'Search active users',
                value: {
                    domain: [['active', '=', true]],
                    fields: ['name', 'login', 'email'],
                    offset: 0,
                    limit: 10,
                    order: 'name asc'
                }
            },
            'Search Companies': {
                summary: 'Search company partners',
                value: {
                    domain: [['is_company', '=', true]],
                    fields: ['name', 'email', 'phone', 'website'],
                    offset: 0,
                    limit: 20,
                    order: 'name asc'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '✅ Records retrieved successfully',
        type: swagger_examples_dto_1.SwaggerSearchResponseExample
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '🔒 Authentication required',
        type: swagger_examples_dto_1.SwaggerErrorResponseExample
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '❌ Model not found',
        type: swagger_examples_dto_1.SwaggerErrorResponseExample
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.SearchReadDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "searchRecords", null);
__decorate([
    (0, common_1.Get)('instances'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '📋 Get User Odoo Instances',
        description: `
**Retrieve all Odoo instances connected by the current user.**

### Features:
- 📊 **Instance Overview**: List all connected Odoo instances
- 🕒 **Usage Statistics**: Last used timestamps and usage counts
- 🔄 **Connection Status**: Active/inactive status for each instance
- 📈 **Performance Metrics**: Connection health and response times

### Use Cases:
- Dashboard displaying user's connected instances
- Instance management and monitoring
- Connection health checks
- Usage analytics and reporting
    `,
        tags: ['Connection Management']
    }),
    (0, swagger_1.ApiProduces)('application/json'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '✅ User instances retrieved successfully',
        content: {
            'application/json': {
                examples: {
                    'Multiple Instances': {
                        summary: 'User with multiple Odoo instances',
                        value: {
                            success: true,
                            data: [
                                {
                                    instanceId: 'odoo_aHR0cHM6Ly9v',
                                    host: 'https://odoo18.bestmix.one/',
                                    database: 'bestmix_27_6',
                                    username: 'tuan.le',
                                    isActive: true,
                                    lastUsed: '2025-07-26T07:35:11.872Z'
                                },
                                {
                                    instanceId: 'odoo_bG9jYWxob3N0',
                                    host: 'http://localhost:8069',
                                    database: 'odoo_dev',
                                    username: 'admin',
                                    isActive: true,
                                    lastUsed: '2025-07-25T15:20:30.123Z'
                                }
                            ],
                            message: 'User instances retrieved successfully',
                            statusCode: 200,
                            apiVersion: 'v1',
                            timestamp: '2025-07-26T07:35:11.872Z',
                            path: '/api/v1/odoo/instances',
                            meta: { instanceCount: 2, activeInstances: 2 }
                        }
                    },
                    'No Instances': {
                        summary: 'User with no connected instances',
                        value: {
                            success: true,
                            data: [],
                            message: 'No Odoo instances found. Connect to an instance first.',
                            statusCode: 200,
                            apiVersion: 'v1',
                            timestamp: '2025-07-26T07:35:11.872Z',
                            path: '/api/v1/odoo/instances',
                            meta: { instanceCount: 0, activeInstances: 0 }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: '🔒 Authentication required',
        type: swagger_examples_dto_1.SwaggerErrorResponseExample
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getUserInstances", null);
__decorate([
    (0, common_1.Get)('version'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: '📋 Get Odoo Version Information',
        description: `
**Returns detailed version information of the connected Odoo instance.**

### Features:
- 🏷️ **Version Details**: Major, minor, patch versions
- 🏢 **Edition Info**: Community vs Enterprise edition
- 🔌 **Protocol Info**: Supported protocols and capabilities
- 🚀 **Performance**: Server response times and health

### Use Cases:
- Version compatibility checks
- Feature availability detection
- Protocol selection optimization
- System monitoring and diagnostics
    `,
        tags: ['Connection Management']
    }),
    (0, swagger_1.ApiProduces)('application/json'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Version information retrieved',
        schema: {
            type: 'object',
            properties: {
                major: { type: 'number', example: 18 },
                minor: { type: 'number', example: 0 },
                patch: { type: 'number', example: 0 },
                series: { type: 'string', example: '18.0' },
                edition: { type: 'string', example: 'enterprise' },
                serverVersion: { type: 'string', example: '********.3' },
                protocolVersion: { type: 'number', example: 1 },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getVersion", null);
__decorate([
    (0, common_1.Get)('capabilities'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Odoo capabilities',
        description: 'Returns the capabilities and features available in the connected Odoo instance',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Capabilities retrieved',
        schema: {
            type: 'object',
            properties: {
                hasJsonRpc: { type: 'boolean', example: true },
                hasRestApi: { type: 'boolean', example: true },
                hasGraphQL: { type: 'boolean', example: false },
                hasWebSocket: { type: 'boolean', example: true },
                hasTokenAuth: { type: 'boolean', example: true },
                hasOAuth2: { type: 'boolean', example: true },
                maxBatchSize: { type: 'number', example: 1000 },
                supportedAuthMethods: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['password', 'api_key', 'oauth2', 'token'],
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getCapabilities", null);
__decorate([
    (0, common_1.Post)(':model/search'),
    (0, swagger_1.ApiOperation)({
        summary: 'Search and read records from Odoo model',
        description: 'Performs search_read operation on the specified Odoo model with domain filtering and field selection',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name (e.g., res.partner, sale.order, product.product)',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records retrieved successfully',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    name: { type: 'string', example: 'Partner Name' },
                    email: { type: 'string', example: '<EMAIL>' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.SearchReadDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "searchRead", null);
__decorate([
    (0, common_1.Post)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new record in Odoo model',
        description: 'Creates a new record in the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Record created successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                id: { type: 'number', example: 123 },
                message: { type: 'string', example: 'Record created successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.CreateRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "create", null);
__decorate([
    (0, common_1.Put)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update records in Odoo model',
        description: 'Updates existing records in the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records updated successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Records updated successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.UpdateRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete records from Odoo model',
        description: 'Deletes records from the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records deleted successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Records deleted successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.DeleteRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "delete", null);
__decorate([
    (0, common_1.Post)(':model/execute/:method'),
    (0, swagger_1.ApiOperation)({
        summary: 'Execute custom method on Odoo model',
        description: 'Executes a custom method on the specified Odoo model with arguments and keyword arguments',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiParam)({
        name: 'method',
        description: 'Method name to execute',
        example: 'search_count',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Method executed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                result: { description: 'Method execution result' },
                message: { type: 'string', example: 'Method executed successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Param)('method')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "execute", null);
__decorate([
    (0, common_1.Post)('disconnect'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Disconnect from Odoo instance',
        description: 'Closes the current connection to the Odoo instance for this user session',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Disconnected successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Disconnected from Odoo successfully',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "disconnect", null);
__decorate([
    (0, common_1.Post)('disconnect-all'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Disconnect all user sessions',
        description: 'Disconnect all Odoo connections for the current user across all instances',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'All user sessions disconnected successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'All user sessions disconnected successfully',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "disconnectAll", null);
__decorate([
    (0, common_1.Post)('instances/:instanceId/disconnect'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Disconnect from specific Odoo instance',
        description: 'Disconnect from a specific Odoo instance',
    }),
    (0, swagger_1.ApiParam)({
        name: 'instanceId',
        description: 'Odoo instance ID',
        example: 'odoo_abc123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Disconnected from instance successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Disconnected from instance successfully',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('instanceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "disconnectInstance", null);
__decorate([
    (0, common_1.Post)('pool/cleanup'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Cleanup stale connections',
        description: 'Remove connections that have been idle for too long',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Stale connections cleaned up',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                cleanedCount: { type: 'number', example: 3 },
                message: { type: 'string', example: 'Cleaned up 3 stale connections' },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "cleanupPool", null);
__decorate([
    (0, common_1.Post)('pool/refresh'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Refresh expiring connections',
        description: 'Proactively refresh connections that are about to expire',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Expiring connections refreshed',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                refreshedCount: { type: 'number', example: 2 },
                message: {
                    type: 'string',
                    example: 'Refreshed 2 expiring connections',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "refreshPool", null);
exports.OdooV1Controller = OdooV1Controller = __decorate([
    (0, swagger_1.ApiTags)('Authentication', 'Data Operations', 'Advanced Operations', 'Connection Management'),
    (0, common_1.Controller)({ path: 'odoo', version: '1' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiExtraModels)(swagger_examples_dto_1.SwaggerSuccessResponseExample, swagger_examples_dto_1.SwaggerErrorResponseExample, swagger_examples_dto_1.SwaggerConnectResponseExample, swagger_examples_dto_1.SwaggerSearchResponseExample, odoo_connection_dto_1.OdooConnectionDto, odoo_connection_dto_1.SearchReadDto, odoo_connection_dto_1.CreateRecordDto, odoo_connection_dto_1.UpdateRecordDto, odoo_connection_dto_1.DeleteRecordDto),
    __metadata("design:paramtypes", [odoo_connection_use_case_1.OdooConnectionUseCase,
        jwt_service_1.JwtAuthService,
        response_builder_service_1.ResponseBuilderService])
], OdooV1Controller);
//# sourceMappingURL=odoo-v1.controller.js.map