"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooV1Controller = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const odoo_connection_use_case_1 = require("../../../application/use-cases/odoo-connection.use-case");
const odoo_connection_dto_1 = require("../../../application/dtos/odoo-connection.dto");
const jwt_service_1 = require("../../../infrastructure/auth/jwt.service");
const jwt_auth_guard_1 = require("../../../infrastructure/auth/jwt-auth.guard");
let OdooV1Controller = class OdooV1Controller {
    odooConnectionUseCase;
    jwtAuthService;
    constructor(odooConnectionUseCase, jwtAuthService) {
        this.odooConnectionUseCase = odooConnectionUseCase;
        this.jwtAuthService = jwtAuthService;
    }
    createErrorResponse(error, message, statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR) {
        const errorResponse = {
            success: false,
            error: error.message || 'Unknown error',
            message: message || error.message || 'An error occurred',
            statusCode,
            timestamp: new Date().toISOString(),
            apiVersion: 'v1',
        };
        if (process.env.NODE_ENV === 'development') {
            errorResponse.stack = error.stack;
            errorResponse.errorName = error.name;
            errorResponse.errorType = error.constructor.name;
        }
        return errorResponse;
    }
    async connect(connectionDto, request) {
        try {
            await this.odooConnectionUseCase.connect(connectionDto);
            const version = await this.odooConnectionUseCase.getVersionInfo();
            const capabilities = await this.odooConnectionUseCase.getCapabilities();
            const ip = request.ip || 'unknown';
            const userAgent = request.headers?.['user-agent'] || 'unknown';
            const hash = Buffer.from(`${ip}:${userAgent}`).toString('base64').substring(0, 8);
            const webUserId = `anon_${hash}`;
            const instanceHash = Buffer.from(`${connectionDto.host}:${connectionDto.database}:${connectionDto.username}`)
                .toString('base64')
                .replace(/[^a-zA-Z0-9]/g, '')
                .substring(0, 12);
            const odooInstanceId = `odoo_${instanceHash}`;
            const tokenData = await this.jwtAuthService.generateToken({
                sub: webUserId,
                odooInstanceId: odooInstanceId,
                odooHost: connectionDto.host,
                odooDatabase: connectionDto.database,
                odooUsername: connectionDto.username,
                sessionId: this.jwtAuthService.generateSessionId(),
            });
            return {
                success: true,
                message: 'Connected to Odoo successfully',
                token: tokenData.token,
                expiresIn: tokenData.expiresIn,
                version,
                capabilities,
                apiVersion: 'v1',
            };
        }
        catch (error) {
            throw new common_1.HttpException(this.createErrorResponse(error, 'Failed to connect to Odoo'), common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getVersion() {
        return await this.odooConnectionUseCase.getVersionInfo();
    }
    async getCapabilities() {
        return await this.odooConnectionUseCase.getCapabilities();
    }
    async searchRead(model, searchDto) {
        const { domain, fields, limit, offset, order } = searchDto;
        return await this.odooConnectionUseCase.searchRead(model, domain, {
            fields,
            limit,
            offset,
            order,
        });
    }
    async create(model, createDto) {
        const recordId = await this.odooConnectionUseCase.create(model, createDto.values);
        return {
            success: true,
            id: recordId,
            message: 'Record created successfully',
            apiVersion: 'v1',
        };
    }
    async update(model, updateDto) {
        const success = await this.odooConnectionUseCase.update(model, updateDto.ids, updateDto.values);
        return {
            success,
            message: success ? 'Records updated successfully' : 'Update failed',
            apiVersion: 'v1',
        };
    }
    async delete(model, deleteDto) {
        const success = await this.odooConnectionUseCase.delete(model, deleteDto.ids);
        return {
            success,
            message: success ? 'Records deleted successfully' : 'Delete failed',
            apiVersion: 'v1',
        };
    }
    async execute(model, method, body) {
        const { args = [], kwargs = {} } = body;
        const result = await this.odooConnectionUseCase.execute(model, method, args, kwargs);
        return {
            success: true,
            result,
            message: 'Method executed successfully',
            apiVersion: 'v1',
        };
    }
    async disconnect() {
        await this.odooConnectionUseCase.disconnect();
        return {
            success: true,
            message: 'Disconnected from Odoo successfully',
            apiVersion: 'v1',
        };
    }
    async disconnectAll() {
        await this.odooConnectionUseCase.disconnectUser();
        return {
            success: true,
            message: 'All user sessions disconnected successfully',
            apiVersion: 'v1',
        };
    }
    async getUserInstances() {
        const instances = await this.odooConnectionUseCase.getUserOdooInstances();
        return {
            success: true,
            instances,
            apiVersion: 'v1',
        };
    }
    async disconnectInstance(instanceId) {
        await this.odooConnectionUseCase.disconnectInstance(instanceId);
        return {
            success: true,
            message: 'Disconnected from instance successfully',
            apiVersion: 'v1',
        };
    }
    async cleanupPool() {
        try {
            const cleanedCount = await this.odooConnectionUseCase.cleanupStaleConnections();
            return {
                success: true,
                cleanedCount,
                message: `Cleaned up ${cleanedCount} stale connections`,
                apiVersion: 'v1',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to cleanup pool',
                error: error instanceof Error ? error.message : 'Unknown error',
                apiVersion: 'v1',
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async refreshPool() {
        try {
            const refreshedCount = await this.odooConnectionUseCase.refreshExpiringConnections();
            return {
                success: true,
                refreshedCount,
                message: `Refreshed ${refreshedCount} expiring connections`,
                apiVersion: 'v1',
            };
        }
        catch (error) {
            throw new common_1.HttpException({
                success: false,
                message: 'Failed to refresh pool',
                error: error instanceof Error ? error.message : 'Unknown error',
                apiVersion: 'v1',
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.OdooV1Controller = OdooV1Controller;
__decorate([
    (0, common_1.Post)('connect'),
    (0, jwt_auth_guard_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Connect to Odoo instance',
        description: 'Establishes connection to an Odoo instance with automatic version detection and protocol selection. Returns JWT token for subsequent API calls.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Successfully connected to Odoo',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Connected to Odoo successfully' },
                token: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
                expiresIn: { type: 'string', example: '24h' },
                version: {
                    type: 'object',
                    properties: {
                        major: { type: 'number', example: 18 },
                        minor: { type: 'number', example: 0 },
                        series: { type: 'string', example: '18.0' },
                        edition: { type: 'string', example: 'enterprise' },
                    },
                },
                capabilities: {
                    type: 'object',
                    properties: {
                        hasJsonRpc: { type: 'boolean', example: true },
                        hasRestApi: { type: 'boolean', example: true },
                        maxBatchSize: { type: 'number', example: 1000 },
                        supportedAuthMethods: {
                            type: 'array',
                            items: { type: 'string' },
                            example: ['password', 'api_key', 'oauth2'],
                        },
                    },
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid connection parameters' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Connection failed' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [odoo_connection_dto_1.OdooConnectionDto, Object]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "connect", null);
__decorate([
    (0, common_1.Get)('version'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Odoo version information',
        description: 'Returns detailed version information of the connected Odoo instance',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Version information retrieved',
        schema: {
            type: 'object',
            properties: {
                major: { type: 'number', example: 18 },
                minor: { type: 'number', example: 0 },
                patch: { type: 'number', example: 0 },
                series: { type: 'string', example: '18.0' },
                edition: { type: 'string', example: 'enterprise' },
                serverVersion: { type: 'string', example: '********.3' },
                protocolVersion: { type: 'number', example: 1 },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getVersion", null);
__decorate([
    (0, common_1.Get)('capabilities'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Odoo capabilities',
        description: 'Returns the capabilities and features available in the connected Odoo instance',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Capabilities retrieved',
        schema: {
            type: 'object',
            properties: {
                hasJsonRpc: { type: 'boolean', example: true },
                hasRestApi: { type: 'boolean', example: true },
                hasGraphQL: { type: 'boolean', example: false },
                hasWebSocket: { type: 'boolean', example: true },
                hasTokenAuth: { type: 'boolean', example: true },
                hasOAuth2: { type: 'boolean', example: true },
                maxBatchSize: { type: 'number', example: 1000 },
                supportedAuthMethods: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['password', 'api_key', 'oauth2', 'token'],
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getCapabilities", null);
__decorate([
    (0, common_1.Post)(':model/search'),
    (0, swagger_1.ApiOperation)({
        summary: 'Search and read records from Odoo model',
        description: 'Performs search_read operation on the specified Odoo model with domain filtering and field selection',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name (e.g., res.partner, sale.order, product.product)',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records retrieved successfully',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'number', example: 1 },
                    name: { type: 'string', example: 'Partner Name' },
                    email: { type: 'string', example: '<EMAIL>' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.SearchReadDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "searchRead", null);
__decorate([
    (0, common_1.Post)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new record in Odoo model',
        description: 'Creates a new record in the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Record created successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                id: { type: 'number', example: 123 },
                message: { type: 'string', example: 'Record created successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.CreateRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "create", null);
__decorate([
    (0, common_1.Put)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update records in Odoo model',
        description: 'Updates existing records in the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records updated successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Records updated successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.UpdateRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':model'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete records from Odoo model',
        description: 'Deletes records from the specified Odoo model',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Records deleted successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Records deleted successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, odoo_connection_dto_1.DeleteRecordDto]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "delete", null);
__decorate([
    (0, common_1.Post)(':model/execute/:method'),
    (0, swagger_1.ApiOperation)({
        summary: 'Execute custom method on Odoo model',
        description: 'Executes a custom method on the specified Odoo model with arguments and keyword arguments',
    }),
    (0, swagger_1.ApiParam)({
        name: 'model',
        description: 'Odoo model name',
        example: 'res.partner',
    }),
    (0, swagger_1.ApiParam)({
        name: 'method',
        description: 'Method name to execute',
        example: 'search_count',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Method executed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                result: { description: 'Method execution result' },
                message: { type: 'string', example: 'Method executed successfully' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('model')),
    __param(1, (0, common_1.Param)('method')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "execute", null);
__decorate([
    (0, common_1.Post)('disconnect'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Disconnect from Odoo instance',
        description: 'Closes the current connection to the Odoo instance for this user session',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Disconnected successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Disconnected from Odoo successfully',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "disconnect", null);
__decorate([
    (0, common_1.Post)('disconnect-all'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Disconnect all user sessions',
        description: 'Disconnect all Odoo connections for the current user across all instances',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'All user sessions disconnected successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'All user sessions disconnected successfully',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "disconnectAll", null);
__decorate([
    (0, common_1.Get)('instances'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get user Odoo instances',
        description: 'List all Odoo instances connected by the current user',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User Odoo instances retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                instances: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            instanceId: { type: 'string', example: 'odoo_abc123' },
                            host: { type: 'string', example: 'https://odoo18.bestmix.one/' },
                            database: { type: 'string', example: 'bestmix_27_6' },
                            username: { type: 'string', example: 'tuan.le' },
                            isActive: { type: 'boolean', example: true },
                            lastUsed: { type: 'string', example: '2025-07-26T06:05:11.000Z' },
                        },
                    },
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "getUserInstances", null);
__decorate([
    (0, common_1.Post)('instances/:instanceId/disconnect'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Disconnect from specific Odoo instance',
        description: 'Disconnect from a specific Odoo instance',
    }),
    (0, swagger_1.ApiParam)({
        name: 'instanceId',
        description: 'Odoo instance ID',
        example: 'odoo_abc123',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Disconnected from instance successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: {
                    type: 'string',
                    example: 'Disconnected from instance successfully',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('instanceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "disconnectInstance", null);
__decorate([
    (0, common_1.Post)('pool/cleanup'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Cleanup stale connections',
        description: 'Remove connections that have been idle for too long',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Stale connections cleaned up',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                cleanedCount: { type: 'number', example: 3 },
                message: { type: 'string', example: 'Cleaned up 3 stale connections' },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "cleanupPool", null);
__decorate([
    (0, common_1.Post)('pool/refresh'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Refresh expiring connections',
        description: 'Proactively refresh connections that are about to expire',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Expiring connections refreshed',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                refreshedCount: { type: 'number', example: 2 },
                message: {
                    type: 'string',
                    example: 'Refreshed 2 expiring connections',
                },
                apiVersion: { type: 'string', example: 'v1' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OdooV1Controller.prototype, "refreshPool", null);
exports.OdooV1Controller = OdooV1Controller = __decorate([
    (0, swagger_1.ApiTags)('Odoo Integration v1'),
    (0, common_1.Controller)({ path: 'odoo', version: '1' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [odoo_connection_use_case_1.OdooConnectionUseCase,
        jwt_service_1.JwtAuthService])
], OdooV1Controller);
//# sourceMappingURL=odoo-v1.controller.js.map