import { Request } from 'express';
import { OdooConnectionUseCase } from '../../../application/use-cases/odoo-connection.use-case';
import { OdooConnectionDto, SearchReadDto, CreateRecordDto, UpdateRecordDto, DeleteRecordDto } from '../../../application/dtos/odoo-connection.dto';
import { JwtAuthService } from '../../../infrastructure/auth/jwt.service';
export declare class OdooV1Controller {
    private readonly odooConnectionUseCase;
    private readonly jwtAuthService;
    constructor(odooConnectionUseCase: OdooConnectionUseCase, jwtAuthService: JwtAuthService);
    private createErrorResponse;
    connect(connectionDto: OdooConnectionDto, request: Request): Promise<{
        success: boolean;
        message: string;
        token: string;
        expiresIn: string;
        version: import("../../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null;
        capabilities: import("../../../domain/value-objects/odoo-connection-config").OdooCapabilities | null;
        apiVersion: string;
    }>;
    getVersion(): Promise<import("../../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null>;
    getCapabilities(): Promise<import("../../../domain/value-objects/odoo-connection-config").OdooCapabilities | null>;
    searchRead(model: string, searchDto: SearchReadDto): Promise<any[]>;
    create(model: string, createDto: CreateRecordDto): Promise<{
        success: boolean;
        id: number;
        message: string;
        apiVersion: string;
    }>;
    update(model: string, updateDto: UpdateRecordDto): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    delete(model: string, deleteDto: DeleteRecordDto): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    execute(model: string, method: string, body: {
        args?: any[];
        kwargs?: any;
    }): Promise<{
        success: boolean;
        result: any;
        message: string;
        apiVersion: string;
    }>;
    disconnect(): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    disconnectAll(): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    getUserInstances(): Promise<{
        success: boolean;
        instances: {
            instanceId: string;
            host: string;
            database: string;
            username: string;
            isActive: boolean;
            lastUsed: Date;
        }[];
        apiVersion: string;
    }>;
    disconnectInstance(instanceId: string): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    cleanupPool(): Promise<{
        success: boolean;
        cleanedCount: number;
        message: string;
        apiVersion: string;
    }>;
    refreshPool(): Promise<{
        success: boolean;
        refreshedCount: number;
        message: string;
        apiVersion: string;
    }>;
}
