import { Request } from 'express';
import { OdooConnectionUseCase } from '../../../shared/application/use-cases/odoo-connection.use-case';
import { OdooConnectionDto, SearchReadDto, CreateRecordDto, UpdateRecordDto, DeleteRecordDto } from '../../../shared/application/dtos/odoo-connection.dto';
import { JwtAuthService } from '../../../infrastructure/auth/jwt.service';
import { ResponseBuilderService } from '../../../common/services/response-builder.service';
export declare class OdooV1Controller {
    private readonly odooConnectionUseCase;
    private readonly jwtAuthService;
    private readonly responseBuilder;
    constructor(odooConnectionUseCase: OdooConnectionUseCase, jwtAuthService: JwtAuthService, responseBuilder: ResponseBuilderService);
    connect(connectionDto: OdooConnectionDto, request: Request): Promise<import("../../../common/interfaces/api-response.interface").SuccessResponse<{
        token: string;
        expiresIn: string;
        version: import("../../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null;
        capabilities: import("../../../domain/value-objects/odoo-connection-config").OdooCapabilities | null;
    }>>;
    searchRecords(model: string, searchDto: SearchReadDto): Promise<import("../../../common/interfaces/api-response.interface").SuccessResponse<any[]>>;
    getUserInstances(): Promise<import("../../../common/interfaces/api-response.interface").SuccessResponse<{
        instanceId: string;
        host: string;
        database: string;
        username: string;
        isActive: boolean;
        lastUsed: Date;
    }[]>>;
    getVersion(): Promise<import("../../../domain/value-objects/odoo-connection-config").OdooVersionInfo | null>;
    getCapabilities(): Promise<import("../../../domain/value-objects/odoo-connection-config").OdooCapabilities | null>;
    searchRead(model: string, searchDto: SearchReadDto): Promise<any[]>;
    create(model: string, createDto: CreateRecordDto): Promise<{
        success: boolean;
        id: number;
        message: string;
        apiVersion: string;
    }>;
    update(model: string, updateDto: UpdateRecordDto): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    delete(model: string, deleteDto: DeleteRecordDto): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    execute(model: string, method: string, body: {
        args?: any[];
        kwargs?: any;
    }): Promise<{
        success: boolean;
        result: any;
        message: string;
        apiVersion: string;
    }>;
    disconnect(): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    disconnectAll(): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    disconnectInstance(instanceId: string): Promise<{
        success: boolean;
        message: string;
        apiVersion: string;
    }>;
    cleanupPool(): Promise<{
        success: boolean;
        cleanedCount: number;
        message: string;
        apiVersion: string;
    }>;
    refreshPool(): Promise<{
        success: boolean;
        refreshedCount: number;
        message: string;
        apiVersion: string;
    }>;
}
