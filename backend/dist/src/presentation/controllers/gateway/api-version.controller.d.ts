import { PerformanceMonitorService } from '../../../infrastructure/adapters/odoo/performance-monitor.service';
import { PoolMetrics, HealthCheckResult } from '../../../infrastructure/adapters/odoo/odoo-connection-pool.service';
import { OdooConnectionUseCase } from '../../../shared/application/use-cases/odoo-connection.use-case';
export declare class ApiVersionController {
    private readonly odooConnectionUseCase;
    private readonly performanceMonitor;
    constructor(odooConnectionUseCase: OdooConnectionUseCase, performanceMonitor: PerformanceMonitorService);
    getApiInfo(): {
        name: string;
        description: string;
        currentVersion: string;
        availableVersions: string[];
        endpoints: {
            v1: string;
        };
        documentation: {
            swagger: string;
            v1: string;
        };
        features: {
            v1: string[];
        };
        compatibility: {
            odooVersions: string[];
            protocols: string[];
            authMethods: string[];
        };
    };
    getHealth(): {
        status: string;
        timestamp: string;
        version: string;
        uptime: number;
    };
    getPoolStats(): {
        size: number;
        maxSize: number;
        connections: {
            key: string;
            lastUsed: Date;
            host: string;
            database: string;
        }[];
    };
    getPoolMetrics(): PoolMetrics;
    getPoolHealth(): Promise<{
        totalConnections: number;
        healthyConnections: number;
        unhealthyConnections: number;
        details: HealthCheckResult[];
        poolMetrics: PoolMetrics;
    }>;
    getPerformanceStats(timeWindow?: string): import("../../../infrastructure/adapters/odoo/performance-monitor.service").PerformanceStats;
    getRecentErrors(count?: string): import("../../../infrastructure/adapters/odoo/performance-monitor.service").PerformanceMetric[];
}
