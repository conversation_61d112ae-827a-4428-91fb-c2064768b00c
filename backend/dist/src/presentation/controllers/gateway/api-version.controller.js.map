{"version": 3, "file": "api-version.controller.js", "sourceRoot": "", "sources": ["../../../../../src/presentation/controllers/gateway/api-version.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwD;AACxD,6CAA+E;AAC/E,mHAA8G;AAE9G,6GAAuG;AAIhG,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAEZ;IACA;IAFnB,YACmB,qBAA4C,EAC5C,kBAA6C;QAD7C,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,uBAAkB,GAAlB,kBAAkB,CAA2B;IAC7D,CAAC;IAiCJ,UAAU;QACR,OAAO;YACL,IAAI,EAAE,4BAA4B;YAClC,WAAW,EACT,gGAAgG;YAClG,cAAc,EAAE,IAAI;YACpB,iBAAiB,EAAE,CAAC,IAAI,CAAC;YACzB,SAAS,EAAE;gBACT,EAAE,EAAE,SAAS;aACd;YACD,aAAa,EAAE;gBACb,OAAO,EAAE,WAAW;gBACpB,EAAE,EAAE,cAAc;aACnB;YACD,QAAQ,EAAE;gBACR,EAAE,EAAE;oBACF,8CAA8C;oBAC9C,kDAAkD;oBAClD,6BAA6B;oBAC7B,gCAAgC;oBAChC,gCAAgC;oBAChC,iBAAiB;oBACjB,yBAAyB;iBAC1B;aACF;YACD,aAAa,EAAE;gBACb,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;gBAC/C,SAAS,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;gBAC9C,WAAW,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;aACxD;SACF,CAAC;IACJ,CAAC;IAKD,SAAS;QACP,OAAO;YACL,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;SACzB,CAAC;IACJ,CAAC;IA2BD,YAAY;QACV,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,CAAC;IACnD,CAAC;IAmBD,cAAc;QACZ,OAAO,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC;IACrD,CAAC;IA2BK,AAAN,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;IACxD,CAAC;IAwCD,mBAAmB,CAAsB,UAAmB;QAC1D,MAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACvE,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IA2BD,eAAe,CAAiB,KAAc;QAC5C,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpD,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;CACF,CAAA;AAzOY,oDAAoB;AAqC/B;IAhCC,IAAA,YAAG,GAAE;IACL,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4CAA4C,EAAE,CAAC;IACvE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,wCAAwC;QACrD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,4BAA4B,EAAE;gBAC/D,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC/B,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;gBACjD,iBAAiB,EAAE;oBACjB,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,OAAO,EAAE,CAAC,IAAI,CAAC;iBAChB;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE;qBAC3C;iBACF;gBACD,aAAa,EAAE;oBACb,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE;wBACjD,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE;qBAChD;iBACF;aACF;SACF;KACF,CAAC;;;;sDAgCD;AAKD;IAHC,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;IAClD,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;;;;qDAQ3D;AA2BD;IAzBC,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAChE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,4BAA4B;QACzC,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;gBACpC,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;gBACzC,WAAW,EAAE;oBACX,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,4CAA4C,EAAE;4BAC9E,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,0BAA0B,EAAE;4BACjE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,kBAAkB,EAAE;4BACrD,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE;yBACvD;qBACF;iBACF;aACF;SACF;KACF,CAAC;;;;wDAGD;AAmBD;IAjBC,IAAA,YAAG,EAAC,cAAc,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;IACjE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,sCAAsC;QACnD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;gBACpC,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;gBACzC,kBAAkB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;gBACpD,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;gBACpD,uBAAuB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;gBAC1D,uBAAuB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;aAC1D;SACF;KACF,CAAC;;;;0DAGD;AA2BK;IAzBL,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;IACrE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,kCAAkC;QAC/C,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,gBAAgB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;gBAChD,kBAAkB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;gBAClD,oBAAoB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;gBACpD,OAAO,EAAE;oBACP,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACvB,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC5B,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBAC7B;qBACF;iBACF;aACF;SACF;KACF,CAAC;;;;yDAGD;AAwCD;IAtCC,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gDAAgD,EAAE,CAAC;IAC3E,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,KAAK;QACf,WAAW,EAAE,2DAA2D;QACxE,OAAO,EAAE,OAAO;KACjB,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,wBAAwB;QACrC,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;gBACjD,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;gBACnD,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;gBAC9C,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;gBAC1C,gBAAgB,EAAE;oBAChB,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;wBACpD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;wBAChD,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;wBAC3C,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,0BAA0B,EAAE;qBACnE;iBACF;gBACD,eAAe,EAAE;oBACf,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE;wBACP,uBAAuB,EAAE,EAAE;wBAC3B,wBAAwB,EAAE,EAAE;wBAC5B,mBAAmB,EAAE,EAAE;qBACxB;iBACF;aACF;SACF;KACF,CAAC;IACmB,WAAA,IAAA,cAAK,EAAC,YAAY,CAAC,CAAA;;;;+DAGvC;AA2BD;IAzBC,IAAA,YAAG,EAAC,oBAAoB,CAAC;IACzB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACrD,IAAA,kBAAQ,EAAC;QACR,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,KAAK;QACf,WAAW,EAAE,mCAAmC;QAChD,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,sBAAsB;QACnC,MAAM,EAAE;YACN,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;oBACpD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;oBAChD,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE;oBAC3C,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,0BAA0B,EAAE;oBAClE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,oBAAoB,EAAE;iBACzD;aACF;SACF;KACF,CAAC;IACe,WAAA,IAAA,cAAK,EAAC,OAAO,CAAC,CAAA;;;;2DAG9B;+BAxOU,oBAAoB;IAFhC,IAAA,iBAAO,EAAC,iBAAiB,CAAC;IAC1B,IAAA,mBAAU,EAAC,MAAM,CAAC;qCAGyB,gDAAqB;QACxB,uDAAyB;GAHrD,oBAAoB,CAyOhC"}