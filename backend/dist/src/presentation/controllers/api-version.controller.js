"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiVersionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const performance_monitor_service_1 = require("../../infrastructure/adapters/odoo/performance-monitor.service");
const odoo_connection_use_case_1 = require("../../application/use-cases/odoo-connection.use-case");
let ApiVersionController = class ApiVersionController {
    odooConnectionUseCase;
    performanceMonitor;
    constructor(odooConnectionUseCase, performanceMonitor) {
        this.odooConnectionUseCase = odooConnectionUseCase;
        this.performanceMonitor = performanceMonitor;
    }
    getApiInfo() {
        return {
            name: 'Universal Odoo Adapter API',
            description: 'A comprehensive API for connecting to multiple Odoo versions with automatic protocol selection',
            currentVersion: 'v1',
            availableVersions: ['v1'],
            endpoints: {
                v1: '/api/v1',
            },
            documentation: {
                swagger: '/api/docs',
                v1: '/api/v1/docs',
            },
            features: {
                v1: [
                    'Multi-version Odoo support (13, 15, 17, 18+)',
                    'Multi-protocol support (XML-RPC, JSON-RPC, REST)',
                    'Automatic version detection',
                    'Intelligent protocol selection',
                    'Field mapping between versions',
                    'CRUD operations',
                    'Custom method execution',
                ],
            },
            compatibility: {
                odooVersions: ['13.0', '15.0', '17.0', '18.0+'],
                protocols: ['XML-RPC', 'JSON-RPC', 'REST API'],
                authMethods: ['Password', 'API Key', 'OAuth2', 'Token'],
            },
        };
    }
    getHealth() {
        return {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: 'v1',
            uptime: process.uptime(),
        };
    }
    getPoolStats() {
        return this.odooConnectionUseCase.getPoolStats();
    }
    getPoolMetrics() {
        return this.odooConnectionUseCase.getPoolMetrics();
    }
    async getPoolHealth() {
        return await this.odooConnectionUseCase.healthCheck();
    }
    getPerformanceStats(timeWindow) {
        const timeWindowMs = timeWindow ? parseInt(timeWindow, 10) : undefined;
        return this.performanceMonitor.getStats(timeWindowMs);
    }
    getRecentErrors(count) {
        const errorCount = count ? parseInt(count, 10) : 10;
        return this.performanceMonitor.getRecentErrors(errorCount);
    }
};
exports.ApiVersionController = ApiVersionController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get API information and available versions' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'API information retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', example: 'Universal Odoo Adapter API' },
                description: { type: 'string' },
                currentVersion: { type: 'string', example: 'v1' },
                availableVersions: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['v1'],
                },
                endpoints: {
                    type: 'object',
                    properties: {
                        v1: { type: 'string', example: '/api/v1' },
                    },
                },
                documentation: {
                    type: 'object',
                    properties: {
                        swagger: { type: 'string', example: '/api/docs' },
                        v1: { type: 'string', example: '/api/v1/docs' },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getApiInfo", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Health check endpoint' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'API is healthy' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getHealth", null);
__decorate([
    (0, common_1.Get)('pool-stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Odoo connection pool statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Connection pool statistics',
        schema: {
            type: 'object',
            properties: {
                size: { type: 'number', example: 5 },
                maxSize: { type: 'number', example: 100 },
                connections: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            key: { type: 'string', example: 'user123:odoo.example.com:database:username' },
                            lastUsed: { type: 'string', example: '2025-01-26T10:00:00.000Z' },
                            host: { type: 'string', example: 'odoo.example.com' },
                            database: { type: 'string', example: 'production_db' },
                        },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getPoolStats", null);
__decorate([
    (0, common_1.Get)('pool-metrics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get detailed connection pool metrics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Detailed pool metrics for monitoring',
        schema: {
            type: 'object',
            properties: {
                size: { type: 'number', example: 5 },
                maxSize: { type: 'number', example: 100 },
                utilizationPercent: { type: 'number', example: 5.0 },
                averageAgeMinutes: { type: 'number', example: 15.5 },
                oldestConnectionMinutes: { type: 'number', example: 25.0 },
                newestConnectionMinutes: { type: 'number', example: 2.0 },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getPoolMetrics", null);
__decorate([
    (0, common_1.Get)('pool-health'),
    (0, swagger_1.ApiOperation)({ summary: 'Health check for all connections in pool' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Health status of all connections',
        schema: {
            type: 'object',
            properties: {
                totalConnections: { type: 'number', example: 5 },
                healthyConnections: { type: 'number', example: 4 },
                unhealthyConnections: { type: 'number', example: 1 },
                details: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            key: { type: 'string' },
                            healthy: { type: 'boolean' },
                            lastUsed: { type: 'string' },
                        },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ApiVersionController.prototype, "getPoolHealth", null);
__decorate([
    (0, common_1.Get)('performance'),
    (0, swagger_1.ApiOperation)({ summary: 'Get performance statistics for Odoo operations' }),
    (0, swagger_1.ApiQuery)({
        name: 'timeWindow',
        required: false,
        description: 'Time window in milliseconds (e.g., 3600000 for last hour)',
        example: 3600000
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Performance statistics',
        schema: {
            type: 'object',
            properties: {
                totalOperations: { type: 'number', example: 150 },
                averageDuration: { type: 'number', example: 245.5 },
                successRate: { type: 'number', example: 98.5 },
                errorCount: { type: 'number', example: 2 },
                slowestOperation: {
                    type: 'object',
                    properties: {
                        operation: { type: 'string', example: 'searchRead' },
                        model: { type: 'string', example: 'sale.order' },
                        duration: { type: 'number', example: 1250 },
                        timestamp: { type: 'string', example: '2025-01-26T10:00:00.000Z' },
                    },
                },
                operationCounts: {
                    type: 'object',
                    example: {
                        'searchRead:sale.order': 45,
                        'searchRead:res.partner': 32,
                        'create:sale.order': 15,
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('timeWindow')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getPerformanceStats", null);
__decorate([
    (0, common_1.Get)('performance/errors'),
    (0, swagger_1.ApiOperation)({ summary: 'Get recent error details' }),
    (0, swagger_1.ApiQuery)({
        name: 'count',
        required: false,
        description: 'Number of recent errors to return',
        example: 10
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Recent error details',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    operation: { type: 'string', example: 'searchRead' },
                    model: { type: 'string', example: 'sale.order' },
                    duration: { type: 'number', example: 1250 },
                    timestamp: { type: 'string', example: '2025-01-26T10:00:00.000Z' },
                    error: { type: 'string', example: 'Connection timeout' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('count')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ApiVersionController.prototype, "getRecentErrors", null);
exports.ApiVersionController = ApiVersionController = __decorate([
    (0, swagger_1.ApiTags)('API Information'),
    (0, common_1.Controller)('info'),
    __metadata("design:paramtypes", [odoo_connection_use_case_1.OdooConnectionUseCase,
        performance_monitor_service_1.PerformanceMonitorService])
], ApiVersionController);
//# sourceMappingURL=api-version.controller.js.map