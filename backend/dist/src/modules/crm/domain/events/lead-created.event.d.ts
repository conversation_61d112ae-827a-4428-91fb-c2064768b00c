export declare class LeadCreatedEvent {
    readonly leadId: number;
    readonly leadName: string;
    readonly email?: string | undefined;
    readonly source?: string | undefined;
    readonly score?: number | undefined;
    readonly timestamp: Date;
    constructor(leadId: number, leadName: string, email?: string | undefined, source?: string | undefined, score?: number | undefined, timestamp?: Date);
    static getEventName(): string;
    toPlainObject(): {
        eventName: string;
        leadId: number;
        leadName: string;
        email: string | undefined;
        source: string | undefined;
        score: number | undefined;
        timestamp: Date;
    };
}
