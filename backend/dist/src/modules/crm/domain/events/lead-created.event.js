"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadCreatedEvent = void 0;
class LeadCreatedEvent {
    leadId;
    leadName;
    email;
    source;
    score;
    timestamp;
    constructor(leadId, leadName, email, source, score, timestamp = new Date()) {
        this.leadId = leadId;
        this.leadName = leadName;
        this.email = email;
        this.source = source;
        this.score = score;
        this.timestamp = timestamp;
    }
    static getEventName() {
        return 'lead.created';
    }
    toPlainObject() {
        return {
            eventName: LeadCreatedEvent.getEventName(),
            leadId: this.leadId,
            leadName: this.leadName,
            email: this.email,
            source: this.source,
            score: this.score,
            timestamp: this.timestamp,
        };
    }
}
exports.LeadCreatedEvent = LeadCreatedEvent;
//# sourceMappingURL=lead-created.event.js.map