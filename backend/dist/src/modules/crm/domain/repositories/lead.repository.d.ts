import { Lead } from '../entities/lead.entity';
export interface ILeadRepository {
    save(lead: Lead): Promise<Lead>;
    findById(id: number): Promise<Lead | null>;
    findByEmail(email: string): Promise<Lead | null>;
    findMany(filters: {
        status?: string;
        source?: string;
        assignedUserId?: number;
        minScore?: number;
        maxScore?: number;
        offset?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        leads: Lead[];
        total: number;
        analytics: {
            averageScore: number;
            conversionRate: number;
            topSources: Array<{
                source: string;
                count: number;
            }>;
        };
    }>;
    updateStatus(id: number, status: string): Promise<boolean>;
    delete(id: number): Promise<boolean>;
    getStatistics(): Promise<{
        totalLeads: number;
        qualifiedLeads: number;
        convertedLeads: number;
        averageScore: number;
        conversionRate: number;
    }>;
}
