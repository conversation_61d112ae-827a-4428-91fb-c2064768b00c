import { OdooBaseModel } from '../../../../shared/domain/entities/odoo-base.entity';
import { LeadStatus } from '../value-objects/lead-status.vo';
import { ContactInfo } from '../value-objects/contact-info.vo';
export declare class Lead extends OdooBaseModel {
    readonly name: string;
    readonly contactInfo: ContactInfo;
    readonly status: LeadStatus;
    readonly source: string;
    readonly expectedRevenue?: number | undefined;
    readonly probability?: number | undefined;
    readonly description?: string | undefined;
    readonly assignedUserId?: number | undefined;
    readonly companyId?: number | undefined;
    readonly tags: string[];
    constructor(id: number, name: string, contactInfo: ContactInfo, status: LeadStatus, source: string, expectedRevenue?: number | undefined, probability?: number | undefined, description?: string | undefined, assignedUserId?: number | undefined, companyId?: number | undefined, tags?: string[], createdAt?: Date, updatedAt?: Date);
    isQualified(): boolean;
    canConvertToOpportunity(): boolean;
    calculateScore(): number;
    updateStatus(newStatus: LeadStatus, reason?: string): Lead;
    addTag(tag: string): Lead;
    assignTo(userId: number): Lead;
    private getSourceScore;
    toPlainObject(): {
        id: number;
        name: string;
        contactInfo: {
            email: string | undefined;
            phone: string | undefined;
            company: string | undefined;
            website: string | undefined;
            address: string | undefined;
            city: string | undefined;
            country: string | undefined;
            isComplete: boolean;
            hasContactMethod: boolean;
            completenessScore: number;
            primaryContactMethod: "none" | "email" | "phone";
            isBusinessEmail: boolean;
            formattedPhone: string;
            emailDomain: string;
        };
        status: string;
        source: string;
        expectedRevenue: number | undefined;
        probability: number | undefined;
        description: string | undefined;
        assignedUserId: number | undefined;
        companyId: number | undefined;
        tags: string[];
        score: number;
        isQualified: boolean;
        canConvert: boolean;
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
    };
}
