"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Lead = void 0;
const odoo_base_entity_1 = require("../../../../shared/domain/entities/odoo-base.entity");
class Lead extends odoo_base_entity_1.OdooBaseModel {
    name;
    contactInfo;
    status;
    source;
    expectedRevenue;
    probability;
    description;
    assignedUserId;
    companyId;
    tags;
    constructor(id, name, contactInfo, status, source, expectedRevenue, probability, description, assignedUserId, companyId, tags = [], createdAt, updatedAt) {
        super(id, createdAt, updatedAt);
        this.name = name;
        this.contactInfo = contactInfo;
        this.status = status;
        this.source = source;
        this.expectedRevenue = expectedRevenue;
        this.probability = probability;
        this.description = description;
        this.assignedUserId = assignedUserId;
        this.companyId = companyId;
        this.tags = tags;
    }
    isQualified() {
        return this.status.isQualified() &&
            this.contactInfo.isComplete() &&
            this.expectedRevenue !== undefined &&
            this.expectedRevenue > 0;
    }
    canConvertToOpportunity() {
        return this.isQualified() &&
            this.status.canConvert() &&
            this.assignedUserId !== undefined;
    }
    calculateScore() {
        let score = 0;
        score += this.status.getScoreWeight();
        if (this.expectedRevenue) {
            score += Math.min(this.expectedRevenue / 1000, 50);
        }
        if (this.probability) {
            score += this.probability * 0.3;
        }
        score += this.contactInfo.getCompletenessScore();
        score += this.getSourceScore();
        return Math.min(Math.round(score), 100);
    }
    updateStatus(newStatus, reason) {
        if (!this.status.canTransitionTo(newStatus)) {
            throw new Error(`Cannot transition from ${this.status.value} to ${newStatus.value}`);
        }
        return new Lead(this.id, this.name, this.contactInfo, newStatus, this.source, this.expectedRevenue, this.probability, this.description, this.assignedUserId, this.companyId, this.tags, this.createdAt, new Date());
    }
    addTag(tag) {
        if (this.tags.includes(tag)) {
            return this;
        }
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.expectedRevenue, this.probability, this.description, this.assignedUserId, this.companyId, [...this.tags, tag], this.createdAt, new Date());
    }
    assignTo(userId) {
        return new Lead(this.id, this.name, this.contactInfo, this.status, this.source, this.expectedRevenue, this.probability, this.description, userId, this.companyId, this.tags, this.createdAt, new Date());
    }
    getSourceScore() {
        const sourceScores = {
            'website': 15,
            'referral': 20,
            'social_media': 10,
            'email_campaign': 12,
            'cold_call': 8,
            'trade_show': 18,
            'partner': 16,
            'direct': 14,
        };
        return sourceScores[this.source.toLowerCase()] || 5;
    }
    toPlainObject() {
        return {
            id: this.id,
            name: this.name,
            contactInfo: this.contactInfo.toPlainObject(),
            status: this.status.value,
            source: this.source,
            expectedRevenue: this.expectedRevenue,
            probability: this.probability,
            description: this.description,
            assignedUserId: this.assignedUserId,
            companyId: this.companyId,
            tags: this.tags,
            score: this.calculateScore(),
            isQualified: this.isQualified(),
            canConvert: this.canConvertToOpportunity(),
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
exports.Lead = Lead;
//# sourceMappingURL=lead.entity.js.map