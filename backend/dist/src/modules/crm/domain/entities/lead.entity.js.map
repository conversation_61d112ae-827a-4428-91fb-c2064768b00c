{"version": 3, "file": "lead.entity.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/entities/lead.entity.ts"], "names": [], "mappings": ";;;AAAA,0FAAoF;AAQpF,MAAa,IAAK,SAAQ,gCAAa;IAGnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAXlB,YACE,EAAU,EACM,IAAY,EACZ,WAAwB,EACxB,MAAkB,EAClB,MAAc,EACd,eAAwB,EACxB,WAAoB,EACpB,WAAoB,EACpB,cAAuB,EACvB,SAAkB,EAClB,OAAiB,EAAE,EACnC,SAAgB,EAChB,SAAgB;QAEhB,KAAK,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAbhB,SAAI,GAAJ,IAAI,CAAQ;QACZ,gBAAW,GAAX,WAAW,CAAa;QACxB,WAAM,GAAN,MAAM,CAAY;QAClB,WAAM,GAAN,MAAM,CAAQ;QACd,oBAAe,GAAf,eAAe,CAAS;QACxB,gBAAW,GAAX,WAAW,CAAS;QACpB,gBAAW,GAAX,WAAW,CAAS;QACpB,mBAAc,GAAd,cAAc,CAAS;QACvB,cAAS,GAAT,SAAS,CAAS;QAClB,SAAI,GAAJ,IAAI,CAAe;IAKrC,CAAC;IAKD,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACzB,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;YAC7B,IAAI,CAAC,eAAe,KAAK,SAAS;YAClC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;IAClC,CAAC;IAKD,uBAAuB;QACrB,OAAO,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACxB,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC;IAC3C,CAAC;IAKD,cAAc;QACZ,IAAI,KAAK,GAAG,CAAC,CAAC;QAGd,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;QAGtC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,EAAE,EAAE,CAAC,CAAC;QACrD,CAAC;QAGD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,KAAK,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QAClC,CAAC;QAGD,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC;QAGjD,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;QAE/B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAKD,YAAY,CAAC,SAAqB,EAAE,MAAe;QACjD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,CAAC,MAAM,CAAC,KAAK,OAAO,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,WAAW,EAChB,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,GAAW;QAChB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,SAAS,EACd,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EACnB,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKD,QAAQ,CAAC,MAAc;QACrB,OAAO,IAAI,IAAI,CACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,WAAW,EAChB,MAAM,EACN,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,SAAS,EACd,IAAI,IAAI,EAAE,CACX,CAAC;IACJ,CAAC;IAKO,cAAc;QACpB,MAAM,YAAY,GAA2B;YAC3C,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,EAAE;YACd,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,EAAE;YACpB,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,EAAE;YAChB,SAAS,EAAE,EAAE;YACb,QAAQ,EAAE,EAAE;SACb,CAAC;QAEF,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAKD,aAAa;QACX,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YAC7C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE;YAC5B,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,UAAU,EAAE,IAAI,CAAC,uBAAuB,EAAE;YAC1C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF;AAlLD,oBAkLC"}