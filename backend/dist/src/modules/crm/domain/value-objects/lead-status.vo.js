"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadStatus = void 0;
class LeadStatus {
    value;
    static VALID_STATUSES = [
        'new',
        'contacted',
        'qualified',
        'proposal',
        'negotiation',
        'won',
        'lost',
        'cancelled'
    ];
    static STATUS_TRANSITIONS = {
        'new': ['contacted', 'qualified', 'lost', 'cancelled'],
        'contacted': ['qualified', 'proposal', 'lost', 'cancelled'],
        'qualified': ['proposal', 'negotiation', 'lost', 'cancelled'],
        'proposal': ['negotiation', 'won', 'lost', 'cancelled'],
        'negotiation': ['won', 'lost', 'cancelled'],
        'won': [],
        'lost': [],
        'cancelled': []
    };
    static STATUS_WEIGHTS = {
        'new': 5,
        'contacted': 10,
        'qualified': 20,
        'proposal': 30,
        'negotiation': 40,
        'won': 100,
        'lost': 0,
        'cancelled': 0
    };
    constructor(value) {
        this.value = value;
        if (!LeadStatus.VALID_STATUSES.includes(value)) {
            throw new Error(`Invalid lead status: ${value}`);
        }
    }
    isQualified() {
        return ['qualified', 'proposal', 'negotiation', 'won'].includes(this.value);
    }
    canConvert() {
        return ['qualified', 'proposal', 'negotiation'].includes(this.value);
    }
    isTerminal() {
        return ['won', 'lost', 'cancelled'].includes(this.value);
    }
    isWon() {
        return this.value === 'won';
    }
    isLost() {
        return ['lost', 'cancelled'].includes(this.value);
    }
    canTransitionTo(newStatus) {
        const allowedTransitions = LeadStatus.STATUS_TRANSITIONS[this.value] || [];
        return allowedTransitions.includes(newStatus.value);
    }
    getScoreWeight() {
        return LeadStatus.STATUS_WEIGHTS[this.value] || 0;
    }
    getLabel() {
        const labels = {
            'new': 'New Lead',
            'contacted': 'Contacted',
            'qualified': 'Qualified',
            'proposal': 'Proposal Sent',
            'negotiation': 'In Negotiation',
            'won': 'Won',
            'lost': 'Lost',
            'cancelled': 'Cancelled'
        };
        return labels[this.value] || this.value;
    }
    getColor() {
        const colors = {
            'new': '#6c757d',
            'contacted': '#17a2b8',
            'qualified': '#ffc107',
            'proposal': '#fd7e14',
            'negotiation': '#6f42c1',
            'won': '#28a745',
            'lost': '#dc3545',
            'cancelled': '#6c757d'
        };
        return colors[this.value] || '#6c757d';
    }
    getNextStatuses() {
        const nextStatusValues = LeadStatus.STATUS_TRANSITIONS[this.value] || [];
        return nextStatusValues.map(status => new LeadStatus(status));
    }
    static fromString(value) {
        return new LeadStatus(value.toLowerCase());
    }
    static getAllStatuses() {
        return LeadStatus.VALID_STATUSES.map(status => new LeadStatus(status));
    }
    equals(other) {
        return this.value === other.value;
    }
    toString() {
        return this.value;
    }
    toJSON() {
        return this.value;
    }
}
exports.LeadStatus = LeadStatus;
//# sourceMappingURL=lead-status.vo.js.map