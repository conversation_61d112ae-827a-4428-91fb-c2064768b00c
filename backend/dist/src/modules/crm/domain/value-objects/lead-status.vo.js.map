{"version": 3, "file": "lead-status.vo.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/crm/domain/value-objects/lead-status.vo.ts"], "names": [], "mappings": ";;;AAIA,MAAa,UAAU;IAkCO;IAjCpB,MAAM,CAAU,cAAc,GAAG;QACvC,KAAK;QACL,WAAW;QACX,WAAW;QACX,UAAU;QACV,aAAa;QACb,KAAK;QACL,MAAM;QACN,WAAW;KACH,CAAC;IAEH,MAAM,CAAU,kBAAkB,GAA6B;QACrE,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,CAAC;QACtD,WAAW,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,CAAC;QAC3D,WAAW,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,MAAM,EAAE,WAAW,CAAC;QAC7D,UAAU,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC;QACvD,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC;QAC3C,KAAK,EAAE,EAAE;QACT,MAAM,EAAE,EAAE;QACV,WAAW,EAAE,EAAE;KAChB,CAAC;IAEM,MAAM,CAAU,cAAc,GAA2B;QAC/D,KAAK,EAAE,CAAC;QACR,WAAW,EAAE,EAAE;QACf,WAAW,EAAE,EAAE;QACf,UAAU,EAAE,EAAE;QACd,aAAa,EAAE,EAAE;QACjB,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,CAAC;QACT,WAAW,EAAE,CAAC;KACf,CAAC;IAEF,YAA4B,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QACvC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAY,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,EAAE,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAKD,WAAW;QACT,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9E,CAAC;IAKD,UAAU;QACR,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvE,CAAC;IAKD,UAAU;QACR,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3D,CAAC;IAKD,KAAK;QACH,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC;IAC9B,CAAC;IAKD,MAAM;QACJ,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IAKD,eAAe,CAAC,SAAqB;QACnC,MAAM,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAC3E,OAAO,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAKD,cAAc;QACZ,OAAO,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAKD,QAAQ;QACN,MAAM,MAAM,GAA2B;YACrC,KAAK,EAAE,UAAU;YACjB,WAAW,EAAE,WAAW;YACxB,WAAW,EAAE,WAAW;YACxB,UAAU,EAAE,eAAe;YAC3B,aAAa,EAAE,gBAAgB;YAC/B,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,MAAM;YACd,WAAW,EAAE,WAAW;SACzB,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;IAC1C,CAAC;IAKD,QAAQ;QACN,MAAM,MAAM,GAA2B;YACrC,KAAK,EAAE,SAAS;YAChB,WAAW,EAAE,SAAS;YACtB,WAAW,EAAE,SAAS;YACtB,UAAU,EAAE,SAAS;YACrB,aAAa,EAAE,SAAS;YACxB,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,SAAS;YACjB,WAAW,EAAE,SAAS;SACvB,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;IACzC,CAAC;IAKD,eAAe;QACb,MAAM,gBAAgB,GAAG,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QACzE,OAAO,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IAKD,MAAM,CAAC,UAAU,CAAC,KAAa;QAC7B,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;IAC7C,CAAC;IAKD,MAAM,CAAC,cAAc;QACnB,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IAKD,MAAM,CAAC,KAAiB;QACtB,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC;IACpC,CAAC;IAKD,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAKD,MAAM;QACJ,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;;AAvKH,gCAwKC"}