"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactInfo = void 0;
class ContactInfo {
    email;
    phone;
    company;
    website;
    address;
    city;
    country;
    constructor(email, phone, company, website, address, city, country) {
        this.email = email;
        this.phone = phone;
        this.company = company;
        this.website = website;
        this.address = address;
        this.city = city;
        this.country = country;
        this.validateEmail();
        this.validatePhone();
        this.validateWebsite();
    }
    isComplete() {
        return !!(this.email && this.phone && this.company);
    }
    hasContactMethod() {
        return !!(this.email || this.phone);
    }
    hasEmail() {
        return !!this.email;
    }
    hasPhone() {
        return !!this.phone;
    }
    hasCompany() {
        return !!this.company;
    }
    getCompletenessScore() {
        let score = 0;
        if (this.email)
            score += 25;
        if (this.phone)
            score += 25;
        if (this.company)
            score += 20;
        if (this.website)
            score += 10;
        if (this.address)
            score += 10;
        if (this.city)
            score += 5;
        if (this.country)
            score += 5;
        return score;
    }
    getMissingFields() {
        const missing = [];
        if (!this.email)
            missing.push('email');
        if (!this.phone)
            missing.push('phone');
        if (!this.company)
            missing.push('company');
        if (!this.website)
            missing.push('website');
        if (!this.address)
            missing.push('address');
        return missing;
    }
    getPrimaryContactMethod() {
        if (this.email)
            return 'email';
        if (this.phone)
            return 'phone';
        return 'none';
    }
    getFormattedPhone() {
        if (!this.phone)
            return '';
        const cleaned = this.phone.replace(/\D/g, '');
        if (cleaned.length === 10) {
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        }
        return this.phone;
    }
    getEmailDomain() {
        if (!this.email)
            return '';
        const parts = this.email.split('@');
        return parts.length > 1 ? parts[1].toLowerCase() : '';
    }
    isBusinessEmail() {
        if (!this.email)
            return false;
        const domain = this.getEmailDomain();
        const personalDomains = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'aol.com', 'icloud.com', 'live.com', 'msn.com'
        ];
        return !personalDomains.includes(domain);
    }
    updateEmail(email) {
        return new ContactInfo(email, this.phone, this.company, this.website, this.address, this.city, this.country);
    }
    updatePhone(phone) {
        return new ContactInfo(this.email, phone, this.company, this.website, this.address, this.city, this.country);
    }
    updateCompany(company) {
        return new ContactInfo(this.email, this.phone, company, this.website, this.address, this.city, this.country);
    }
    validateEmail() {
        if (this.email && !this.isValidEmail(this.email)) {
            throw new Error(`Invalid email format: ${this.email}`);
        }
    }
    validatePhone() {
        if (this.phone && !this.isValidPhone(this.phone)) {
            throw new Error(`Invalid phone format: ${this.phone}`);
        }
    }
    validateWebsite() {
        if (this.website && !this.isValidWebsite(this.website)) {
            throw new Error(`Invalid website format: ${this.website}`);
        }
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        const cleaned = phone.replace(/[\s\-\(\)\.]/g, '');
        return phoneRegex.test(cleaned) && cleaned.length >= 7;
    }
    isValidWebsite(website) {
        try {
            new URL(website);
            return true;
        }
        catch {
            try {
                new URL(`https://${website}`);
                return true;
            }
            catch {
                return false;
            }
        }
    }
    equals(other) {
        return this.email === other.email &&
            this.phone === other.phone &&
            this.company === other.company &&
            this.website === other.website &&
            this.address === other.address &&
            this.city === other.city &&
            this.country === other.country;
    }
    toPlainObject() {
        return {
            email: this.email,
            phone: this.phone,
            company: this.company,
            website: this.website,
            address: this.address,
            city: this.city,
            country: this.country,
            isComplete: this.isComplete(),
            hasContactMethod: this.hasContactMethod(),
            completenessScore: this.getCompletenessScore(),
            primaryContactMethod: this.getPrimaryContactMethod(),
            isBusinessEmail: this.isBusinessEmail(),
            formattedPhone: this.getFormattedPhone(),
            emailDomain: this.getEmailDomain(),
        };
    }
    toString() {
        const parts = [];
        if (this.email)
            parts.push(this.email);
        if (this.phone)
            parts.push(this.phone);
        if (this.company)
            parts.push(this.company);
        return parts.join(' | ');
    }
}
exports.ContactInfo = ContactInfo;
//# sourceMappingURL=contact-info.vo.js.map