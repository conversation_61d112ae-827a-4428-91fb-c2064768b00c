export declare class ContactInfo {
    readonly email?: string | undefined;
    readonly phone?: string | undefined;
    readonly company?: string | undefined;
    readonly website?: string | undefined;
    readonly address?: string | undefined;
    readonly city?: string | undefined;
    readonly country?: string | undefined;
    constructor(email?: string | undefined, phone?: string | undefined, company?: string | undefined, website?: string | undefined, address?: string | undefined, city?: string | undefined, country?: string | undefined);
    isComplete(): boolean;
    hasContactMethod(): boolean;
    hasEmail(): boolean;
    hasPhone(): boolean;
    hasCompany(): boolean;
    getCompletenessScore(): number;
    getMissingFields(): string[];
    getPrimaryContactMethod(): 'email' | 'phone' | 'none';
    getFormattedPhone(): string;
    getEmailDomain(): string;
    isBusinessEmail(): boolean;
    updateEmail(email: string): ContactInfo;
    updatePhone(phone: string): ContactInfo;
    updateCompany(company: string): ContactInfo;
    private validateEmail;
    private validatePhone;
    private validateWebsite;
    private isValidEmail;
    private isValidPhone;
    private isValidWebsite;
    equals(other: ContactInfo): boolean;
    toPlainObject(): {
        email: string | undefined;
        phone: string | undefined;
        company: string | undefined;
        website: string | undefined;
        address: string | undefined;
        city: string | undefined;
        country: string | undefined;
        isComplete: boolean;
        hasContactMethod: boolean;
        completenessScore: number;
        primaryContactMethod: "none" | "email" | "phone";
        isBusinessEmail: boolean;
        formattedPhone: string;
        emailDomain: string;
    };
    toString(): string;
}
