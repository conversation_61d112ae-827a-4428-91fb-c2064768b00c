export declare class LeadStatus {
    readonly value: string;
    private static readonly VALID_STATUSES;
    private static readonly STATUS_TRANSITIONS;
    private static readonly STATUS_WEIGHTS;
    constructor(value: string);
    isQualified(): boolean;
    canConvert(): boolean;
    isTerminal(): boolean;
    isWon(): boolean;
    isLost(): boolean;
    canTransitionTo(newStatus: LeadStatus): boolean;
    getScoreWeight(): number;
    getLabel(): string;
    getColor(): string;
    getNextStatuses(): LeadStatus[];
    static fromString(value: string): LeadStatus;
    static getAllStatuses(): LeadStatus[];
    equals(other: LeadStatus): boolean;
    toString(): string;
    toJSON(): string;
}
