"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CreateLeadUseCase_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLeadUseCase = void 0;
const common_1 = require("@nestjs/common");
const lead_entity_1 = require("../../domain/entities/lead.entity");
const lead_status_vo_1 = require("../../domain/value-objects/lead-status.vo");
const contact_info_vo_1 = require("../../domain/value-objects/contact-info.vo");
let CreateLeadUseCase = CreateLeadUseCase_1 = class CreateLeadUseCase {
    logger = new common_1.Logger(CreateLeadUseCase_1.name);
    constructor() { }
    async execute(request) {
        try {
            this.logger.log(`Creating new lead: ${request.name}`);
            const mockLead = new lead_entity_1.Lead(1, request.name, new contact_info_vo_1.ContactInfo(request.email, request.phone, request.company), new lead_status_vo_1.LeadStatus('new'), request.source, request.expectedRevenue, request.probability || 50, request.description, request.assignedUserId, undefined, request.tags || []);
            const score = mockLead.calculateScore();
            const recommendations = ['Mock recommendation: Follow up within 24 hours'];
            this.logger.log(`Lead created successfully (mock): ${request.name}, Score: ${score}`);
            return {
                lead: mockLead,
                score,
                recommendations,
            };
        }
        catch (error) {
            this.logger.error(`Failed to create lead: ${request.name}`, error);
            throw error;
        }
    }
};
exports.CreateLeadUseCase = CreateLeadUseCase;
exports.CreateLeadUseCase = CreateLeadUseCase = CreateLeadUseCase_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], CreateLeadUseCase);
//# sourceMappingURL=create-lead.use-case.js.map