import { Lead } from '../../domain/entities/lead.entity';
export interface CreateLeadRequest {
    name: string;
    email?: string;
    phone?: string;
    company?: string;
    source: string;
    expectedRevenue?: number;
    probability?: number;
    description?: string;
    assignedUserId?: number;
    tags?: string[];
}
export interface CreateLeadResponse {
    lead: Lead;
    score: number;
    recommendations: string[];
}
export declare class CreateLeadUseCase {
    private readonly logger;
    constructor();
    execute(request: CreateLeadRequest): Promise<CreateLeadResponse>;
}
