"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeadsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../../../infrastructure/auth/jwt-auth.guard");
const create_lead_use_case_1 = require("../../application/use-cases/create-lead.use-case");
const update_lead_use_case_1 = require("../../application/use-cases/update-lead.use-case");
const get_leads_query_1 = require("../../application/queries/get-leads.query");
const create_lead_dto_1 = require("../../application/dtos/create-lead.dto");
const update_lead_dto_1 = require("../../application/dtos/update-lead.dto");
const lead_filter_dto_1 = require("../../application/dtos/lead-filter.dto");
let LeadsController = class LeadsController {
    createLeadUseCase;
    updateLeadUseCase;
    getLeadsQuery;
    constructor(createLeadUseCase, updateLeadUseCase, getLeadsQuery) {
        this.createLeadUseCase = createLeadUseCase;
        this.updateLeadUseCase = updateLeadUseCase;
        this.getLeadsQuery = getLeadsQuery;
    }
    async createLead(createLeadDto, request) {
        const startTime = Date.now();
        const result = await this.createLeadUseCase.execute({
            name: createLeadDto.name,
            email: createLeadDto.email,
            phone: createLeadDto.phone,
            company: createLeadDto.company,
            source: createLeadDto.source,
            expectedRevenue: createLeadDto.expectedRevenue,
            probability: createLeadDto.probability,
            description: createLeadDto.description,
            assignedUserId: createLeadDto.assignedUserId,
            tags: createLeadDto.tags,
        });
        return {
            success: true,
            data: result,
            message: `Lead created successfully with score ${result.score}`,
            statusCode: 201,
            meta: {
                leadScore: result.score,
                autoAssigned: !!result.lead.assignedUserId,
                assignedUserId: result.lead.assignedUserId,
                processingTime: Date.now() - startTime,
            }
        };
    }
    async getLeads(filterDto, request) {
        const startTime = Date.now();
        const result = await this.getLeadsQuery.execute(filterDto);
        return {
            success: true,
            data: result.leads,
            message: `Retrieved ${result.leads.length} leads`,
            statusCode: 200,
            meta: {
                totalLeads: result.total,
                averageScore: result.analytics.averageScore,
                conversionRate: result.analytics.conversionRate,
                topSources: result.analytics.topSources,
                processingTime: Date.now() - startTime,
            }
        };
    }
    async getLeadById(id, request) {
    }
    async updateLead(id, updateLeadDto, request) {
    }
    async deleteLead(id, request) {
    }
    async convertToOpportunity(id, request) {
    }
};
exports.LeadsController = LeadsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: '🎯 Create New Lead',
        description: `
**Create a new lead in the CRM system with intelligent scoring and auto-assignment.**

### Features:
- 🤖 **Auto-scoring**: Intelligent lead scoring based on multiple factors
- 👥 **Auto-assignment**: Automatic assignment to appropriate sales reps
- 🏷️ **Auto-tagging**: Automatic tag generation based on lead characteristics
- 📊 **Recommendations**: AI-powered recommendations for lead management
- ✅ **Validation**: Comprehensive business rule validation

### Scoring Factors:
- Lead source quality (referral > website > cold call)
- Expected revenue potential
- Contact information completeness
- Probability assessment
- Company information availability

### Auto-assignment Rules:
- Territory-based assignment
- Workload balancing
- Source-specific routing
- Skill-based matching
    `,
    }),
    (0, swagger_1.ApiConsumes)('application/json'),
    (0, swagger_1.ApiProduces)('application/json'),
    (0, swagger_1.ApiBody)({
        type: create_lead_dto_1.CreateLeadDto,
        description: 'Lead creation data with contact and business information',
        examples: {
            'High-Value Lead': {
                summary: 'High-value enterprise lead',
                description: 'Example of a high-potential enterprise lead',
                value: {
                    name: 'John Smith - Enterprise Solutions',
                    email: '<EMAIL>',
                    phone: '******-0123',
                    company: 'Enterprise Corp',
                    source: 'referral',
                    expectedRevenue: 150000,
                    probability: 70,
                    description: 'Looking for comprehensive ERP solution for 500+ employees',
                    tags: ['enterprise', 'erp', 'urgent']
                }
            },
            'Website Lead': {
                summary: 'Website inquiry lead',
                description: 'Lead from website contact form',
                value: {
                    name: 'Sarah Johnson',
                    email: '<EMAIL>',
                    phone: '******-0456',
                    company: 'Small Business Inc',
                    source: 'website',
                    expectedRevenue: 25000,
                    description: 'Interested in accounting software for small business'
                }
            },
            'Trade Show Lead': {
                summary: 'Trade show contact',
                description: 'Lead collected at industry trade show',
                value: {
                    name: 'Mike Chen',
                    email: '<EMAIL>',
                    phone: '******-0789',
                    company: 'Manufacturing Solutions',
                    source: 'trade_show',
                    expectedRevenue: 75000,
                    probability: 45,
                    description: 'Interested in inventory management system',
                    tags: ['manufacturing', 'inventory']
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '✅ Lead created successfully',
        content: {
            'application/json': {
                examples: {
                    'Success Response': {
                        summary: 'Successful lead creation',
                        value: {
                            success: true,
                            data: {
                                lead: {
                                    id: 123,
                                    name: 'John Smith - Enterprise Solutions',
                                    contactInfo: {
                                        email: '<EMAIL>',
                                        phone: '******-0123',
                                        company: 'Enterprise Corp'
                                    },
                                    status: 'new',
                                    source: 'referral',
                                    expectedRevenue: 150000,
                                    probability: 70,
                                    assignedUserId: 5,
                                    tags: ['enterprise', 'erp', 'urgent', 'source:referral', 'high-value'],
                                    score: 85,
                                    isQualified: false,
                                    canConvert: false
                                },
                                score: 85,
                                recommendations: [
                                    'High-priority lead - contact immediately',
                                    'Complete contact information available',
                                    'High revenue potential - prioritize qualification'
                                ]
                            },
                            message: 'Lead created successfully with score 85',
                            statusCode: 201,
                            apiVersion: 'v1',
                            timestamp: '2025-07-26T08:30:15.123Z',
                            path: '/api/v1/crm/leads',
                            meta: {
                                leadScore: 85,
                                autoAssigned: true,
                                assignedUserId: 5,
                                autoTags: ['source:referral', 'high-value'],
                                processingTime: 245
                            }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '❌ Invalid lead data or business rule violation'
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: '⚠️ Duplicate lead exists'
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_lead_dto_1.CreateLeadDto, Object]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "createLead", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: '📋 Get Leads List',
        description: `
**Retrieve leads with advanced filtering, sorting, and pagination.**

### Features:
- 🔍 **Advanced Filtering**: Filter by status, source, score, assignment
- 📊 **Sorting**: Sort by score, revenue, date, status
- 📄 **Pagination**: Efficient pagination with metadata
- 📈 **Analytics**: Lead statistics and conversion metrics
- 🎯 **Search**: Full-text search across lead data

### Filter Options:
- Status: new, contacted, qualified, proposal, negotiation, won, lost
- Source: website, referral, social_media, email_campaign, etc.
- Score range: minimum and maximum score values
- Revenue range: expected revenue filtering
- Assignment: assigned/unassigned leads
- Date range: creation date filtering
    `,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        description: 'Filter by lead status',
        enum: ['new', 'contacted', 'qualified', 'proposal', 'negotiation', 'won', 'lost', 'cancelled']
    }),
    (0, swagger_1.ApiQuery)({
        name: 'source',
        required: false,
        description: 'Filter by lead source'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'minScore',
        required: false,
        description: 'Minimum lead score (0-100)',
        type: Number
    }),
    (0, swagger_1.ApiQuery)({
        name: 'maxScore',
        required: false,
        description: 'Maximum lead score (0-100)',
        type: Number
    }),
    (0, swagger_1.ApiQuery)({
        name: 'assignedUserId',
        required: false,
        description: 'Filter by assigned user ID',
        type: Number
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number (1-based)',
        type: Number,
        example: 1
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Items per page (max 100)',
        type: Number,
        example: 20
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        required: false,
        description: 'Sort field',
        enum: ['score', 'expectedRevenue', 'createdAt', 'updatedAt', 'name']
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortOrder',
        required: false,
        description: 'Sort order',
        enum: ['asc', 'desc']
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '✅ Leads retrieved successfully'
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [lead_filter_dto_1.LeadFilterDto, Object]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getLeads", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '🔍 Get Lead Details',
        description: 'Retrieve detailed information about a specific lead including history and analytics'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Lead ID',
        type: Number,
        example: 123
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '✅ Lead details retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '❌ Lead not found'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "getLeadById", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '✏️ Update Lead',
        description: 'Update lead information with automatic re-scoring and validation'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_lead_dto_1.UpdateLeadDto, Object]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "updateLead", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '🗑️ Delete Lead',
        description: 'Soft delete a lead (marks as cancelled)'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "deleteLead", null);
__decorate([
    (0, common_1.Post)(':id/convert'),
    (0, swagger_1.ApiOperation)({
        summary: '🔄 Convert Lead to Opportunity',
        description: 'Convert a qualified lead to an opportunity'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], LeadsController.prototype, "convertToOpportunity", null);
exports.LeadsController = LeadsController = __decorate([
    (0, swagger_1.ApiTags)('CRM - Leads'),
    (0, common_1.Controller)({ path: 'crm/leads', version: '1' }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [create_lead_use_case_1.CreateLeadUseCase,
        update_lead_use_case_1.UpdateLeadUseCase,
        get_leads_query_1.GetLeadsQuery])
], LeadsController);
//# sourceMappingURL=leads.controller.js.map