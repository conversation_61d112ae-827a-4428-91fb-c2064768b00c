import { Request } from 'express';
import { CreateLeadUseCase } from '../../application/use-cases/create-lead.use-case';
import { UpdateLeadUseCase } from '../../application/use-cases/update-lead.use-case';
import { GetLeadsQuery } from '../../application/queries/get-leads.query';
import { CreateLeadDto } from '../../application/dtos/create-lead.dto';
import { UpdateLeadDto } from '../../application/dtos/update-lead.dto';
import { LeadFilterDto } from '../../application/dtos/lead-filter.dto';
export declare class LeadsController {
    private readonly createLeadUseCase;
    private readonly updateLeadUseCase;
    private readonly getLeadsQuery;
    constructor(createLeadUseCase: CreateLeadUseCase, updateLeadUseCase: UpdateLeadUseCase, getLeadsQuery: GetLeadsQuery);
    createLead(createLeadDto: CreateLeadDto, request: Request): Promise<{
        success: boolean;
        data: import("../../application/use-cases/create-lead.use-case").CreateLeadResponse;
        message: string;
        statusCode: number;
        meta: {
            leadScore: number;
            autoAssigned: boolean;
            assignedUserId: number | undefined;
            processingTime: number;
        };
    }>;
    getLeads(filterDto: LeadFilterDto, request: Request): Promise<{
        success: boolean;
        data: never[];
        message: string;
        statusCode: number;
        meta: {
            totalLeads: number;
            averageScore: number;
            conversionRate: number;
            topSources: never[];
            processingTime: number;
        };
    }>;
    getLeadById(id: number, request: Request): Promise<void>;
    updateLead(id: number, updateLeadDto: UpdateLeadDto, request: Request): Promise<void>;
    deleteLead(id: number, request: Request): Promise<void>;
    convertToOpportunity(id: number, request: Request): Promise<void>;
}
