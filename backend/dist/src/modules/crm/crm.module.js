"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrmModule = void 0;
const common_1 = require("@nestjs/common");
const common_module_1 = require("../../common/common.module");
const create_lead_use_case_1 = require("./application/use-cases/create-lead.use-case");
const update_lead_use_case_1 = require("./application/use-cases/update-lead.use-case");
const get_leads_query_1 = require("./application/queries/get-leads.query");
const leads_controller_1 = require("./presentation/controllers/leads.controller");
const UseCases = [
    create_lead_use_case_1.CreateLeadUseCase,
    update_lead_use_case_1.UpdateLeadUseCase,
];
const Queries = [
    get_leads_query_1.GetLeadsQuery,
];
const Controllers = [
    leads_controller_1.LeadsController,
];
let CrmModule = class CrmModule {
};
exports.CrmModule = CrmModule;
exports.CrmModule = CrmModule = __decorate([
    (0, common_1.Module)({
        imports: [
            common_module_1.CommonModule,
        ],
        providers: [
            ...UseCases,
            ...Queries,
        ],
        controllers: Controllers,
        exports: [
            ...UseCases,
            ...Queries,
        ],
    })
], CrmModule);
//# sourceMappingURL=crm.module.js.map