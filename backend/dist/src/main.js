"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const api_versioning_config_1 = require("./infrastructure/config/api-versioning.config");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.enableCors();
    app.setGlobalPrefix('api');
    app.enableVersioning(api_versioning_config_1.API_VERSIONING_CONFIG);
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Universal Odoo Adapter API')
        .setDescription(`
# Universal Odoo Adapter API

A comprehensive, enterprise-grade API for connecting to multiple Odoo versions with automatic protocol selection and intelligent version detection.

## Features
- 🔄 **Multi-version Support**: Compatible with Odoo 13, 15, 17, 18+
- 🌐 **Multi-protocol**: XML-RPC, JSON-RPC, REST API support
- 🤖 **Auto-detection**: Automatic version and protocol selection
- 🔐 **Secure**: JWT authentication with encrypted password storage
- 📊 **Scalable**: Connection pooling and MongoDB persistence
- 🎯 **Type-safe**: Full TypeScript support with comprehensive validation

## Authentication
All protected endpoints require a JWT token obtained from the \`/connect\` endpoint.

\`\`\`bash
# 1. Connect to get token
curl -X POST /api/v1/odoo/connect \\
  -H "Content-Type: application/json" \\
  -d '{"host": "https://your-odoo.com", "database": "db", "username": "user", "password": "pass"}'

# 2. Use token for API calls
curl -H "Authorization: Bearer YOUR_TOKEN" /api/v1/odoo/res.users/search
\`\`\`
    `)
        .setVersion('1.0.1')
        .setContact('Universal Odoo Adapter Team', 'https://github.com/your-org/universal-odoo-adapter', '<EMAIL>')
        .setLicense('MIT', 'https://opensource.org/licenses/MIT')
        .addServer('http://localhost:3000', 'Development Server')
        .addServer('https://api.example.com', 'Production Server')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token obtained from /connect endpoint',
        in: 'header',
    }, 'JWT-auth')
        .addTag('API Information', 'General API information and health checks')
        .addTag('Authentication', 'Odoo connection and JWT token management')
        .addTag('Data Operations', 'CRUD operations on Odoo models')
        .addTag('Advanced Operations', 'Custom method execution and batch operations')
        .addTag('Connection Management', 'Connection pooling and instance management')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document);
    const v1Config = new swagger_1.DocumentBuilder()
        .setTitle('Universal Odoo Adapter API v1')
        .setDescription(`
# Universal Odoo Adapter API v1

Version 1 of the Universal Odoo Adapter API with comprehensive Odoo integration capabilities.

## Quick Start
1. **Connect**: Use \`POST /connect\` to authenticate with your Odoo instance
2. **Search**: Use \`POST /{model}/search\` to query records
3. **Create**: Use \`POST /{model}/create\` to create new records
4. **Update**: Use \`PUT /{model}/{id}\` to update existing records
5. **Delete**: Use \`DELETE /{model}/{id}\` to delete records

## Supported Odoo Versions
- Odoo 13.0 (Community & Enterprise)
- Odoo 15.0 (Community & Enterprise)
- Odoo 17.0 (Community & Enterprise)
- Odoo 18.0+ (Community & Enterprise)
    `)
        .setVersion('1.0.1')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token obtained from /connect endpoint',
        in: 'header',
    }, 'JWT-auth')
        .addTag('Authentication', 'Odoo connection and JWT token management')
        .addTag('Data Operations', 'CRUD operations on Odoo models')
        .addTag('Advanced Operations', 'Custom method execution and batch operations')
        .addTag('Connection Management', 'Connection pooling and instance management')
        .build();
    const v1Document = swagger_1.SwaggerModule.createDocument(app, v1Config, {
        include: [],
    });
    swagger_1.SwaggerModule.setup('api/v1/docs', app, v1Document);
    const port = process.env.PORT ?? 3000;
    await app.listen(port);
    console.log(`🚀 Universal Odoo Adapter API is running on: http://localhost:${port}`);
    console.log(`📚 API Documentation:`);
    console.log(`   • Main docs: http://localhost:${port}/api/docs`);
    console.log(`   • v1 docs:   http://localhost:${port}/api/v1/docs`);
    console.log(`📋 API Endpoints:`);
    console.log(`   • API info:  http://localhost:${port}/api/v1/info`);
    console.log(`   • Health:    http://localhost:${port}/api/v1/info/health`);
    console.log(`   • v1 base:   http://localhost:${port}/api/v1/odoo`);
}
bootstrap();
//# sourceMappingURL=main.js.map