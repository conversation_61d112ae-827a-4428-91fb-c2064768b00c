"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SwaggerSearchResponseExample = exports.SwaggerConnectResponseExample = exports.SwaggerPaginationExample = exports.SwaggerErrorResponseExample = exports.SwaggerSuccessResponseExample = void 0;
const swagger_1 = require("@nestjs/swagger");
class SwaggerSuccessResponseExample {
    success;
    data;
    message;
    statusCode;
    apiVersion;
    timestamp;
    path;
    meta;
}
exports.SwaggerSuccessResponseExample = SwaggerSuccessResponseExample;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: true,
        description: 'Indicates if the request was successful'
    }),
    __metadata("design:type", Boolean)
], SwaggerSuccessResponseExample.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { id: 1, name: 'John Doe', email: '<EMAIL>' },
        description: 'The actual response data'
    }),
    __metadata("design:type", Object)
], SwaggerSuccessResponseExample.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Request completed successfully',
        description: 'Human-readable success message'
    }),
    __metadata("design:type", String)
], SwaggerSuccessResponseExample.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 200,
        description: 'HTTP status code'
    }),
    __metadata("design:type", Number)
], SwaggerSuccessResponseExample.prototype, "statusCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'v1',
        description: 'API version used'
    }),
    __metadata("design:type", String)
], SwaggerSuccessResponseExample.prototype, "apiVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2025-07-26T07:25:06.791Z',
        description: 'ISO timestamp of the response'
    }),
    __metadata("design:type", String)
], SwaggerSuccessResponseExample.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '/api/v1/odoo/res.users/search',
        description: 'Request path',
        required: false
    }),
    __metadata("design:type", String)
], SwaggerSuccessResponseExample.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { duration: 234, recordCount: 10 },
        description: 'Additional metadata about the request',
        required: false
    }),
    __metadata("design:type", Object)
], SwaggerSuccessResponseExample.prototype, "meta", void 0);
class SwaggerErrorResponseExample {
    success;
    error;
    message;
    statusCode;
    apiVersion;
    timestamp;
    path;
    errorCode;
    validationErrors;
}
exports.SwaggerErrorResponseExample = SwaggerErrorResponseExample;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: false,
        description: 'Indicates the request failed'
    }),
    __metadata("design:type", Boolean)
], SwaggerErrorResponseExample.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Unauthorized',
        description: 'Error type/category'
    }),
    __metadata("design:type", String)
], SwaggerErrorResponseExample.prototype, "error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Authentication token required. Please connect to Odoo first.',
        description: 'Human-readable error message'
    }),
    __metadata("design:type", String)
], SwaggerErrorResponseExample.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 401,
        description: 'HTTP status code'
    }),
    __metadata("design:type", Number)
], SwaggerErrorResponseExample.prototype, "statusCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'v1',
        description: 'API version used'
    }),
    __metadata("design:type", String)
], SwaggerErrorResponseExample.prototype, "apiVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '2025-07-26T07:25:06.791Z',
        description: 'ISO timestamp of the response'
    }),
    __metadata("design:type", String)
], SwaggerErrorResponseExample.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '/api/v1/odoo/res.users/search',
        description: 'Request path',
        required: false
    }),
    __metadata("design:type", String)
], SwaggerErrorResponseExample.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'UNAUTHORIZED',
        description: 'Machine-readable error code',
        required: false
    }),
    __metadata("design:type", String)
], SwaggerErrorResponseExample.prototype, "errorCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: [Object],
        example: [
            {
                field: 'email',
                message: 'Email must be a valid email address',
                value: 'invalid-email',
                rule: 'isEmail'
            }
        ],
        description: 'Validation errors for 400 responses',
        required: false
    }),
    __metadata("design:type", Array)
], SwaggerErrorResponseExample.prototype, "validationErrors", void 0);
class SwaggerPaginationExample {
    page;
    limit;
    total;
    totalPages;
    hasNext;
    hasPrev;
    nextPage;
    prevPage;
}
exports.SwaggerPaginationExample = SwaggerPaginationExample;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'Current page number (1-based)'
    }),
    __metadata("design:type", Number)
], SwaggerPaginationExample.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 10,
        description: 'Number of items per page'
    }),
    __metadata("design:type", Number)
], SwaggerPaginationExample.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 150,
        description: 'Total number of items'
    }),
    __metadata("design:type", Number)
], SwaggerPaginationExample.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 15,
        description: 'Total number of pages'
    }),
    __metadata("design:type", Number)
], SwaggerPaginationExample.prototype, "totalPages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: true,
        description: 'Whether there is a next page'
    }),
    __metadata("design:type", Boolean)
], SwaggerPaginationExample.prototype, "hasNext", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: false,
        description: 'Whether there is a previous page'
    }),
    __metadata("design:type", Boolean)
], SwaggerPaginationExample.prototype, "hasPrev", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 2,
        description: 'Next page number (if exists)',
        required: false
    }),
    __metadata("design:type", Number)
], SwaggerPaginationExample.prototype, "nextPage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: null,
        description: 'Previous page number (if exists)',
        required: false
    }),
    __metadata("design:type", Number)
], SwaggerPaginationExample.prototype, "prevPage", void 0);
class SwaggerConnectResponseExample {
    success;
    data;
    message;
    statusCode;
    apiVersion;
    timestamp;
    path;
    meta;
}
exports.SwaggerConnectResponseExample = SwaggerConnectResponseExample;
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    __metadata("design:type", Boolean)
], SwaggerConnectResponseExample.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: {
            token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhbm9uX09qb3hPbU4xIiwib2Rvb0luc3RhbmNlSWQiOiJvZG9vX2FIUjBjSE02THk5diIsIm9kb29Ib3N0IjoiaHR0cHM6Ly9vZG9vMTguYmVzdG1peC5vbmUvIiwib2Rvb0RhdGFiYXNlIjoiYmVzdG1peF8yN182Iiwib2Rvb1VzZXJuYW1lIjoidHVhbi5sZSIsInNlc3Npb25JZCI6InNlc3Npb25fMTc1MzUxNDEwODk2MF81cWFnczI2cXkiLCJpYXQiOjE3NTM1MTQxMDgsImV4cCI6MTc1MzYwMDUwOCwiYXVkIjoib2Rvby1jbGllbnQiLCJpc3MiOiJ1bml2ZXJzYWwtb2Rvby1hZGFwdGVyIn0.U0-7un8Veo4f7Jxb1h-z6u1xUb2bfFg5h1FKu3JJx3Q',
            expiresIn: '24h',
            version: {
                major: 18,
                minor: 0,
                patch: 0,
                series: '18.0',
                edition: 'enterprise',
                serverVersion: '18.0+e',
                protocolVersion: 1
            },
            capabilities: {
                hasJsonRpc: true,
                hasRestApi: false,
                hasGraphQL: false,
                hasWebSocket: true,
                hasTokenAuth: true,
                hasOAuth2: true,
                maxBatchSize: 1000,
                supportedAuthMethods: ['password', 'api_key', 'oauth2', 'token']
            }
        }
    }),
    __metadata("design:type", Object)
], SwaggerConnectResponseExample.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Connected to Odoo successfully' }),
    __metadata("design:type", String)
], SwaggerConnectResponseExample.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 200 }),
    __metadata("design:type", Number)
], SwaggerConnectResponseExample.prototype, "statusCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'v1' }),
    __metadata("design:type", String)
], SwaggerConnectResponseExample.prototype, "apiVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2025-07-26T07:23:53.901Z' }),
    __metadata("design:type", String)
], SwaggerConnectResponseExample.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '/api/v1/odoo/connect' }),
    __metadata("design:type", String)
], SwaggerConnectResponseExample.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: {
            connectionTime: 995,
            odooVersion: '18.0',
            protocol: 'JSON-RPC'
        }
    }),
    __metadata("design:type", Object)
], SwaggerConnectResponseExample.prototype, "meta", void 0);
class SwaggerSearchResponseExample {
    success;
    data;
    message;
    statusCode;
    apiVersion;
    timestamp;
    path;
    meta;
}
exports.SwaggerSearchResponseExample = SwaggerSearchResponseExample;
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    __metadata("design:type", Boolean)
], SwaggerSearchResponseExample.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: [
            { id: 49, name: 'Admin', login: 'admin01' },
            { id: 2, name: 'Administrator', login: 'admin' },
            { id: 26, name: 'Bùi Doãn Quang', login: 'doanquang' }
        ]
    }),
    __metadata("design:type", Array)
], SwaggerSearchResponseExample.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Search completed successfully' }),
    __metadata("design:type", String)
], SwaggerSearchResponseExample.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 200 }),
    __metadata("design:type", Number)
], SwaggerSearchResponseExample.prototype, "statusCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'v1' }),
    __metadata("design:type", String)
], SwaggerSearchResponseExample.prototype, "apiVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2025-07-26T07:25:06.791Z' }),
    __metadata("design:type", String)
], SwaggerSearchResponseExample.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '/api/v1/odoo/res.users/search' }),
    __metadata("design:type", String)
], SwaggerSearchResponseExample.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: { duration: 234, recordCount: 3 }
    }),
    __metadata("design:type", Object)
], SwaggerSearchResponseExample.prototype, "meta", void 0);
//# sourceMappingURL=swagger-examples.dto.js.map