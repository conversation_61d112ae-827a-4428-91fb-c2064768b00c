import { SuccessResponse, ApiErrorResponse, PaginationMeta, ValidationError } from '../interfaces/api-response.interface';
export declare class SwaggerSuccessResponseExample implements SuccessResponse {
    success: true;
    data: any;
    message: string;
    statusCode: number;
    apiVersion: string;
    timestamp: string;
    path?: string;
    meta?: Record<string, any>;
}
export declare class SwaggerErrorResponseExample implements ApiErrorResponse {
    success: false;
    error: string;
    message: string;
    statusCode: number;
    apiVersion: string;
    timestamp: string;
    path?: string;
    errorCode?: string;
    validationErrors?: ValidationError[];
}
export declare class SwaggerPaginationExample implements PaginationMeta {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextPage?: number;
    prevPage?: number;
}
export declare class SwaggerConnectResponseExample {
    success: boolean;
    data: any;
    message: string;
    statusCode: number;
    apiVersion: string;
    timestamp: string;
    path: string;
    meta: any;
}
export declare class SwaggerSearchResponseExample {
    success: boolean;
    data: any[];
    message: string;
    statusCode: number;
    apiVersion: string;
    timestamp: string;
    path: string;
    meta: any;
}
