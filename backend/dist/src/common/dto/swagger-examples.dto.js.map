{"version": 3, "file": "swagger-examples.dto.js", "sourceRoot": "", "sources": ["../../../../src/common/dto/swagger-examples.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAc9C,MAAa,6BAA6B;IAKxC,OAAO,CAAO;IAMd,IAAI,CAAM;IAMV,OAAO,CAAS;IAMhB,UAAU,CAAS;IAMnB,UAAU,CAAS;IAMnB,SAAS,CAAS;IAOlB,IAAI,CAAU;IAOd,IAAI,CAAuB;CAC5B;AAlDD,sEAkDC;AA7CC;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,yCAAyC;KACvD,CAAC;;8DACY;AAMd;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,kBAAkB,EAAE;QAC/D,WAAW,EAAE,0BAA0B;KACxC,CAAC;;2DACQ;AAMV;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,gCAAgC;QACzC,WAAW,EAAE,gCAAgC;KAC9C,CAAC;;8DACc;AAMhB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,GAAG;QACZ,WAAW,EAAE,kBAAkB;KAChC,CAAC;;iEACiB;AAMnB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,kBAAkB;KAChC,CAAC;;iEACiB;AAMnB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,0BAA0B;QACnC,WAAW,EAAE,+BAA+B;KAC7C,CAAC;;gEACgB;AAOlB;IALC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,+BAA+B;QACxC,WAAW,EAAE,cAAc;QAC3B,QAAQ,EAAE,KAAK;KAChB,CAAC;;2DACY;AAOd;IALC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE;QAC3C,WAAW,EAAE,uCAAuC;QACpD,QAAQ,EAAE,KAAK;KAChB,CAAC;;2DACyB;AAI7B,MAAa,2BAA2B;IAKtC,OAAO,CAAQ;IAMf,KAAK,CAAS;IAMd,OAAO,CAAS;IAMhB,UAAU,CAAS;IAMnB,UAAU,CAAS;IAMnB,SAAS,CAAS;IAOlB,IAAI,CAAU;IAOd,SAAS,CAAU;IAenB,gBAAgB,CAAqB;CACtC;AAjED,kEAiEC;AA5DC;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,8BAA8B;KAC5C,CAAC;;4DACa;AAMf;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,qBAAqB;KACnC,CAAC;;0DACY;AAMd;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,8DAA8D;QACvE,WAAW,EAAE,8BAA8B;KAC5C,CAAC;;4DACc;AAMhB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,GAAG;QACZ,WAAW,EAAE,kBAAkB;KAChC,CAAC;;+DACiB;AAMnB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,kBAAkB;KAChC,CAAC;;+DACiB;AAMnB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,0BAA0B;QACnC,WAAW,EAAE,+BAA+B;KAC7C,CAAC;;8DACgB;AAOlB;IALC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,+BAA+B;QACxC,WAAW,EAAE,cAAc;QAC3B,QAAQ,EAAE,KAAK;KAChB,CAAC;;yDACY;AAOd;IALC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,cAAc;QACvB,WAAW,EAAE,6BAA6B;QAC1C,QAAQ,EAAE,KAAK;KAChB,CAAC;;8DACiB;AAenB;IAbC,IAAA,qBAAW,EAAC;QACX,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,OAAO,EAAE;YACP;gBACE,KAAK,EAAE,OAAO;gBACd,OAAO,EAAE,qCAAqC;gBAC9C,KAAK,EAAE,eAAe;gBACtB,IAAI,EAAE,SAAS;aAChB;SACF;QACD,WAAW,EAAE,qCAAqC;QAClD,QAAQ,EAAE,KAAK;KAChB,CAAC;;qEACmC;AAIvC,MAAa,wBAAwB;IAKnC,IAAI,CAAS;IAMb,KAAK,CAAS;IAMd,KAAK,CAAS;IAMd,UAAU,CAAS;IAMnB,OAAO,CAAU;IAMjB,OAAO,CAAU;IAOjB,QAAQ,CAAU;IAOlB,QAAQ,CAAU;CACnB;AAlDD,4DAkDC;AA7CC;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,CAAC;QACV,WAAW,EAAE,+BAA+B;KAC7C,CAAC;;sDACW;AAMb;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,EAAE;QACX,WAAW,EAAE,0BAA0B;KACxC,CAAC;;uDACY;AAMd;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,GAAG;QACZ,WAAW,EAAE,uBAAuB;KACrC,CAAC;;uDACY;AAMd;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,EAAE;QACX,WAAW,EAAE,uBAAuB;KACrC,CAAC;;4DACiB;AAMnB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,8BAA8B;KAC5C,CAAC;;yDACe;AAMjB;IAJC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,kCAAkC;KAChD,CAAC;;yDACe;AAOjB;IALC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,CAAC;QACV,WAAW,EAAE,8BAA8B;QAC3C,QAAQ,EAAE,KAAK;KAChB,CAAC;;0DACgB;AAOlB;IALC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,kCAAkC;QAC/C,QAAQ,EAAE,KAAK;KAChB,CAAC;;0DACgB;AAIpB,MAAa,6BAA6B;IAExC,OAAO,CAAU;IA2BjB,IAAI,CAAM;IAGV,OAAO,CAAS;IAGhB,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,SAAS,CAAS;IAGlB,IAAI,CAAS;IASb,IAAI,CAAM;CACX;AAtDD,sEAsDC;AApDC;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;8DACd;AA2BjB;IAzBC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE;YACP,KAAK,EAAE,kdAAkd;YACzd,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE;gBACP,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,YAAY;gBACrB,aAAa,EAAE,QAAQ;gBACvB,eAAe,EAAE,CAAC;aACnB;YACD,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,KAAK;gBACjB,UAAU,EAAE,KAAK;gBACjB,YAAY,EAAE,IAAI;gBAClB,YAAY,EAAE,IAAI;gBAClB,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,IAAI;gBAClB,oBAAoB,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC;aACjE;SACF;KACF,CAAC;;2DACQ;AAGV;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;;8DAC3C;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;iEACX;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iEACZ;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;gEACnC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;;2DACpC;AASb;IAPC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE;YACP,cAAc,EAAE,GAAG;YACnB,WAAW,EAAE,MAAM;YACnB,QAAQ,EAAE,UAAU;SACrB;KACF,CAAC;;2DACQ;AAIZ,MAAa,4BAA4B;IAEvC,OAAO,CAAU;IASjB,IAAI,CAAQ;IAGZ,OAAO,CAAS;IAGhB,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,SAAS,CAAS;IAGlB,IAAI,CAAS;IAKb,IAAI,CAAM;CACX;AAhCD,oEAgCC;AA9BC;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;6DACd;AASjB;IAPC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE;YACP,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;YAC3C,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;YAChD,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW,EAAE;SACvD;KACF,CAAC;;0DACU;AAGZ;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;;6DAC1C;AAGhB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;;gEACX;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;gEACZ;AAGnB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;;+DACnC;AAGlB;IADC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;;0DAC7C;AAKb;IAHC,IAAA,qBAAW,EAAC;QACX,OAAO,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE;KAC3C,CAAC;;0DACQ"}