"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseBuilderService = void 0;
const common_1 = require("@nestjs/common");
let ResponseBuilderService = class ResponseBuilderService {
    apiVersion = 'v1';
    success(data, message = 'Request successful', statusCode = 200, request, meta) {
        return {
            success: true,
            data,
            message,
            statusCode,
            apiVersion: this.apiVersion,
            timestamp: new Date().toISOString(),
            path: request?.url,
            meta,
        };
    }
    list(data, pagination, message = 'List retrieved successfully', request, meta) {
        return {
            success: true,
            data,
            message,
            statusCode: 200,
            apiVersion: this.apiVersion,
            timestamp: new Date().toISOString(),
            path: request?.url,
            pagination,
            meta,
        };
    }
    created(data, message = 'Resource created successfully', request, meta) {
        return {
            success: true,
            data,
            message,
            statusCode: 201,
            apiVersion: this.apiVersion,
            timestamp: new Date().toISOString(),
            path: request?.url,
            meta,
        };
    }
    updated(data, message = 'Resource updated successfully', request, meta) {
        return {
            success: true,
            data,
            message,
            statusCode: 200,
            apiVersion: this.apiVersion,
            timestamp: new Date().toISOString(),
            path: request?.url,
            meta,
        };
    }
    deleted(message = 'Resource deleted successfully', request, meta) {
        return {
            success: true,
            data: null,
            message,
            statusCode: 200,
            apiVersion: this.apiVersion,
            timestamp: new Date().toISOString(),
            path: request?.url,
            meta,
        };
    }
    error(error, message, statusCode = 500, request, errorCode, validationErrors, additionalData) {
        const response = {
            success: false,
            error,
            message,
            statusCode,
            apiVersion: this.apiVersion,
            timestamp: new Date().toISOString(),
            path: request?.url,
            errorCode,
            validationErrors,
        };
        if (process.env.NODE_ENV === 'development' && request) {
            response.debug = this.buildDebugInfo(request, additionalData);
        }
        return response;
    }
    validationError(validationErrors, message = 'Validation failed', request) {
        return this.error('Validation Error', message, 400, request, 'VALIDATION_FAILED', validationErrors);
    }
    unauthorized(message = 'Authentication required', request, errorCode = 'UNAUTHORIZED') {
        return this.error('Unauthorized', message, 401, request, errorCode);
    }
    forbidden(message = 'Access denied', request, errorCode = 'FORBIDDEN') {
        return this.error('Forbidden', message, 403, request, errorCode);
    }
    notFound(message = 'Resource not found', request, errorCode = 'NOT_FOUND') {
        return this.error('Not Found', message, 404, request, errorCode);
    }
    conflict(message = 'Resource conflict', request, errorCode = 'CONFLICT') {
        return this.error('Conflict', message, 409, request, errorCode);
    }
    internalError(message = 'Internal server error', request, errorCode = 'INTERNAL_ERROR', additionalData) {
        return this.error('Internal Server Error', message, 500, request, errorCode, undefined, additionalData);
    }
    buildPagination(page, limit, total) {
        const totalPages = Math.ceil(total / limit);
        const hasNext = page < totalPages;
        const hasPrev = page > 1;
        return {
            page,
            limit,
            total,
            totalPages,
            hasNext,
            hasPrev,
            nextPage: hasNext ? page + 1 : undefined,
            prevPage: hasPrev ? page - 1 : undefined,
        };
    }
    buildDebugInfo(request, additionalData) {
        return {
            requestBody: request.body,
            requestHeaders: this.sanitizeHeaders(request.headers),
            userAgent: request.headers['user-agent'],
            ip: request.ip || request.connection?.remoteAddress,
            requestId: request.headers['x-request-id'],
            additional: additionalData,
        };
    }
    sanitizeHeaders(headers) {
        const sanitized = { ...headers };
        delete sanitized.authorization;
        delete sanitized.cookie;
        delete sanitized['x-api-key'];
        delete sanitized['x-auth-token'];
        return sanitized;
    }
};
exports.ResponseBuilderService = ResponseBuilderService;
exports.ResponseBuilderService = ResponseBuilderService = __decorate([
    (0, common_1.Injectable)()
], ResponseBuilderService);
//# sourceMappingURL=response-builder.service.js.map