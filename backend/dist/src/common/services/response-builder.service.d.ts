import { Request } from 'express';
import { ApiErrorResponse, SuccessResponse, ListResponse, CreatedResponse, UpdatedResponse, DeletedResponse, PaginationMeta, ValidationError } from '../interfaces/api-response.interface';
export declare class ResponseBuilderService {
    private readonly apiVersion;
    success<T>(data: T, message?: string, statusCode?: number, request?: Request, meta?: Record<string, any>): SuccessResponse<T>;
    list<T>(data: T[], pagination: PaginationMeta, message?: string, request?: Request, meta?: Record<string, any>): ListResponse<T>;
    created<T>(data: T, message?: string, request?: Request, meta?: Record<string, any>): CreatedResponse<T>;
    updated<T>(data: T, message?: string, request?: Request, meta?: Record<string, any>): UpdatedResponse<T>;
    deleted(message?: string, request?: Request, meta?: Record<string, any>): DeletedResponse;
    error(error: string, message: string, statusCode?: number, request?: Request, errorCode?: string, validationErrors?: ValidationError[], additionalData?: Record<string, any>): ApiErrorResponse;
    validationError(validationErrors: ValidationError[], message?: string, request?: Request): ApiErrorResponse;
    unauthorized(message?: string, request?: Request, errorCode?: string): ApiErrorResponse;
    forbidden(message?: string, request?: Request, errorCode?: string): ApiErrorResponse;
    notFound(message?: string, request?: Request, errorCode?: string): ApiErrorResponse;
    conflict(message?: string, request?: Request, errorCode?: string): ApiErrorResponse;
    internalError(message?: string, request?: Request, errorCode?: string, additionalData?: Record<string, any>): ApiErrorResponse;
    buildPagination(page: number, limit: number, total: number): PaginationMeta;
    private buildDebugInfo;
    private sanitizeHeaders;
}
