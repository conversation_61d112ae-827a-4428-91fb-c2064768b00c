{"version": 3, "file": "response-builder.service.js", "sourceRoot": "", "sources": ["../../../../src/common/services/response-builder.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAgBrC,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAChB,UAAU,GAAG,IAAI,CAAC;IAKnC,OAAO,CACL,IAAO,EACP,UAAkB,oBAAoB,EACtC,aAAqB,GAAG,EACxB,OAAiB,EACjB,IAA0B;QAE1B,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,UAAU;YACV,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,GAAG;YAClB,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,IAAI,CACF,IAAS,EACT,UAA0B,EAC1B,UAAkB,6BAA6B,EAC/C,OAAiB,EACjB,IAA0B;QAE1B,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,GAAG;YAClB,UAAU;YACV,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,OAAO,CACL,IAAO,EACP,UAAkB,+BAA+B,EACjD,OAAiB,EACjB,IAA0B;QAE1B,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,GAAG;YAClB,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,OAAO,CACL,IAAO,EACP,UAAkB,+BAA+B,EACjD,OAAiB,EACjB,IAA0B;QAE1B,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI;YACJ,OAAO;YACP,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,GAAG;YAClB,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,OAAO,CACL,UAAkB,+BAA+B,EACjD,OAAiB,EACjB,IAA0B;QAE1B,OAAO;YACL,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;YACV,OAAO;YACP,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,GAAG;YAClB,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,KAAK,CACH,KAAa,EACb,OAAe,EACf,aAAqB,GAAG,EACxB,OAAiB,EACjB,SAAkB,EAClB,gBAAoC,EACpC,cAAoC;QAEpC,MAAM,QAAQ,GAAqB;YACjC,OAAO,EAAE,KAAK;YACd,KAAK;YACL,OAAO;YACP,UAAU;YACV,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,OAAO,EAAE,GAAG;YAClB,SAAS;YACT,gBAAgB;SACjB,CAAC;QAGF,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,EAAE,CAAC;YACtD,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,eAAe,CACb,gBAAmC,EACnC,UAAkB,mBAAmB,EACrC,OAAiB;QAEjB,OAAO,IAAI,CAAC,KAAK,CACf,kBAAkB,EAClB,OAAO,EACP,GAAG,EACH,OAAO,EACP,mBAAmB,EACnB,gBAAgB,CACjB,CAAC;IACJ,CAAC;IAKD,YAAY,CACV,UAAkB,yBAAyB,EAC3C,OAAiB,EACjB,YAAoB,cAAc;QAElC,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IACtE,CAAC;IAKD,SAAS,CACP,UAAkB,eAAe,EACjC,OAAiB,EACjB,YAAoB,WAAW;QAE/B,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IACnE,CAAC;IAKD,QAAQ,CACN,UAAkB,oBAAoB,EACtC,OAAiB,EACjB,YAAoB,WAAW;QAE/B,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IACnE,CAAC;IAKD,QAAQ,CACN,UAAkB,mBAAmB,EACrC,OAAiB,EACjB,YAAoB,UAAU;QAE9B,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAClE,CAAC;IAKD,aAAa,CACX,UAAkB,uBAAuB,EACzC,OAAiB,EACjB,YAAoB,gBAAgB,EACpC,cAAoC;QAEpC,OAAO,IAAI,CAAC,KAAK,CACf,uBAAuB,EACvB,OAAO,EACP,GAAG,EACH,OAAO,EACP,SAAS,EACT,SAAS,EACT,cAAc,CACf,CAAC;IACJ,CAAC;IAKD,eAAe,CACb,IAAY,EACZ,KAAa,EACb,KAAa;QAEb,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,IAAI,GAAG,UAAU,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;QAEzB,OAAO;YACL,IAAI;YACJ,KAAK;YACL,KAAK;YACL,UAAU;YACV,OAAO;YACP,OAAO;YACP,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;YACxC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS;SACzC,CAAC;IACJ,CAAC;IAKO,cAAc,CACpB,OAAgB,EAChB,cAAoC;QAEpC,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,IAAI;YACzB,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC;YACrD,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;YACxC,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE,aAAa;YACnD,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAW;YACpD,UAAU,EAAE,cAAc;SAC3B,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,OAAY;QAClC,MAAM,SAAS,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;QAGjC,OAAO,SAAS,CAAC,aAAa,CAAC;QAC/B,OAAO,SAAS,CAAC,MAAM,CAAC;QACxB,OAAO,SAAS,CAAC,WAAW,CAAC,CAAC;QAC9B,OAAO,SAAS,CAAC,cAAc,CAAC,CAAC;QAEjC,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AAvRY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;GACA,sBAAsB,CAuRlC"}