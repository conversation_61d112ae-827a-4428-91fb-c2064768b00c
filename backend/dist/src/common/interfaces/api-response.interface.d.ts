export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message: string;
    statusCode: number;
    apiVersion: string;
    timestamp: string;
    path?: string;
    pagination?: PaginationMeta;
    meta?: Record<string, any>;
}
export interface ApiErrorResponse extends Omit<ApiResponse, 'data'> {
    error: string;
    errorCode?: string;
    validationErrors?: ValidationError[];
    debug?: DebugInfo;
}
export interface PaginationMeta {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextPage?: number;
    prevPage?: number;
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
    rule?: string;
}
export interface DebugInfo {
    stack?: string;
    requestBody?: any;
    requestHeaders?: Record<string, string>;
    userAgent?: string;
    ip?: string;
    requestId?: string;
    additional?: Record<string, any>;
}
export interface SuccessResponse<T = any> extends ApiResponse<T> {
    success: true;
    data: T;
}
export interface ListResponse<T = any> extends SuccessResponse<T[]> {
    pagination: PaginationMeta;
}
export interface CreatedResponse<T = any> extends SuccessResponse<T> {
    statusCode: 201;
    message: string;
}
export interface UpdatedResponse<T = any> extends SuccessResponse<T> {
    statusCode: 200;
    message: string;
}
export interface DeletedResponse extends SuccessResponse<null> {
    statusCode: 200;
    data: null;
    message: string;
}
export interface BadRequestResponse extends ApiErrorResponse {
    statusCode: 400;
    error: 'Bad Request';
}
export interface UnauthorizedResponse extends ApiErrorResponse {
    statusCode: 401;
    error: 'Unauthorized';
}
export interface ForbiddenResponse extends ApiErrorResponse {
    statusCode: 403;
    error: 'Forbidden';
}
export interface NotFoundResponse extends ApiErrorResponse {
    statusCode: 404;
    error: 'Not Found';
}
export interface ConflictResponse extends ApiErrorResponse {
    statusCode: 409;
    error: 'Conflict';
}
export interface ValidationErrorResponse extends BadRequestResponse {
    validationErrors: ValidationError[];
}
export interface InternalServerErrorResponse extends ApiErrorResponse {
    statusCode: 500;
    error: 'Internal Server Error';
}
