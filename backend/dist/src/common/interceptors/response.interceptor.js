"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ResponseInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const response_builder_service_1 = require("../services/response-builder.service");
let ResponseInterceptor = ResponseInterceptor_1 = class ResponseInterceptor {
    responseBuilder;
    logger = new common_1.Logger(ResponseInterceptor_1.name);
    constructor(responseBuilder) {
        this.responseBuilder = responseBuilder;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const startTime = Date.now();
        return next.handle().pipe((0, operators_1.map)((data) => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            this.logger.log(`${request.method} ${request.url} - ${response.statusCode} - ${duration}ms`);
            if (this.isStandardizedResponse(data)) {
                return data;
            }
            if (data === null || data === undefined) {
                return this.responseBuilder.success(null, 'Request completed successfully', response.statusCode, request, { duration });
            }
            return this.responseBuilder.success(data, 'Request completed successfully', response.statusCode, request, { duration });
        }));
    }
    isStandardizedResponse(data) {
        return (data &&
            typeof data === 'object' &&
            typeof data.success === 'boolean' &&
            typeof data.message === 'string' &&
            typeof data.statusCode === 'number' &&
            typeof data.apiVersion === 'string' &&
            typeof data.timestamp === 'string');
    }
};
exports.ResponseInterceptor = ResponseInterceptor;
exports.ResponseInterceptor = ResponseInterceptor = ResponseInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [response_builder_service_1.ResponseBuilderService])
], ResponseInterceptor);
//# sourceMappingURL=response.interceptor.js.map