import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ResponseBuilderService } from '../services/response-builder.service';
export declare class ResponseInterceptor implements NestInterceptor {
    private readonly responseBuilder;
    private readonly logger;
    constructor(responseBuilder: ResponseBuilderService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private isStandardizedResponse;
}
