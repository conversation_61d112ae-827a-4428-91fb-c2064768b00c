{"version": 3, "file": "odoo-connection.use-case.js", "sourceRoot": "", "sources": ["../../../../src/application/use-cases/odoo-connection.use-case.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AAKpD,kHAA4G;AAC5G,kGAA6F;AAC7F,4GAAsG;AAG/F,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAIb;IACA;IACA;IALF,MAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;IAEjE,YACmB,qBAAgD,EAChD,kBAAsC,EACtC,sBAA8C;QAF9C,0BAAqB,GAArB,qBAAqB,CAA2B;QAChD,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC9D,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,MAA4B,EAAE,cAAuB;QACjE,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,EAAE,CAAC;YAGpE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;gBAC9D,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,cAAc;gBACd,UAAU,EAAE,MAAM;aACnB,CAAC,CAAC;YAGH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAGtF,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;YAGxD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAC5D,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CACzC,CAAC;YAGF,MAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;YAErF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,MAAM,CAAC,IAAI,kBAAkB,OAAO,CAAC,EAAE,eAAe,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC;QACvI,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,OAAO,CAAC,cAAc,EAAE,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,OAAO,CAAC,eAAe,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CACd,KAAa,EACb,MAAc,EACd,OAA2B;QAE3B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,UAAU,CAAI,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAU,KAAa,EAAE,MAAkB;QACrD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CACV,KAAa,EACb,GAAa,EACb,MAAkB;QAElB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,KAAa,EAAE,GAAa;QACvC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CACX,KAAa,EACb,MAAc,EACd,IAAW,EACX,MAAY;QAEZ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,KAAK,IAAI,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC7C,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAC/C,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CACzC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACzF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,YAAY;QACV,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,CAAC;IACnD,CAAC;IAKD,cAAc;QACZ,OAAO,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC;IACrD,CAAC;IAKD,KAAK,CAAC,WAAW;QACf,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;IACxD,CAAC;IAKD,KAAK,CAAC,YAAY,CAChB,UAGE;QAEF,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;IAC7E,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,gBAAwB,EAAE;QACtD,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;IACjF,CAAC;IAKD,KAAK,CAAC,0BAA0B,CAAC,0BAAkC,EAAE;QACnE,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,uBAAuB,CAAC,CAAC;IAC9F,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAG9C,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAGjE,MAAM,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAEnE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB;QAQxB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAE/E,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC9B,UAAU,EAAE,OAAO,CAAC,cAAc;gBAClC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI;gBAC7B,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ;gBACrC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ;gBACrC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QAC7C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAG9C,MAAM,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAGhF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAC7F,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACtF,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;gBACxD,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,CAAC;YAC9F,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,cAAc,kBAAkB,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACnG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,UAAU,CAAC,cAAuB;QAC9C,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC;YAE9C,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAG9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YAE7F,IAAI,OAAO,EAAE,CAAC;gBAEZ,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACtF,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;gBAGxD,MAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;gBAErF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,OAAO,CAAC,EAAE,eAAe,OAAO,CAAC,cAAc,EAAE,CAAC,CAAC;YAC9G,CAAC;iBAAM,CAAC;gBAEN,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACrF,MAAM,SAAS,GAAG;oBAChB,eAAe,EAAE,OAAO,CAAC,EAAE;oBAC3B,mBAAmB,EAAE,cAAc;oBACnC,iBAAiB,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBACvC,UAAU,EAAE,CAAC,CAAC,cAAc;wBAC5B,IAAI,EAAE,CAAC,CAAC,QAAQ;wBAChB,QAAQ,EAAE,CAAC,CAAC,YAAY;wBACxB,QAAQ,EAAE,CAAC,CAAC,QAAQ;wBACpB,QAAQ,EAAE,CAAC,CAAC,QAAQ;qBACrB,CAAC,CAAC;oBACH,aAAa,EAAE,WAAW,CAAC,MAAM;iBAClC,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,SAAS,CAAC,CAAC;gBAEvE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,qCAAqC,OAAO,CAAC,EAAE,gCAAgC,CAAC,CAAC;gBACxG,KAAa,CAAC,SAAS,GAAG,SAAS,CAAC;gBACrC,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,CACnD,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CACzC,CAAC;IACJ,CAAC;IAKO,sBAAsB;QAE5B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAGnD,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAW,CAAC;YACjC,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY;aAC7C,CAAC;QACJ,CAAC;QAGD,IAAI,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC7C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;YACzC,OAAO;gBACL,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,QAAQ,EAAE,WAAW,CAAC,QAAQ;aAC/B,CAAC;QACJ,CAAC;QAGD,MAAM,UAAU,GAAG,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC;QACnD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtC,OAAO;oBACL,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;oBACjC,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC3B,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;YAEjB,CAAC;QACH,CAAC;QAGD,MAAM,MAAM,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC,WAAW,CAAW,CAAC;QACzD,MAAM,QAAQ,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC,YAAY,CAAW,CAAC;QAE5D,IAAI,MAAM,IAAI,QAAQ,EAAE,CAAC;YACvB,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAClC,CAAC;QAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,MAAM,EAAE,GAAG,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,UAAU,EAAE,aAAa,IAAI,SAAS,CAAC;YAC1E,MAAM,SAAS,GAAG,OAAO,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;YAGhE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,MAAM,WAAW,GAAG,QAAQ,IAAI,EAAE,CAAC;YAEnC,OAAO,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;QACpD,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;IAKO,SAAS,CAAC,KAAa;QAC7B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;CACF,CAAA;AAhYY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;qCAK+B,wDAAyB;QAC5B,yCAAkB;QACd,kDAAsB;GANtD,qBAAqB,CAgYjC"}