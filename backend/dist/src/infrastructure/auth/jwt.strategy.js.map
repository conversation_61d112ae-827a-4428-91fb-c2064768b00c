{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../../src/infrastructure/auth/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,+CAA8E;AAGvE,IAAM,WAAW,mBAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAItC;IACA;IAJF,MAAM,GAAG,IAAI,eAAM,CAAC,aAAW,CAAC,IAAI,CAAC,CAAC;IAEvD,YACmB,aAA4B,EAC5B,cAA8B;QAE/C,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,yCAAyC;SAClG,CAAC,CAAC;QAPc,kBAAa,GAAb,aAAa,CAAe;QAC5B,mBAAc,GAAd,cAAc,CAAgB;QAS/C,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,CAAC;QACvD,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,yCAAyC,EAAE,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAMD,KAAK,CAAC,QAAQ,CAAC,OAAmB;QAChC,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAEtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,IAAI,CAAC,EAAE,cAAc,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YAEpG,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;CACF,CAAA;AArCY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAKuB,sBAAa;QACZ,4BAAc;GALtC,WAAW,CAqCvB"}