"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtAuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtAuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
let JwtAuthService = JwtAuthService_1 = class JwtAuthService {
    jwtService;
    configService;
    logger = new common_1.Logger(JwtAuthService_1.name);
    constructor(jwtService, configService) {
        this.jwtService = jwtService;
        this.configService = configService;
    }
    async generateToken(payload) {
        try {
            const expiresIn = this.configService.get('JWT_EXPIRATION') || '24h';
            const token = await this.jwtService.signAsync(payload, {
                expiresIn,
            });
            this.logger.debug(`Generated JWT token for user ${payload.sub}, instance ${payload.odooInstanceId}`);
            return {
                token,
                expiresIn,
            };
        }
        catch (error) {
            this.logger.error('Failed to generate JWT token', error);
            throw new Error('Token generation failed');
        }
    }
    async verifyToken(token) {
        try {
            const payload = await this.jwtService.verifyAsync(token);
            this.logger.debug(`Verified JWT token for user ${payload.sub}`);
            return payload;
        }
        catch (error) {
            this.logger.error('JWT token verification failed', error);
            throw new Error('Invalid or expired token');
        }
    }
    extractUser(payload) {
        return {
            id: payload.sub,
            odooInstanceId: payload.odooInstanceId,
            odooHost: payload.odooHost,
            odooDatabase: payload.odooDatabase,
            odooUsername: payload.odooUsername,
            sessionId: payload.sessionId,
        };
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    isTokenExpired(token) {
        try {
            const payload = this.jwtService.decode(token);
            if (!payload || !payload.exp) {
                return true;
            }
            return Date.now() >= payload.exp * 1000;
        }
        catch (error) {
            return true;
        }
    }
    getTokenExpiration(token) {
        try {
            const payload = this.jwtService.decode(token);
            if (!payload || !payload.exp) {
                return null;
            }
            return new Date(payload.exp * 1000);
        }
        catch (error) {
            return null;
        }
    }
    async refreshToken(oldToken) {
        try {
            const payload = await this.verifyToken(oldToken);
            const { iat, exp, ...tokenPayload } = payload;
            return await this.generateToken(tokenPayload);
        }
        catch (error) {
            this.logger.error('Failed to refresh token', error);
            throw new Error('Token refresh failed');
        }
    }
};
exports.JwtAuthService = JwtAuthService;
exports.JwtAuthService = JwtAuthService = JwtAuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService])
], JwtAuthService);
//# sourceMappingURL=jwt.service.js.map