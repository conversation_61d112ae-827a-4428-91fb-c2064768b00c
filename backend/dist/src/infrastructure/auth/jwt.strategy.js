"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const jwt_service_1 = require("./jwt.service");
let JwtStrategy = JwtStrategy_1 = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    configService;
    jwtAuthService;
    logger = new common_1.Logger(JwtStrategy_1.name);
    constructor(configService, jwtAuthService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET') || 'default-dev-secret-change-in-production',
        });
        this.configService = configService;
        this.jwtAuthService = jwtAuthService;
        const secret = configService.get('JWT_SECRET');
        if (!secret || secret === 'default-dev-secret-change-in-production') {
            this.logger.warn('Using default JWT secret. Set JWT_SECRET environment variable in production!');
        }
    }
    async validate(payload) {
        try {
            const user = this.jwtAuthService.extractUser(payload);
            this.logger.debug(`JWT validation successful for user ${user.id}, instance ${user.odooInstanceId}`);
            return user;
        }
        catch (error) {
            this.logger.error('JWT payload validation failed', error);
            throw new Error('Invalid token payload');
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = JwtStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        jwt_service_1.JwtAuthService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map