import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { JwtPayload, AuthenticatedUser, JwtAuthService } from './jwt.service';
declare const JwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class JwtStrategy extends JwtStrategy_base {
    private readonly configService;
    private readonly jwtAuthService;
    private readonly logger;
    constructor(configService: ConfigService, jwtAuthService: JwtAuthService);
    validate(payload: JwtPayload): Promise<AuthenticatedUser>;
}
export {};
