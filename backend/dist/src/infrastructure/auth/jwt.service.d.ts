import { JwtService as NestJwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
export interface JwtPayload {
    sub: string;
    odooInstanceId: string;
    odooHost: string;
    odooDatabase: string;
    odooUsername: string;
    sessionId: string;
    iat?: number;
    exp?: number;
}
export interface AuthenticatedUser {
    id: string;
    odooInstanceId: string;
    odooHost: string;
    odooDatabase: string;
    odooUsername: string;
    sessionId: string;
}
export declare class JwtAuthService {
    private readonly jwtService;
    private readonly configService;
    private readonly logger;
    constructor(jwtService: NestJwtService, configService: ConfigService);
    generateToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): Promise<{
        token: string;
        expiresIn: string;
    }>;
    verifyToken(token: string): Promise<JwtPayload>;
    extractUser(payload: JwtPayload): AuthenticatedUser;
    generateSessionId(): string;
    isTokenExpired(token: string): boolean;
    getTokenExpiration(token: string): Date | null;
    refreshToken(oldToken: string): Promise<{
        token: string;
        expiresIn: string;
    }>;
}
