{"version": 3, "file": "user-odoo-mapping.service.js", "sourceRoot": "", "sources": ["../../../../../src/infrastructure/adapters/odoo/user-odoo-mapping.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAA+C;AAC/C,uCAAiC;AAEjC,uHAAoI;AACpI,iCAAiC;AA0B1B,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAMd;IALF,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IACjD,aAAa,CAAS;IAEvC,YAEmB,oBAAoD;QAApD,yBAAoB,GAApB,oBAAoB,CAAgC;QAGrE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,sCAAsC,CAAC;QAC1F,IAAI,IAAI,CAAC,aAAa,KAAK,sCAAsC,EAAE,CAAC;YAClE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,GAAqB;QACvC,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;YACtC,MAAM,cAAc,GAAG,GAAG,CAAC,cAAc,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAEjF,MAAM,WAAW,GAAG;gBAClB,SAAS;gBACT,cAAc;gBACd,QAAQ,EAAE,UAAU,CAAC,IAAI;gBACzB,YAAY,EAAE,UAAU,CAAC,QAAQ;gBACjC,YAAY,EAAE,UAAU,CAAC,QAAQ;gBACjC,qBAAqB,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAChE,YAAY,EAAE,UAAU,CAAC,QAAQ,IAAI,OAAO;gBAC5C,QAAQ,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzE,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE;gBACpB,eAAe,EAAE,CAAC;aACnB,CAAC;YAGF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAC9D,EAAE,SAAS,EAAE,cAAc,EAAE,EAC7B,WAAW,EACX;gBACE,MAAM,EAAE,IAAI;gBACZ,GAAG,EAAE,IAAI;gBACT,aAAa,EAAE,IAAI;aACpB,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,0BAA0B,SAAS,cAAc,cAAc,EAAE,CAAC,CAAC;YAE3H,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,GAAG,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/E,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,SAAiB;QACrC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB;iBAC7C,IAAI,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;iBACnC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;iBACtB,IAAI,EAAE,CAAC;YAEV,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,cAAuB;QAC7D,IAAI,CAAC;YACH,IAAI,OAAuC,CAAC;YAE5C,IAAI,cAAc,EAAE,CAAC;gBAEnB,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB;qBACtC,OAAO,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;qBACtD,IAAI,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBAEN,OAAO,GAAG,MAAM,IAAI,CAAC,oBAAoB;qBACtC,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;qBACtC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;qBACtB,IAAI,EAAE,CAAC;YACZ,CAAC;YAED,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,SAAS,cAAc,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;YACpG,MAAM,IAAI,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,SAAiB,EAAE,cAAsB;QAC5D,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CACvC,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,EAC7C;gBACE,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE;gBAC9B,IAAI,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;aAC7B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,SAAS,cAAc,cAAc,EAAE,CAAC,CAAC;QAC3F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,SAAS,cAAc,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;QAE3G,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,cAAsB;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CACtD,EAAE,SAAS,EAAE,cAAc,EAAE,EAC7B,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAC9B,CAAC;YAEF,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,SAAS,cAAc,cAAc,EAAE,CAAC,CAAC;gBAC9F,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,SAAS,cAAc,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;YAC3G,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,SAAiB;QACxC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CACvD,EAAE,SAAS,EAAE,EACb,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAC9B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,MAAM,CAAC,aAAa,2BAA2B,SAAS,EAAE,CAAC,CAAC;QAC7F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,OAA2B;QAClD,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI;YAC7B,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ;YACrC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ;YACrC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC3D,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ;YACrC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI;SAC9B,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,cAAsB;QACnE,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CACvC,EAAE,SAAS,EAAE,cAAc,EAAE,EAC7B;gBACE,IAAI,EAAE;oBACJ,wBAAwB,EAAE,IAAI,IAAI,EAAE;oBACpC,kBAAkB,EAAE,CAAC;iBACtB;gBACD,MAAM,EAAE,EAAE,mBAAmB,EAAE,CAAC,EAAE;aACnC,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,SAAS,cAAc,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,cAAsB,EAAE,YAAoB;QACzF,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CACvC,EAAE,SAAS,EAAE,cAAc,EAAE,EAC7B;gBACE,IAAI,EAAE,EAAE,mBAAmB,EAAE,YAAY,EAAE;gBAC3C,IAAI,EAAE,EAAE,kBAAkB,EAAE,CAAC,EAAE;aAChC,CACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,SAAS,cAAc,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,MAA4B;QACrD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;aAC7E,QAAQ,CAAC,QAAQ,CAAC;aAClB,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;aAC5B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpB,OAAO,QAAQ,IAAI,EAAE,CAAC;IACxB,CAAC;IAKO,eAAe,CAAC,QAAgB;QACtC,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YAClC,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YAE7D,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACvD,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAEjC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YAGpC,OAAO,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAKO,eAAe,CAAC,iBAAyB;QAC/C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAEvB,OAAO,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACpE,CAAC;YAED,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAE3B,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YAC9D,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,aAAa,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YACjE,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAE7B,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC1D,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAEpC,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,QAAQ;QAOZ,IAAI,CAAC;YACH,MAAM,CACJ,aAAa,EACb,cAAc,EACd,WAAW,EACX,UAAU,EACV,SAAS,CACV,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE;gBAC1C,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;gBAC5D,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAC/C,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;oBAClC,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE,EAAE,EAAE;iBACpE,CAAC;gBACF,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;oBAClC,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;oBAC9B,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;oBACpD,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE;oBACxB,EAAE,MAAM,EAAE,EAAE,EAAE;iBACf,CAAC;aACH,CAAC,CAAC;YAEH,OAAO;gBACL,UAAU,EAAE,WAAW,CAAC,MAAM;gBAC9B,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC;gBAC/C,QAAQ,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;aACzE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,GAA4B;QACnD,OAAO;YACL,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,cAAc,EAAE,GAAG,CAAC,cAAc;YAClC,UAAU,EAAE;gBACV,IAAI,EAAE,GAAG,CAAC,QAAQ;gBAClB,QAAQ,EAAE,GAAG,CAAC,YAAY;gBAC1B,QAAQ,EAAE,GAAG,CAAC,YAAY;gBAC1B,QAAQ,EAAE,GAAG,CAAC,qBAAqB;gBACnC,QAAQ,EAAE,GAAG,CAAC,YAAgC;gBAC9C,IAAI,EAAE,GAAG,CAAC,QAAQ;aACnB;YACD,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,SAAS,EAAG,GAAW,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;YAC/C,QAAQ,EAAE,GAAG,CAAC,QAAQ;SACvB,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,SAAiB;QACxC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,oBAAoB;iBACnC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC;iBACnB,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;iBACtB,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7E,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF,CAAA;AAnWY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,sBAAW,EAAC,0CAAe,CAAC,IAAI,CAAC,CAAA;qCACK,gBAAK;GANnC,sBAAsB,CAmWlC"}