"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserOdooMappingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOdooMappingService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_odoo_mapping_schema_1 = require("../../database/schemas/user-odoo-mapping.schema");
const crypto = require("crypto");
let UserOdooMappingService = UserOdooMappingService_1 = class UserOdooMappingService {
    userOdooMappingModel;
    logger = new common_1.Logger(UserOdooMappingService_1.name);
    encryptionKey;
    constructor(userOdooMappingModel) {
        this.userOdooMappingModel = userOdooMappingModel;
        this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-dev-key-change-in-production';
        if (this.encryptionKey === 'default-dev-key-change-in-production') {
            this.logger.warn('Using default encryption key. Set ENCRYPTION_KEY environment variable in production!');
        }
    }
    async createMapping(dto) {
        try {
            const { webUserId, odooConfig } = dto;
            const odooInstanceId = dto.odooInstanceId || this.generateInstanceId(odooConfig);
            const mappingData = {
                webUserId,
                odooInstanceId,
                odooHost: odooConfig.host,
                odooDatabase: odooConfig.database,
                odooUsername: odooConfig.username,
                odooPasswordEncrypted: this.encryptPassword(odooConfig.password),
                odooProtocol: odooConfig.protocol || 'https',
                odooPort: odooConfig.port || (odooConfig.protocol === 'https' ? 443 : 80),
                isActive: true,
                lastUsed: new Date(),
                description: dto.description,
                tags: dto.tags || [],
                totalUsageCount: 0,
            };
            const mapping = await this.userOdooMappingModel.findOneAndUpdate({ webUserId, odooInstanceId }, mappingData, {
                upsert: true,
                new: true,
                runValidators: true,
            });
            this.logger.log(`${mapping.isNew ? 'Created' : 'Updated'} Odoo mapping for user ${webUserId}, instance ${odooInstanceId}`);
            return this.mapDocumentToDto(mapping);
        }
        catch (error) {
            this.logger.error(`Failed to create mapping for user ${dto.webUserId}`, error);
            throw new Error(`Failed to create Odoo mapping: ${error.message}`);
        }
    }
    async getUserMappings(webUserId) {
        try {
            const mappings = await this.userOdooMappingModel
                .find({ webUserId, isActive: true })
                .sort({ lastUsed: -1 })
                .exec();
            return mappings.map(mapping => this.mapDocumentToDto(mapping));
        }
        catch (error) {
            this.logger.error(`Failed to get mappings for user ${webUserId}`, error);
            throw new Error(`Failed to retrieve user mappings: ${error.message}`);
        }
    }
    async getUserMapping(webUserId, odooInstanceId) {
        try {
            let mapping;
            if (odooInstanceId) {
                mapping = await this.userOdooMappingModel
                    .findOne({ webUserId, odooInstanceId, isActive: true })
                    .exec();
            }
            else {
                mapping = await this.userOdooMappingModel
                    .findOne({ webUserId, isActive: true })
                    .sort({ lastUsed: -1 })
                    .exec();
            }
            return mapping ? this.mapDocumentToDto(mapping) : null;
        }
        catch (error) {
            this.logger.error(`Failed to get mapping for user ${webUserId}, instance ${odooInstanceId}`, error);
            throw new Error(`Failed to retrieve user mapping: ${error.message}`);
        }
    }
    async updateLastUsed(webUserId, odooInstanceId) {
        try {
            await this.userOdooMappingModel.updateOne({ webUserId, odooInstanceId, isActive: true }, {
                $set: { lastUsed: new Date() },
                $inc: { totalUsageCount: 1 }
            });
            this.logger.debug(`Updated last used for user ${webUserId}, instance ${odooInstanceId}`);
        }
        catch (error) {
            this.logger.error(`Failed to update last used for user ${webUserId}, instance ${odooInstanceId}`, error);
        }
    }
    async deactivateMapping(webUserId, odooInstanceId) {
        try {
            const result = await this.userOdooMappingModel.updateOne({ webUserId, odooInstanceId }, { $set: { isActive: false } });
            if (result.modifiedCount > 0) {
                this.logger.log(`Deactivated Odoo mapping for user ${webUserId}, instance ${odooInstanceId}`);
                return true;
            }
            return false;
        }
        catch (error) {
            this.logger.error(`Failed to deactivate mapping for user ${webUserId}, instance ${odooInstanceId}`, error);
            throw new Error(`Failed to deactivate mapping: ${error.message}`);
        }
    }
    async removeUserMappings(webUserId) {
        try {
            const result = await this.userOdooMappingModel.updateMany({ webUserId }, { $set: { isActive: false } });
            this.logger.log(`Deactivated ${result.modifiedCount} Odoo mappings for user ${webUserId}`);
        }
        catch (error) {
            this.logger.error(`Failed to remove mappings for user ${webUserId}`, error);
            throw new Error(`Failed to remove user mappings: ${error.message}`);
        }
    }
    async getDecryptedConfig(mapping) {
        return {
            host: mapping.odooConfig.host,
            database: mapping.odooConfig.database,
            username: mapping.odooConfig.username,
            password: this.decryptPassword(mapping.odooConfig.password),
            protocol: mapping.odooConfig.protocol,
            port: mapping.odooConfig.port,
        };
    }
    async markConnectionSuccess(webUserId, odooInstanceId) {
        try {
            await this.userOdooMappingModel.updateOne({ webUserId, odooInstanceId }, {
                $set: {
                    lastSuccessfulConnection: new Date(),
                    connectionAttempts: 0,
                },
                $unset: { lastConnectionError: 1 }
            });
        }
        catch (error) {
            this.logger.error(`Failed to mark connection success for user ${webUserId}, instance ${odooInstanceId}`, error);
        }
    }
    async markConnectionFailure(webUserId, odooInstanceId, errorMessage) {
        try {
            await this.userOdooMappingModel.updateOne({ webUserId, odooInstanceId }, {
                $set: { lastConnectionError: errorMessage },
                $inc: { connectionAttempts: 1 }
            });
        }
        catch (error) {
            this.logger.error(`Failed to mark connection failure for user ${webUserId}, instance ${odooInstanceId}`, error);
        }
    }
    generateInstanceId(config) {
        const hash = Buffer.from(`${config.host}:${config.database}:${config.username}`)
            .toString('base64')
            .replace(/[^a-zA-Z0-9]/g, '')
            .substring(0, 12);
        return `odoo_${hash}`;
    }
    encryptPassword(password) {
        try {
            const iv = crypto.randomBytes(16);
            const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
            const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
            let encrypted = cipher.update(password, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            const authTag = cipher.getAuthTag();
            return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
        }
        catch (error) {
            this.logger.error('Failed to encrypt password', error);
            throw new Error('Password encryption failed');
        }
    }
    decryptPassword(encryptedPassword) {
        try {
            const parts = encryptedPassword.split(':');
            if (parts.length !== 3) {
                return Buffer.from(encryptedPassword, 'base64').toString('utf-8');
            }
            const iv = Buffer.from(parts[0], 'hex');
            const authTag = Buffer.from(parts[1], 'hex');
            const encrypted = parts[2];
            const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
            const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
            decipher.setAuthTag(authTag);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted;
        }
        catch (error) {
            this.logger.error('Failed to decrypt password', error);
            throw new Error('Password decryption failed');
        }
    }
    async getStats() {
        try {
            const [totalMappings, activeMappings, uniqueUsers, usageStats, hostStats] = await Promise.all([
                this.userOdooMappingModel.countDocuments(),
                this.userOdooMappingModel.countDocuments({ isActive: true }),
                this.userOdooMappingModel.distinct('webUserId'),
                this.userOdooMappingModel.aggregate([
                    { $group: { _id: null, totalUsage: { $sum: '$totalUsageCount' } } }
                ]),
                this.userOdooMappingModel.aggregate([
                    { $match: { isActive: true } },
                    { $group: { _id: '$odooHost', count: { $sum: 1 } } },
                    { $sort: { count: -1 } },
                    { $limit: 10 }
                ])
            ]);
            return {
                totalUsers: uniqueUsers.length,
                totalMappings,
                activeMappings,
                totalUsageCount: usageStats[0]?.totalUsage || 0,
                topHosts: hostStats.map(stat => ({ host: stat._id, count: stat.count })),
            };
        }
        catch (error) {
            this.logger.error('Failed to get mapping statistics', error);
            throw new Error(`Failed to retrieve statistics: ${error.message}`);
        }
    }
    mapDocumentToDto(doc) {
        return {
            webUserId: doc.webUserId,
            odooInstanceId: doc.odooInstanceId,
            odooConfig: {
                host: doc.odooHost,
                database: doc.odooDatabase,
                username: doc.odooUsername,
                password: doc.odooPasswordEncrypted,
                protocol: doc.odooProtocol,
                port: doc.odooPort,
            },
            isActive: doc.isActive,
            createdAt: doc.createdAt || new Date(),
            lastUsed: doc.lastUsed,
            description: doc.description,
            tags: doc.tags,
        };
    }
    async getAllUserMappings(webUserId) {
        try {
            return await this.userOdooMappingModel
                .find({ webUserId })
                .sort({ lastUsed: -1 })
                .exec();
        }
        catch (error) {
            this.logger.error(`Failed to get all user mappings for ${webUserId}`, error);
            return [];
        }
    }
};
exports.UserOdooMappingService = UserOdooMappingService;
exports.UserOdooMappingService = UserOdooMappingService = UserOdooMappingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_odoo_mapping_schema_1.UserOdooMapping.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], UserOdooMappingService);
//# sourceMappingURL=user-odoo-mapping.service.js.map