"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OdooV13Adapter = void 0;
const common_1 = require("@nestjs/common");
const base_version_adapter_1 = require("./base-version-adapter");
const odoo_connection_config_1 = require("../../../../shared/domain/value-objects/odoo-connection-config");
let OdooV13Adapter = class OdooV13Adapter extends base_version_adapter_1.BaseVersionAdapter {
    version = '13.0';
    capabilities = {
        hasJsonRpc: true,
        hasRestApi: false,
        hasGraphQL: false,
        hasWebSocket: false,
        hasTokenAuth: false,
        hasOAuth2: false,
        maxBatchSize: 500,
        supportedAuthMethods: [odoo_connection_config_1.AuthMethod.PASSWORD],
    };
    constructor() {
        super();
        this.initializeFieldMappings();
    }
    initializeFieldMappings() {
        this.setFieldMapping('res.partner', {
            deprecated_field_v13: null,
            old_field_name: 'new_field_name',
        });
        this.setFieldMapping('sale.order', {
            confirmation_date: 'date_order',
        });
        this.setFieldMapping('account.invoice', {});
    }
    mapDomain(domain) {
        return domain.map((clause) => {
            if (Array.isArray(clause) && clause.length === 3) {
                const [field, operator, value] = clause;
                if (operator === 'ilike' && typeof value === 'string') {
                    return [field, operator, value];
                }
            }
            return clause;
        });
    }
    handleResponse(response) {
        if (Array.isArray(response)) {
            return response.map((record) => this.processV13Record(record));
        }
        return this.processV13Record(response);
    }
    processV13Record(record) {
        if (!record || typeof record !== 'object') {
            return record;
        }
        const processedRecord = { ...record };
        Object.keys(processedRecord).forEach((key) => {
            const value = processedRecord[key];
            if (Array.isArray(value) &&
                value.length === 2 &&
                typeof value[0] === 'number') {
                processedRecord[key] = value[0];
                processedRecord[`${key}_name`] = value[1];
            }
        });
        return processedRecord;
    }
};
exports.OdooV13Adapter = OdooV13Adapter;
exports.OdooV13Adapter = OdooV13Adapter = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], OdooV13Adapter);
//# sourceMappingURL=odoo-v13-adapter.js.map