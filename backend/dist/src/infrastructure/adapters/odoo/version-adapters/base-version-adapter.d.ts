import { IVersionAdapter, OdooCapabilities } from '../../../../shared/domain/repositories/odoo-adapter.interface';
export declare abstract class BaseVersionAdapter implements IVersionAdapter {
    abstract readonly version: string;
    abstract readonly capabilities: OdooCapabilities;
    protected fieldMappings: Record<string, Record<string, string | null>>;
    protected methodMappings: Record<string, string>;
    mapFields(model: string, fields: string[]): string[];
    mapDomain(domain: any[]): any[];
    mapMethod(model: string, method: string): string;
    handleResponse(response: any): any;
    protected setFieldMapping(model: string, mappings: Record<string, string | null>): void;
    protected setMethodMapping(mappings: Record<string, string>): void;
}
