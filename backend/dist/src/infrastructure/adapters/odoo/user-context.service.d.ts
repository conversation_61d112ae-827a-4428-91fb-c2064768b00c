import { Request } from 'express';
import { UserContext } from './odoo-connection-pool.service';
import { OdooConnectionConfig } from '../../../shared/domain/value-objects/odoo-connection-config';
export interface AuthenticatedUser {
    id: string;
    username: string;
    email?: string;
    sessionId?: string;
}
export interface RequestWithUser extends Request {
    user?: AuthenticatedUser;
    sessionId?: string;
    sessionID?: string;
    session?: any;
}
export declare class UserContextService {
    private readonly request;
    private readonly logger;
    private userContext;
    constructor(request: RequestWithUser);
    setUserContext(odooConfig: OdooConnectionConfig): void;
    getUserContext(): UserContext;
    hasUserContext(): boolean;
    getUserId(): string;
    getSessionId(): string;
    updateOdooConfig(odooConfig: OdooConnectionConfig): void;
    private extractUserFromRequest;
    private verifyJWT;
    private decodeJWT;
    private generateSessionId;
    private generateAnonymousUserId;
    getRequestMetadata(): {
        ip: string | undefined;
        userAgent: string | undefined;
        method: string;
        url: string;
        timestamp: string;
    };
}
