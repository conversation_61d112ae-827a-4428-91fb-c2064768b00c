export interface PerformanceMetric {
    operation: string;
    model?: string;
    duration: number;
    timestamp: Date;
    success: boolean;
    error?: string;
}
export interface PerformanceStats {
    totalOperations: number;
    averageDuration: number;
    successRate: number;
    slowestOperation: PerformanceMetric | null;
    fastestOperation: PerformanceMetric | null;
    errorCount: number;
    operationCounts: Record<string, number>;
}
export declare class PerformanceMonitorService {
    private readonly logger;
    private metrics;
    private readonly maxMetrics;
    recordMetric(metric: PerformanceMetric): void;
    getStats(timeWindowMs?: number): PerformanceStats;
    getRecentErrors(count?: number): PerformanceMetric[];
    getSlowestOperations(count?: number): PerformanceMetric[];
    clearMetrics(): void;
    logPerformanceReport(): void;
}
