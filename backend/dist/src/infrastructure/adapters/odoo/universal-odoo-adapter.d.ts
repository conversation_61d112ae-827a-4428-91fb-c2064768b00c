import { IOdooAdapter } from '../../../domain/repositories/odoo-adapter.interface';
import { OdooConnectionConfig, OdooVersionInfo, OdooCapabilities, SearchReadOptions, AuthMethod } from '../../../domain/value-objects/odoo-connection-config';
import { PerformanceMonitorService } from './performance-monitor.service';
import { XmlRpcProtocol } from '../protocols/xmlrpc/xmlrpc-protocol';
import { JsonRpcProtocol } from '../protocols/jsonrpc/jsonrpc-protocol';
import { RestApiProtocol } from '../protocols/rest/rest-protocol';
import { OdooV18Adapter } from './version-adapters/odoo-v18-adapter';
import { OdooV17Adapter } from './version-adapters/odoo-v17-adapter';
import { OdooV15Adapter } from './version-adapters/odoo-v15-adapter';
import { OdooV13Adapter } from './version-adapters/odoo-v13-adapter';
export declare class UniversalOdooAdapter implements IOdooAdapter {
    private readonly xmlRpcProtocol;
    private readonly jsonRpcProtocol;
    private readonly restApiProtocol;
    private readonly odooV18Adapter;
    private readonly odooV17Adapter;
    private readonly odooV15Adapter;
    private readonly odooV13Adapter;
    private readonly performanceMonitor;
    private readonly logger;
    private protocol;
    private versionAdapter;
    private versionInfo;
    private capabilities;
    private config;
    constructor(xmlRpcProtocol: XmlRpcProtocol, jsonRpcProtocol: JsonRpcProtocol, restApiProtocol: RestApiProtocol, odooV18Adapter: OdooV18Adapter, odooV17Adapter: OdooV17Adapter, odooV15Adapter: OdooV15Adapter, odooV13Adapter: OdooV13Adapter, performanceMonitor: PerformanceMonitorService);
    connect(): Promise<void>;
    authenticate(method?: AuthMethod, credentials?: any): Promise<number>;
    searchRead<T = any>(model: string, domain?: any[], options?: SearchReadOptions): Promise<T[]>;
    create<T = any>(model: string, values: Partial<T>): Promise<number>;
    write<T = any>(model: string, ids: number[], values: Partial<T>): Promise<boolean>;
    unlink(model: string, ids: number[]): Promise<boolean>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    getVersionInfo(): OdooVersionInfo | null;
    getCapabilities(): OdooCapabilities | null;
    disconnect(): Promise<void>;
    setConnectionConfig(config: OdooConnectionConfig): void;
    private detectVersion;
    private detectVersionViaXmlRpc;
    private detectVersionViaJsonRpc;
    private detectVersionViaHttp;
    private parseVersionInfo;
    private detectEdition;
    private createVersionAdapter;
    private selectOptimalProtocol;
    private selectBestAuthMethod;
    private detectVersionWithTimeout;
    private connectWithTimeout;
    private validateConnection;
    private sleep;
    private validateModel;
    private validateDomain;
    private enhanceError;
}
