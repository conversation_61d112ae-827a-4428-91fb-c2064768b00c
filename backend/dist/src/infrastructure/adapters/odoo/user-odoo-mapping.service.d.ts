import { Model } from 'mongoose';
import { OdooConnectionConfig } from '../../../domain/value-objects/odoo-connection-config';
import { UserOdooMappingDocument } from '../../database/schemas/user-odoo-mapping.schema';
export interface UserOdooMappingDto {
    webUserId: string;
    odooInstanceId: string;
    odooConfig: OdooConnectionConfig;
    isActive: boolean;
    createdAt: Date;
    lastUsed: Date;
    description?: string;
    tags?: string[];
}
export interface CreateMappingDto {
    webUserId: string;
    odooInstanceId?: string;
    odooConfig: OdooConnectionConfig;
    description?: string;
    tags?: string[];
}
export declare class UserOdooMappingService {
    private readonly userOdooMappingModel;
    private readonly logger;
    private readonly encryptionKey;
    constructor(userOdooMappingModel: Model<UserOdooMappingDocument>);
    createMapping(dto: CreateMappingDto): Promise<UserOdooMappingDto>;
    getUserMappings(webUserId: string): Promise<UserOdooMappingDto[]>;
    getUserMapping(webUserId: string, odooInstanceId?: string): Promise<UserOdooMappingDto | null>;
    updateLastUsed(webUserId: string, odooInstanceId: string): Promise<void>;
    deactivateMapping(webUserId: string, odooInstanceId: string): Promise<boolean>;
    removeUserMappings(webUserId: string): Promise<void>;
    getDecryptedConfig(mapping: UserOdooMappingDto): Promise<OdooConnectionConfig>;
    markConnectionSuccess(webUserId: string, odooInstanceId: string): Promise<void>;
    markConnectionFailure(webUserId: string, odooInstanceId: string, errorMessage: string): Promise<void>;
    private generateInstanceId;
    private encryptPassword;
    private decryptPassword;
    getStats(): Promise<{
        totalUsers: number;
        totalMappings: number;
        activeMappings: number;
        totalUsageCount: number;
        topHosts: Array<{
            host: string;
            count: number;
        }>;
    }>;
    private mapDocumentToDto;
}
