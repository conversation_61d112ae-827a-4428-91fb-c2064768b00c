"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var PerformanceMonitorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMonitorService = void 0;
const common_1 = require("@nestjs/common");
let PerformanceMonitorService = PerformanceMonitorService_1 = class PerformanceMonitorService {
    logger = new common_1.Logger(PerformanceMonitorService_1.name);
    metrics = [];
    maxMetrics = 1000;
    recordMetric(metric) {
        this.metrics.push(metric);
        if (this.metrics.length > this.maxMetrics) {
            this.metrics = this.metrics.slice(-this.maxMetrics);
        }
        if (metric.duration > 5000) {
            this.logger.warn(`Slow operation detected: ${metric.operation}${metric.model ? ` on ${metric.model}` : ''} took ${metric.duration}ms`);
        }
        if (!metric.success && metric.error) {
            this.logger.error(`Operation failed: ${metric.operation}${metric.model ? ` on ${metric.model}` : ''} - ${metric.error}`);
        }
    }
    getStats(timeWindowMs) {
        const now = new Date();
        const cutoff = timeWindowMs ? new Date(now.getTime() - timeWindowMs) : null;
        const relevantMetrics = cutoff
            ? this.metrics.filter(m => m.timestamp >= cutoff)
            : this.metrics;
        if (relevantMetrics.length === 0) {
            return {
                totalOperations: 0,
                averageDuration: 0,
                successRate: 0,
                slowestOperation: null,
                fastestOperation: null,
                errorCount: 0,
                operationCounts: {},
            };
        }
        const successfulMetrics = relevantMetrics.filter(m => m.success);
        const durations = relevantMetrics.map(m => m.duration);
        const operationCounts = {};
        relevantMetrics.forEach(metric => {
            const key = metric.model ? `${metric.operation}:${metric.model}` : metric.operation;
            operationCounts[key] = (operationCounts[key] || 0) + 1;
        });
        return {
            totalOperations: relevantMetrics.length,
            averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
            successRate: (successfulMetrics.length / relevantMetrics.length) * 100,
            slowestOperation: relevantMetrics.reduce((prev, current) => (prev.duration > current.duration) ? prev : current),
            fastestOperation: relevantMetrics.reduce((prev, current) => (prev.duration < current.duration) ? prev : current),
            errorCount: relevantMetrics.filter(m => !m.success).length,
            operationCounts,
        };
    }
    getRecentErrors(count = 10) {
        return this.metrics
            .filter(m => !m.success)
            .slice(-count)
            .reverse();
    }
    getSlowestOperations(count = 10) {
        return [...this.metrics]
            .sort((a, b) => b.duration - a.duration)
            .slice(0, count);
    }
    clearMetrics() {
        this.metrics = [];
        this.logger.log('Performance metrics cleared');
    }
    logPerformanceReport() {
        const stats = this.getStats();
        this.logger.log('=== Performance Report ===');
        this.logger.log(`Total Operations: ${stats.totalOperations}`);
        this.logger.log(`Average Duration: ${stats.averageDuration.toFixed(2)}ms`);
        this.logger.log(`Success Rate: ${stats.successRate.toFixed(2)}%`);
        this.logger.log(`Error Count: ${stats.errorCount}`);
        if (stats.slowestOperation) {
            this.logger.log(`Slowest Operation: ${stats.slowestOperation.operation} (${stats.slowestOperation.duration}ms)`);
        }
        this.logger.log('Operation Counts:');
        Object.entries(stats.operationCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .forEach(([operation, count]) => {
            this.logger.log(`  ${operation}: ${count}`);
        });
    }
};
exports.PerformanceMonitorService = PerformanceMonitorService;
exports.PerformanceMonitorService = PerformanceMonitorService = PerformanceMonitorService_1 = __decorate([
    (0, common_1.Injectable)()
], PerformanceMonitorService);
//# sourceMappingURL=performance-monitor.service.js.map