"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UniversalOdooAdapter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalOdooAdapter = void 0;
const common_1 = require("@nestjs/common");
const odoo_connection_config_1 = require("../../../shared/domain/value-objects/odoo-connection-config");
const performance_monitor_service_1 = require("./performance-monitor.service");
const xmlrpc_protocol_1 = require("../protocols/xmlrpc/xmlrpc-protocol");
const jsonrpc_protocol_1 = require("../protocols/jsonrpc/jsonrpc-protocol");
const rest_protocol_1 = require("../protocols/rest/rest-protocol");
const odoo_v18_adapter_1 = require("./version-adapters/odoo-v18-adapter");
const odoo_v17_adapter_1 = require("./version-adapters/odoo-v17-adapter");
const odoo_v15_adapter_1 = require("./version-adapters/odoo-v15-adapter");
const odoo_v13_adapter_1 = require("./version-adapters/odoo-v13-adapter");
let UniversalOdooAdapter = UniversalOdooAdapter_1 = class UniversalOdooAdapter {
    xmlRpcProtocol;
    jsonRpcProtocol;
    restApiProtocol;
    odooV18Adapter;
    odooV17Adapter;
    odooV15Adapter;
    odooV13Adapter;
    performanceMonitor;
    logger = new common_1.Logger(UniversalOdooAdapter_1.name);
    protocol;
    versionAdapter;
    versionInfo = null;
    capabilities = null;
    config;
    constructor(xmlRpcProtocol, jsonRpcProtocol, restApiProtocol, odooV18Adapter, odooV17Adapter, odooV15Adapter, odooV13Adapter, performanceMonitor) {
        this.xmlRpcProtocol = xmlRpcProtocol;
        this.jsonRpcProtocol = jsonRpcProtocol;
        this.restApiProtocol = restApiProtocol;
        this.odooV18Adapter = odooV18Adapter;
        this.odooV17Adapter = odooV17Adapter;
        this.odooV15Adapter = odooV15Adapter;
        this.odooV13Adapter = odooV13Adapter;
        this.performanceMonitor = performanceMonitor;
    }
    async connect() {
        if (!this.config) {
            throw new Error('Connection config not set. Call setConnectionConfig() first.');
        }
        const startTime = Date.now();
        let retryCount = 0;
        const maxRetries = 3;
        const retryDelay = 1000;
        while (retryCount < maxRetries) {
            try {
                this.logger.log(`Connection attempt ${retryCount + 1}/${maxRetries} to ${this.config.host}`);
                this.versionInfo = await this.detectVersionWithTimeout();
                this.logger.log(`Detected Odoo version: ${this.versionInfo.series} (${this.versionInfo.edition})`);
                this.versionAdapter = this.createVersionAdapter(this.versionInfo);
                this.capabilities = this.versionAdapter.capabilities;
                this.protocol = this.selectOptimalProtocol();
                this.logger.log(`Selected protocol: ${this.protocol.type}`);
                await this.connectWithTimeout(this.protocol, this.config);
                const duration = Date.now() - startTime;
                this.logger.log(`Successfully connected to Odoo in ${duration}ms`);
                return;
            }
            catch (error) {
                retryCount++;
                const isLastAttempt = retryCount >= maxRetries;
                this.logger.warn(`Connection attempt ${retryCount} failed: ${error.message}${isLastAttempt ? '' : `, retrying in ${retryDelay}ms...`}`);
                if (isLastAttempt) {
                    this.logger.error('All connection attempts failed', error);
                    throw new Error(`Connection failed after ${maxRetries} attempts: ${error.message}`);
                }
                await this.sleep(retryDelay * retryCount);
            }
        }
    }
    async authenticate(method, credentials) {
        let authMethod = method || this.selectBestAuthMethod();
        if (this.protocol.type === 'xmlrpc') {
            authMethod = odoo_connection_config_1.AuthMethod.PASSWORD;
        }
        const creds = credentials || {
            database: this.config.database,
            username: this.config.username,
            password: this.config.password,
        };
        try {
            const uid = await this.protocol.authenticate(authMethod, creds);
            this.logger.log(`Authenticated successfully with method: ${authMethod}`);
            return uid;
        }
        catch (error) {
            this.logger.error('Authentication failed', error);
            throw error;
        }
    }
    async searchRead(model, domain = [], options = {}) {
        const startTime = Date.now();
        try {
            this.validateModel(model);
            this.validateDomain(domain);
            const mappedFields = this.versionAdapter.mapFields(model, options.fields || []);
            const mappedDomain = this.versionAdapter.mapDomain(domain);
            const mappedMethod = this.versionAdapter.mapMethod(model, 'search_read');
            const result = await this.protocol.execute(model, mappedMethod, [mappedDomain], {
                fields: mappedFields.length > 0 ? mappedFields : undefined,
                limit: options.limit,
                offset: options.offset,
                order: options.order,
            });
            const duration = Date.now() - startTime;
            const processedResult = this.versionAdapter.handleResponse(result);
            this.performanceMonitor.recordMetric({
                operation: 'searchRead',
                model,
                duration,
                timestamp: new Date(),
                success: true,
            });
            this.logger.debug(`searchRead for ${model} completed in ${duration}ms (${processedResult?.length || 0} records)`);
            return processedResult;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.performanceMonitor.recordMetric({
                operation: 'searchRead',
                model,
                duration,
                timestamp: new Date(),
                success: false,
                error: error.message,
            });
            this.logger.error(`searchRead failed for model ${model} after ${duration}ms`, {
                model,
                domain,
                options,
                error: error.message,
            });
            throw this.enhanceError(error, 'searchRead', model);
        }
    }
    async create(model, values) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, 'create');
            return await this.protocol.execute(model, mappedMethod, [values]);
        }
        catch (error) {
            this.logger.error(`create failed for model ${model}`, error);
            throw error;
        }
    }
    async write(model, ids, values) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, 'write');
            return await this.protocol.execute(model, mappedMethod, [ids, values]);
        }
        catch (error) {
            this.logger.error(`write failed for model ${model}`, error);
            throw error;
        }
    }
    async unlink(model, ids) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, 'unlink');
            return await this.protocol.execute(model, mappedMethod, [ids]);
        }
        catch (error) {
            this.logger.error(`unlink failed for model ${model}`, error);
            throw error;
        }
    }
    async execute(model, method, args, kwargs) {
        try {
            const mappedMethod = this.versionAdapter.mapMethod(model, method);
            const result = await this.protocol.execute(model, mappedMethod, args, kwargs);
            return this.versionAdapter.handleResponse(result);
        }
        catch (error) {
            this.logger.error(`execute failed for ${model}.${method}`, error);
            throw error;
        }
    }
    getVersionInfo() {
        return this.versionInfo;
    }
    getCapabilities() {
        return this.capabilities;
    }
    async disconnect() {
        if (this.protocol) {
            await this.protocol.disconnect();
        }
        this.logger.log('Disconnected from Odoo');
    }
    setConnectionConfig(config) {
        this.config = config;
    }
    async detectVersion() {
        try {
            return await this.detectVersionViaXmlRpc();
        }
        catch (error) {
            try {
                return await this.detectVersionViaJsonRpc();
            }
            catch (error) {
                return await this.detectVersionViaHttp();
            }
        }
    }
    async detectVersionViaXmlRpc() {
        const xmlrpc = require('xmlrpc');
        const port = this.config.port || (this.config.protocol === 'https' ? 443 : 80);
        const cleanHost = this.config.host.replace(/^https?:\/\//, '');
        const client = xmlrpc.createClient({
            host: cleanHost,
            port,
            path: '/xmlrpc/2/common',
        });
        return new Promise((resolve, reject) => {
            client.methodCall('version', [], (error, value) => {
                if (error) {
                    reject(error);
                }
                else {
                    resolve(this.parseVersionInfo(value));
                }
            });
        });
    }
    async detectVersionViaJsonRpc() {
        let baseUrl = this.config.host;
        if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
            baseUrl = `https://${baseUrl}`;
        }
        baseUrl = baseUrl.replace(/\/$/, '');
        const response = await fetch(`${baseUrl}/jsonrpc`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                jsonrpc: '2.0',
                method: 'call',
                params: {
                    service: 'common',
                    method: 'version',
                    args: []
                },
                id: 1
            }),
        });
        const result = await response.json();
        return this.parseVersionInfo(result.result);
    }
    async detectVersionViaHttp() {
        let baseUrl = this.config.host;
        if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
            baseUrl = `https://${baseUrl}`;
        }
        baseUrl = baseUrl.replace(/\/$/, '');
        const response = await fetch(`${baseUrl}/web/webclient/version_info`);
        const result = await response.json();
        return this.parseVersionInfo(result);
    }
    parseVersionInfo(versionData) {
        const version = versionData.server_version || versionData.version;
        const cleanVersion = version.replace(/[^0-9.]/g, '');
        const versionParts = cleanVersion.split('.');
        const major = parseInt(versionParts[0]) || 0;
        const minor = parseInt(versionParts[1]) || 0;
        const patch = parseInt(versionParts[2]) || 0;
        return {
            major,
            minor,
            patch,
            series: `${major}.${minor}`,
            edition: this.detectEdition(versionData),
            serverVersion: version,
            protocolVersion: versionData.protocol_version || 1,
        };
    }
    detectEdition(versionData) {
        const version = versionData.server_version || versionData.version || '';
        return version.includes('e') || versionData.enterprise
            ? 'enterprise'
            : 'community';
    }
    createVersionAdapter(version) {
        if (version.major >= 18)
            return this.odooV18Adapter;
        if (version.major >= 17)
            return this.odooV17Adapter;
        if (version.major >= 15)
            return this.odooV15Adapter;
        if (version.major >= 13)
            return this.odooV13Adapter;
        throw new Error(`Unsupported Odoo version: ${version.series}`);
    }
    selectOptimalProtocol() {
        const preferredProtocol = this.config.preferredProtocol;
        if (preferredProtocol === 'rest' && this.capabilities?.hasRestApi) {
            this.logger.debug('Using REST API as preferred protocol');
            return this.restApiProtocol;
        }
        if (preferredProtocol === 'jsonrpc' && this.capabilities?.hasJsonRpc) {
            this.logger.debug('Using JSON-RPC as preferred protocol');
            return this.jsonRpcProtocol;
        }
        if (preferredProtocol === 'xmlrpc') {
            this.logger.debug('Using XML-RPC as preferred protocol');
            return this.xmlRpcProtocol;
        }
        if (this.capabilities?.hasRestApi && this.versionInfo && this.versionInfo.major >= 17) {
            this.logger.debug('Auto-selected REST API for modern Odoo version');
            return this.restApiProtocol;
        }
        if (this.capabilities?.hasJsonRpc) {
            this.logger.debug('Auto-selected JSON-RPC');
            return this.jsonRpcProtocol;
        }
        this.logger.debug('Fallback to XML-RPC');
        return this.xmlRpcProtocol;
    }
    selectBestAuthMethod() {
        const supported = this.capabilities?.supportedAuthMethods || [];
        if (supported.includes(odoo_connection_config_1.AuthMethod.API_KEY))
            return odoo_connection_config_1.AuthMethod.API_KEY;
        if (supported.includes(odoo_connection_config_1.AuthMethod.OAUTH2))
            return odoo_connection_config_1.AuthMethod.OAUTH2;
        if (supported.includes(odoo_connection_config_1.AuthMethod.TOKEN))
            return odoo_connection_config_1.AuthMethod.TOKEN;
        return odoo_connection_config_1.AuthMethod.PASSWORD;
    }
    async detectVersionWithTimeout() {
        const timeout = 10000;
        return Promise.race([
            this.detectVersion(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Version detection timeout')), timeout))
        ]);
    }
    async connectWithTimeout(protocol, config) {
        const timeout = 15000;
        return Promise.race([
            protocol.connect(config),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Protocol connection timeout')), timeout))
        ]);
    }
    async validateConnection() {
        try {
            await this.searchRead('res.users', [['id', '=', 1]], { limit: 1 });
            this.logger.debug('Connection validation successful');
        }
        catch (error) {
            throw new Error(`Connection validation failed: ${error.message}`);
        }
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    validateModel(model) {
        if (!model || typeof model !== 'string') {
            throw new Error('Model name is required and must be a string');
        }
        if (!/^[a-z][a-z0-9_.]*$/.test(model)) {
            throw new Error(`Invalid model name format: ${model}`);
        }
    }
    validateDomain(domain) {
        if (!Array.isArray(domain)) {
            throw new Error('Domain must be an array');
        }
    }
    enhanceError(error, operation, model) {
        const enhancedMessage = `${operation} operation failed${model ? ` for model ${model}` : ''}`;
        if (error.message?.includes('AccessError')) {
            return new Error(`${enhancedMessage}: Access denied. Check user permissions for ${model}`);
        }
        if (error.message?.includes('ValidationError')) {
            return new Error(`${enhancedMessage}: Data validation failed. ${error.message}`);
        }
        if (error.message?.includes('MissingError')) {
            return new Error(`${enhancedMessage}: Record not found or has been deleted`);
        }
        if (error.message?.includes('UserError')) {
            return new Error(`${enhancedMessage}: ${error.message}`);
        }
        if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
            return new Error(`${enhancedMessage}: Cannot connect to Odoo server. Check host and port configuration`);
        }
        if (error.code === 'ETIMEDOUT') {
            return new Error(`${enhancedMessage}: Connection timeout. Server may be overloaded`);
        }
        const enhancedError = new Error(`${enhancedMessage}: ${error.message}`);
        enhancedError.stack = error.stack;
        return enhancedError;
    }
};
exports.UniversalOdooAdapter = UniversalOdooAdapter;
exports.UniversalOdooAdapter = UniversalOdooAdapter = UniversalOdooAdapter_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [xmlrpc_protocol_1.XmlRpcProtocol,
        jsonrpc_protocol_1.JsonRpcProtocol,
        rest_protocol_1.RestApiProtocol,
        odoo_v18_adapter_1.OdooV18Adapter,
        odoo_v17_adapter_1.OdooV17Adapter,
        odoo_v15_adapter_1.OdooV15Adapter,
        odoo_v13_adapter_1.OdooV13Adapter,
        performance_monitor_service_1.PerformanceMonitorService])
], UniversalOdooAdapter);
//# sourceMappingURL=universal-odoo-adapter.js.map