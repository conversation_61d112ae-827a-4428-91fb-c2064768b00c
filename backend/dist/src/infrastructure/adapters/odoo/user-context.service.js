"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var UserContextService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserContextService = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
let UserContextService = UserContextService_1 = class UserContextService {
    request;
    logger = new common_1.Logger(UserContextService_1.name);
    userContext = null;
    constructor(request) {
        this.request = request;
    }
    setUserContext(odooConfig) {
        const baseUser = this.extractUserFromRequest();
        const uniqueUserId = this.generateUniqueUserId(baseUser.id, odooConfig);
        this.userContext = {
            userId: uniqueUserId,
            sessionId: baseUser.sessionId || this.generateSessionId(),
            odooConfig,
        };
        this.logger.debug(`User context set for user: ${uniqueUserId} (base: ${baseUser.id})`);
    }
    getUserContext() {
        if (!this.userContext) {
            throw new Error('User context not set. Call setUserContext() first.');
        }
        return this.userContext;
    }
    hasUserContext() {
        return this.userContext !== null;
    }
    getUserId() {
        const context = this.getUserContext();
        return context.userId;
    }
    getSessionId() {
        const context = this.getUserContext();
        return context.sessionId;
    }
    updateOdooConfig(odooConfig) {
        if (!this.userContext) {
            throw new Error('User context not set. Call setUserContext() first.');
        }
        this.userContext.odooConfig = odooConfig;
        this.logger.debug(`Updated Odoo config for user: ${this.userContext.userId}`);
    }
    extractUserFromRequest() {
        if (this.request.user) {
            const user = this.request.user;
            this.logger.debug(`Passport JWT authenticated user: ${user.id}`);
            return {
                id: user.id,
                username: user.username,
                email: user.email,
                sessionId: user.sessionId || this.generateSessionId(),
            };
        }
        const authHeader = this.request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            try {
                const token = authHeader.substring(7);
                const decoded = this.verifyJWT(token);
                this.logger.debug(`Manual JWT authenticated user: ${decoded.sub || decoded.userId}`);
                return {
                    id: decoded.sub || decoded.userId,
                    username: decoded.odooUsername || decoded.username,
                    email: decoded.email,
                    sessionId: decoded.sessionId || this.generateSessionId(),
                };
            }
            catch (error) {
                this.logger.error('JWT verification failed', error);
                throw new Error('Invalid or expired JWT token');
            }
        }
        if (this.request.user) {
            const user = this.request.user;
            this.logger.debug(`Passport authenticated user: ${user.id}`);
            return {
                id: user.id,
                username: user.username,
                email: user.email,
                sessionId: user.sessionId || this.request.sessionId || this.generateSessionId(),
            };
        }
        if (this.request.session && this.request.session.user) {
            const sessionUser = this.request.session.user;
            this.logger.debug(`Session authenticated user: ${sessionUser.id}`);
            return {
                id: sessionUser.id,
                username: sessionUser.username,
                email: sessionUser.email,
                sessionId: this.request.sessionID || this.request.sessionId,
            };
        }
        const userId = this.request.headers['x-user-id'];
        const username = this.request.headers['x-username'];
        if (userId && username) {
            this.logger.debug(`Header authenticated user: ${userId}`);
            return {
                id: userId,
                username: username,
                sessionId: this.generateSessionId(),
            };
        }
        if (process.env.NODE_ENV === 'development') {
            const anonymousId = this.generateAnonymousUserId();
            this.logger.warn(`No authenticated user found, using anonymous user: ${anonymousId}`);
            return {
                id: anonymousId,
                username: 'anonymous',
                sessionId: this.generateSessionId(),
            };
        }
        throw new Error('Authentication required. Please provide valid JWT token or login credentials.');
    }
    verifyJWT(token) {
        try {
            const parts = token.split('.');
            if (parts.length !== 3) {
                throw new Error('Invalid JWT format');
            }
            const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString('utf-8'));
            if (!payload.sub && !payload.userId) {
                throw new Error('JWT missing user identifier');
            }
            if (payload.exp && Date.now() >= payload.exp * 1000) {
                throw new Error('JWT token expired');
            }
            return payload;
        }
        catch (error) {
            throw new Error(`JWT verification failed: ${error.message}`);
        }
    }
    decodeJWT(token) {
        try {
            const payload = token.split('.')[1];
            const decoded = Buffer.from(payload, 'base64').toString('utf-8');
            return JSON.parse(decoded);
        }
        catch (error) {
            throw new Error('Invalid JWT token');
        }
    }
    generateSessionId() {
        return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateUniqueUserId(baseUserId, odooConfig) {
        const odooIdentifier = `${odooConfig.host}:${odooConfig.database}:${odooConfig.username}`;
        const hash = Buffer.from(odooIdentifier).toString('base64').substr(0, 8);
        return `${baseUserId}_${hash}`;
    }
    generateAnonymousUserId() {
        const ip = this.request.ip || this.request.connection.remoteAddress || 'unknown';
        const userAgent = this.request.headers['user-agent'] || 'unknown';
        const sessionData = this.request.sessionID ||
            this.request.headers['x-session-id'] ||
            Date.now().toString();
        const hash = Buffer.from(`${ip}:${userAgent}:${sessionData}`).toString('base64').substr(0, 12);
        return `anon_${hash}`;
    }
    getRequestMetadata() {
        return {
            ip: this.request.ip,
            userAgent: this.request.headers['user-agent'],
            method: this.request.method,
            url: this.request.url,
            timestamp: new Date().toISOString(),
        };
    }
};
exports.UserContextService = UserContextService;
exports.UserContextService = UserContextService = UserContextService_1 = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.REQUEST }),
    __param(0, (0, common_1.Inject)(core_1.REQUEST)),
    __metadata("design:paramtypes", [Object])
], UserContextService);
//# sourceMappingURL=user-context.service.js.map