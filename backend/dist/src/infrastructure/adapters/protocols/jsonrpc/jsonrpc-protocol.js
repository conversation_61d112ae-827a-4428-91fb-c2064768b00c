"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var JsonRpcProtocol_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonRpcProtocol = void 0;
const common_1 = require("@nestjs/common");
const odoo_connection_config_1 = require("../../../../shared/domain/value-objects/odoo-connection-config");
let JsonRpcProtocol = JsonRpcProtocol_1 = class JsonRpcProtocol {
    logger = new common_1.Logger(JsonRpcProtocol_1.name);
    type = odoo_connection_config_1.ProtocolType.JSONRPC;
    supportedMethods = ['execute', 'execute_kw'];
    sessionId = null;
    baseUrl;
    config;
    uid;
    async connect(config) {
        this.config = config;
        let baseUrl = config.host;
        if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
            baseUrl = `https://${baseUrl}`;
        }
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.logger.log(`Connected to Odoo JSON-RPC at ${this.baseUrl}`);
    }
    async authenticate(method, credentials) {
        try {
            const response = await fetch(`${this.baseUrl}/web/session/authenticate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        db: credentials.database || this.config.database,
                        login: credentials.username || this.config.username,
                        password: credentials.password || this.config.password,
                    },
                }),
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await response.json();
            if (result.error) {
                throw new Error(result.error.message || 'Authentication failed');
            }
            if (!result.result || !result.result.uid) {
                throw new Error('Invalid credentials');
            }
            this.logger.debug('Authentication result:', JSON.stringify(result.result, null, 2));
            this.sessionId = result.result.session_id || result.result.sessionId;
            if (!this.sessionId) {
                const setCookieHeader = response.headers.get('set-cookie');
                if (setCookieHeader) {
                    const sessionMatch = setCookieHeader.match(/session_id=([^;]+)/);
                    if (sessionMatch) {
                        this.sessionId = sessionMatch[1];
                    }
                }
            }
            this.uid = result.result.uid;
            this.logger.log(`Authenticated via JSON-RPC as user ID: ${this.uid}`);
            this.logger.debug(`Session ID: ${this.sessionId}`);
            this.logger.debug(`Set-Cookie header: ${response.headers.get('set-cookie')}`);
            if (!this.sessionId) {
                this.logger.warn('No session ID found in response or cookies');
            }
            return this.uid;
        }
        catch (error) {
            this.logger.error('JSON-RPC authentication failed', error);
            throw new Error(`Authentication failed: ${error.message}`);
        }
    }
    async execute(model, method, args, kwargs) {
        if (!this.sessionId) {
            throw new Error('Not authenticated. Call authenticate() first.');
        }
        try {
            const response = await fetch(`${this.baseUrl}/jsonrpc`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Cookie: `session_id=${this.sessionId}`,
                },
                body: JSON.stringify({
                    jsonrpc: '2.0',
                    method: 'call',
                    params: {
                        service: 'object',
                        method: 'execute_kw',
                        args: [
                            this.config.database,
                            this.uid,
                            this.config.password,
                            model,
                            method,
                            args,
                            kwargs || {}
                        ],
                    },
                }),
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await response.json();
            if (result.error) {
                this.logger.error('JSON-RPC execute error:', JSON.stringify(result.error, null, 2));
                throw new Error(result.error.message || result.error.data?.message || 'Execute failed');
            }
            return result.result;
        }
        catch (error) {
            this.logger.error(`JSON-RPC execute failed for ${model}.${method}`, error);
            throw new Error(`Execute failed: ${error.message}`);
        }
    }
    async disconnect() {
        this.sessionId = null;
        this.logger.log('Disconnected from JSON-RPC');
    }
};
exports.JsonRpcProtocol = JsonRpcProtocol;
exports.JsonRpcProtocol = JsonRpcProtocol = JsonRpcProtocol_1 = __decorate([
    (0, common_1.Injectable)()
], JsonRpcProtocol);
//# sourceMappingURL=jsonrpc-protocol.js.map