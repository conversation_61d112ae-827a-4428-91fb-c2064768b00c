import { IOdooProtocol } from '../../../../shared/domain/repositories/odoo-adapter.interface';
import { OdooConnectionConfig, ProtocolType, AuthMethod } from '../../../../shared/domain/value-objects/odoo-connection-config';
export declare class RestApiProtocol implements IOdooProtocol {
    private readonly logger;
    readonly type = ProtocolType.REST;
    readonly supportedMethods: string[];
    private apiKey;
    private baseUrl;
    private config;
    connect(config: OdooConnectionConfig): Promise<void>;
    authenticate(method: AuthMethod, credentials: any): Promise<number>;
    execute(model: string, method: string, args: any[], kwargs?: any): Promise<any>;
    private buildRestUrl;
    private mapToHttpMethod;
    disconnect(): Promise<void>;
}
