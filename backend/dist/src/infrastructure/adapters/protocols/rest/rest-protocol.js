"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var RestApiProtocol_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RestApiProtocol = void 0;
const common_1 = require("@nestjs/common");
const odoo_connection_config_1 = require("../../../../shared/domain/value-objects/odoo-connection-config");
let RestApiProtocol = RestApiProtocol_1 = class RestApiProtocol {
    logger = new common_1.Logger(RestApiProtocol_1.name);
    type = odoo_connection_config_1.ProtocolType.REST;
    supportedMethods = ['GET', 'POST', 'PUT', 'DELETE'];
    apiKey = null;
    baseUrl;
    config;
    async connect(config) {
        this.config = config;
        const port = config.port || (config.protocol === 'https' ? 443 : 80);
        const cleanHost = config.host.replace(/^https?:\/\//, '');
        this.baseUrl = `${config.protocol}://${cleanHost}:${port}/api/v1`;
        this.logger.log(`Connected to Odoo REST API at ${this.baseUrl}`);
    }
    async authenticate(method, credentials) {
        if (method === odoo_connection_config_1.AuthMethod.API_KEY) {
            this.apiKey = credentials.apiKey;
            this.logger.log('Authenticated using API key');
            return credentials.uid;
        }
        try {
            const response = await fetch(`${this.baseUrl}/auth/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    database: credentials.database || this.config.database,
                    username: credentials.username || this.config.username,
                    password: credentials.password || this.config.password,
                }),
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const result = await response.json();
            if (result.error) {
                throw new Error(result.error);
            }
            this.apiKey = result.api_key;
            const uid = result.uid;
            this.logger.log(`Authenticated via REST API as user ID: ${uid}`);
            return uid;
        }
        catch (error) {
            this.logger.error('REST API authentication failed', error);
            throw new Error(`Authentication failed: ${error.message}`);
        }
    }
    async execute(model, method, args, kwargs) {
        if (!this.apiKey) {
            throw new Error('Not authenticated. Call authenticate() first.');
        }
        try {
            const url = this.buildRestUrl(model, method, args, kwargs);
            const httpMethod = this.mapToHttpMethod(method);
            const response = await fetch(url, {
                method: httpMethod,
                headers: {
                    Authorization: `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: httpMethod !== 'GET' ? JSON.stringify(kwargs || {}) : undefined,
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        }
        catch (error) {
            this.logger.error(`REST API execute failed for ${model}.${method}`, error);
            throw new Error(`Execute failed: ${error.message}`);
        }
    }
    buildRestUrl(model, method, args, kwargs) {
        const modelPath = model.replace('.', '/');
        let url = `${this.baseUrl}/${modelPath}`;
        if (method === 'search_read') {
            const params = new URLSearchParams();
            if (kwargs?.domain)
                params.append('domain', JSON.stringify(kwargs.domain));
            if (kwargs?.fields)
                params.append('fields', kwargs.fields.join(','));
            if (kwargs?.limit)
                params.append('limit', kwargs.limit.toString());
            if (kwargs?.offset)
                params.append('offset', kwargs.offset.toString());
            if (kwargs?.order)
                params.append('order', kwargs.order);
            return `${url}?${params.toString()}`;
        }
        if (method === 'write' && args.length > 0) {
            url += `/${args[0].join(',')}`;
        }
        if (method === 'unlink' && args.length > 0) {
            url += `/${args[0].join(',')}`;
        }
        return url;
    }
    mapToHttpMethod(method) {
        const methodMap = {
            search_read: 'GET',
            create: 'POST',
            write: 'PUT',
            unlink: 'DELETE',
        };
        return methodMap[method] || 'POST';
    }
    async disconnect() {
        this.apiKey = null;
        this.logger.log('Disconnected from REST API');
    }
};
exports.RestApiProtocol = RestApiProtocol;
exports.RestApiProtocol = RestApiProtocol = RestApiProtocol_1 = __decorate([
    (0, common_1.Injectable)()
], RestApiProtocol);
//# sourceMappingURL=rest-protocol.js.map