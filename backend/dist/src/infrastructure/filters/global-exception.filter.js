"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GlobalExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const response_builder_service_1 = require("../../common/services/response-builder.service");
let GlobalExceptionFilter = GlobalExceptionFilter_1 = class GlobalExceptionFilter {
    responseBuilder;
    logger = new common_1.Logger(GlobalExceptionFilter_1.name);
    constructor(responseBuilder) {
        this.responseBuilder = responseBuilder;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        let errorResponse;
        if (exception instanceof common_1.HttpException) {
            const status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
                const responseObj = exceptionResponse;
                const message = responseObj.message || exception.message;
                const errorType = responseObj.error || this.getErrorTypeFromStatus(status);
                const validationErrors = this.extractValidationErrors(responseObj);
                errorResponse = this.responseBuilder.error(errorType, message, status, request, responseObj.errorCode, validationErrors, { originalException: responseObj });
            }
            else {
                errorResponse = this.responseBuilder.error(this.getErrorTypeFromStatus(status), exceptionResponse, status, request);
            }
        }
        else if (exception instanceof Error) {
            errorResponse = this.responseBuilder.internalError(exception.message, request, 'INTERNAL_ERROR', {
                errorName: exception.name,
                stack: exception.stack,
            });
        }
        else {
            errorResponse = this.responseBuilder.internalError('Unknown error occurred', request, 'UNKNOWN_ERROR', { exception });
        }
        this.logger.error(`${request.method} ${request.url} - ${errorResponse.statusCode} ${errorResponse.error}: ${errorResponse.message}`, exception instanceof Error ? exception.stack : exception);
        response.status(errorResponse.statusCode).json(errorResponse);
    }
    getErrorTypeFromStatus(status) {
        switch (status) {
            case 400: return 'Bad Request';
            case 401: return 'Unauthorized';
            case 403: return 'Forbidden';
            case 404: return 'Not Found';
            case 409: return 'Conflict';
            case 422: return 'Unprocessable Entity';
            case 429: return 'Too Many Requests';
            case 500: return 'Internal Server Error';
            case 502: return 'Bad Gateway';
            case 503: return 'Service Unavailable';
            case 504: return 'Gateway Timeout';
            default: return 'HTTP Error';
        }
    }
    extractValidationErrors(responseObj) {
        if (responseObj.validationErrors && Array.isArray(responseObj.validationErrors)) {
            return responseObj.validationErrors;
        }
        if (responseObj.message && Array.isArray(responseObj.message)) {
            return responseObj.message.map((msg) => ({
                field: msg.property || 'unknown',
                message: Object.values(msg.constraints || {}).join(', ') || msg,
                value: msg.value,
                rule: Object.keys(msg.constraints || {})[0],
            }));
        }
        return undefined;
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = GlobalExceptionFilter_1 = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [response_builder_service_1.ResponseBuilderService])
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map