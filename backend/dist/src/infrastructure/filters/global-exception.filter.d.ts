import { ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { ResponseBuilderService } from '../../common/services/response-builder.service';
export declare class GlobalExceptionFilter implements ExceptionFilter {
    private readonly responseBuilder;
    private readonly logger;
    constructor(responseBuilder: ResponseBuilderService);
    catch(exception: unknown, host: ArgumentsHost): void;
    private getErrorTypeFromStatus;
    private extractValidationErrors;
}
