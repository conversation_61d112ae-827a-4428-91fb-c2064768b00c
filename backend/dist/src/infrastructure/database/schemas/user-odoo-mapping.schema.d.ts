import { Document } from 'mongoose';
export type UserOdooMappingDocument = UserOdooMapping & Document;
export declare class UserOdooMapping {
    webUserId: string;
    odooInstanceId: string;
    odooHost: string;
    odooDatabase: string;
    odooUsername: string;
    odooPasswordEncrypted: string;
    odooProtocol: string;
    odooPort: number;
    isActive: boolean;
    lastUsed: Date;
    lastConnectionError?: string;
    connectionAttempts: number;
    lastSuccessfulConnection?: Date;
    totalUsageCount: number;
    tags?: string[];
    description?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const UserOdooMappingSchema: import("mongoose").Schema<UserOdooMapping, import("mongoose").Model<UserOdooMapping, any, any, any, Document<unknown, any, UserOdooMapping, any> & UserOdooMapping & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, UserOdooMapping, Document<unknown, {}, import("mongoose").FlatRecord<UserOdooMapping>, {}> & import("mongoose").FlatRecord<UserOdooMapping> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
