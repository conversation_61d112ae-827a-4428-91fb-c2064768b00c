"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOdooMappingSchema = exports.UserOdooMapping = void 0;
const mongoose_1 = require("@nestjs/mongoose");
let UserOdooMapping = class UserOdooMapping {
    webUserId;
    odooInstanceId;
    odooHost;
    odooDatabase;
    odooUsername;
    odooPasswordEncrypted;
    odooProtocol;
    odooPort;
    isActive;
    lastUsed;
    lastConnectionError;
    connectionAttempts;
    lastSuccessfulConnection;
    totalUsageCount;
    tags;
    description;
    createdAt;
    updatedAt;
};
exports.UserOdooMapping = UserOdooMapping;
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "webUserId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooInstanceId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooHost", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooDatabase", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooUsername", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooPasswordEncrypted", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 'https' }),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "odooProtocol", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 443 }),
    __metadata("design:type", Number)
], UserOdooMapping.prototype, "odooPort", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], UserOdooMapping.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], UserOdooMapping.prototype, "lastUsed", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "lastConnectionError", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], UserOdooMapping.prototype, "connectionAttempts", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], UserOdooMapping.prototype, "lastSuccessfulConnection", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], UserOdooMapping.prototype, "totalUsageCount", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Array)
], UserOdooMapping.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UserOdooMapping.prototype, "description", void 0);
exports.UserOdooMapping = UserOdooMapping = __decorate([
    (0, mongoose_1.Schema)({
        collection: 'user_odoo_mappings',
        timestamps: true,
    })
], UserOdooMapping);
exports.UserOdooMappingSchema = mongoose_1.SchemaFactory.createForClass(UserOdooMapping);
exports.UserOdooMappingSchema.index({ webUserId: 1, odooInstanceId: 1 }, { unique: true });
exports.UserOdooMappingSchema.index({ webUserId: 1, isActive: 1 });
exports.UserOdooMappingSchema.index({ webUserId: 1, lastUsed: -1 });
exports.UserOdooMappingSchema.index({ odooHost: 1, odooDatabase: 1, odooUsername: 1 });
exports.UserOdooMappingSchema.virtual('odooConfig').get(function () {
    return {
        host: this.odooHost,
        database: this.odooDatabase,
        username: this.odooUsername,
        password: this.odooPasswordEncrypted,
        protocol: this.odooProtocol,
        port: this.odooPort,
    };
});
exports.UserOdooMappingSchema.methods.updateLastUsed = function () {
    this.lastUsed = new Date();
    this.totalUsageCount += 1;
    return this.save();
};
exports.UserOdooMappingSchema.methods.markConnectionSuccess = function () {
    this.lastSuccessfulConnection = new Date();
    this.connectionAttempts = 0;
    this.lastConnectionError = undefined;
    return this.save();
};
exports.UserOdooMappingSchema.methods.markConnectionFailure = function (error) {
    this.connectionAttempts += 1;
    this.lastConnectionError = error;
    return this.save();
};
//# sourceMappingURL=user-odoo-mapping.schema.js.map