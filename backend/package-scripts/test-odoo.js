#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting Odoo Connection Test...\n');

try {
  // Change to backend directory
  process.chdir(path.join(__dirname, '..'));
  
  // Run the TypeScript test file
  const command = 'npx ts-node test-odoo-connection.ts';
  
  console.log(`Executing: ${command}\n`);
  
  execSync(command, { 
    stdio: 'inherit',
    env: { 
      ...process.env,
      NODE_ENV: 'development'
    }
  });
  
} catch (error) {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
}
